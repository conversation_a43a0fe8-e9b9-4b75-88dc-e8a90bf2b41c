<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.joyadata.apps</groupId>
  <artifactId>datasource</artifactId>
  <name>Data Source Hub</name>
  <description>数据源中心</description>
  <packaging>jar</packaging>
  <properties>
    <app.name>dedp-dms-dsc</app.name>
    <app.version>1.0.0</app.version>
    <app.port>18199</app.port>
    <app.main.class>com.joyadata.dsc.DscApp</app.main.class>
    <app.plugins>datasourceX</app.plugins>
  </properties>
  <parent>
    <groupId>com.joyadata.cloud</groupId>
    <artifactId>parent</artifactId>
    <version>1.0.1</version>
  </parent>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dtstack.dtcenter</groupId>
      <artifactId>common.loader.common</artifactId>
      <version>2.0.0-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>com.dtstack.dtcenter</groupId>
      <artifactId>common.loader.core</artifactId>
      <version>2.0.0-RELEASE</version>
    </dependency>

    <dependency>
      <groupId>com.joyadata.apps</groupId>
      <artifactId>business.engine.beans</artifactId>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler</groupId>
      <artifactId>quartz</artifactId>
    </dependency>

    <!-- 导出-->
    <!-- https://mvnrepository.com/artifact/com.alibaba/easyexcel -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>4.0.3</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

  </dependencies>
</project>
