# Hadoop配置文件上传指南

## 概述

除了Kerberos认证文件外，Hive数据源还需要Hadoop相关的配置文件来正确连接和访问Hadoop集群。本文档介绍如何上传和配置这些文件。

## 支持的Hadoop配置文件

### 1. hive-site.xml
- **用途**: Hive服务配置文件
- **包含内容**: Hive MetaStore连接信息、Hive服务器配置等
- **必需性**: 通常必需

### 2. hdfs-site.xml  
- **用途**: HDFS配置文件
- **包含内容**: HDFS NameNode地址、副本数量、块大小等配置
- **必需性**: 访问HDFS时必需

### 3. core-site.xml
- **用途**: Hadoop核心配置文件
- **包含内容**: 默认文件系统、临时目录、安全配置等
- **必需性**: 基础配置，通常必需

## 文件上传

### 单个文件上传接口

**接口**: `POST /datasourceInfo/kerberos/upload/single`

**支持的fileType值**:
- `hivesite`: 对应 hive-site.xml
- `hdfssite`: 对应 hdfs-site.xml  
- `coresite`: 对应 core-site.xml

### 上传示例

```bash
# 上传hive-site.xml
curl -X POST \
  http://localhost:8080/datasourceInfo/kerberos/upload/single \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@hive-site.xml' \
  -F 'fileType=hivesite'

# 上传hdfs-site.xml
curl -X POST \
  http://localhost:8080/datasourceInfo/kerberos/upload/single \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@hdfs-site.xml' \
  -F 'fileType=hdfssite'

# 上传core-site.xml
curl -X POST \
  http://localhost:8080/datasourceInfo/kerberos/upload/single \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@core-site.xml' \
  -F 'fileType=coresite'
```

## 前端集成示例

### JavaScript上传函数

```javascript
// 上传Hadoop配置文件
async function uploadHadoopConfigFiles(hiveSiteFile, hdfsSiteFile, coreSiteFile) {
  const fileIds = {};
  
  try {
    // 上传hive-site.xml
    if (hiveSiteFile) {
      fileIds.hivesite = await uploadSingleKerberosFile(hiveSiteFile, 'hivesite');
      console.log('hive-site.xml上传成功:', fileIds.hivesite);
    }
    
    // 上传hdfs-site.xml
    if (hdfsSiteFile) {
      fileIds.hdfssite = await uploadSingleKerberosFile(hdfsSiteFile, 'hdfssite');
      console.log('hdfs-site.xml上传成功:', fileIds.hdfssite);
    }
    
    // 上传core-site.xml
    if (coreSiteFile) {
      fileIds.coresite = await uploadSingleKerberosFile(coreSiteFile, 'coresite');
      console.log('core-site.xml上传成功:', fileIds.coresite);
    }
    
    return fileIds;
  } catch (error) {
    console.error('Hadoop配置文件上传失败:', error);
    throw error;
  }
}
```

### React组件示例

```jsx
const HadoopConfigUpload = ({ onFileUploaded }) => {
  const [uploading, setUploading] = useState({});
  const [fileIds, setFileIds] = useState({});
  const [errors, setErrors] = useState({});

  const handleFileUpload = async (file, fileType) => {
    if (!file) return;
    
    setUploading(prev => ({ ...prev, [fileType]: true }));
    setErrors(prev => ({ ...prev, [fileType]: null }));
    
    try {
      const fileId = await uploadSingleKerberosFile(file, fileType);
      
      setFileIds(prev => {
        const newFileIds = { ...prev, [fileType]: fileId };
        onFileUploaded && onFileUploaded(newFileIds);
        return newFileIds;
      });
      
    } catch (error) {
      setErrors(prev => ({ ...prev, [fileType]: error.message }));
    } finally {
      setUploading(prev => ({ ...prev, [fileType]: false }));
    }
  };

  return (
    <div className="hadoop-config-upload">
      <h3>Hadoop配置文件上传</h3>
      
      {/* hive-site.xml文件上传 */}
      <div className="file-upload-item">
        <label>hive-site.xml (推荐):</label>
        <input
          type="file"
          accept=".xml"
          onChange={(e) => handleFileUpload(e.target.files[0], 'hivesite')}
          disabled={uploading.hivesite}
        />
        {uploading.hivesite && <span>上传中...</span>}
        {fileIds.hivesite && <span className="success">✓ 已上传</span>}
        {errors.hivesite && <span className="error">✗ {errors.hivesite}</span>}
      </div>
      
      {/* hdfs-site.xml文件上传 */}
      <div className="file-upload-item">
        <label>hdfs-site.xml (推荐):</label>
        <input
          type="file"
          accept=".xml"
          onChange={(e) => handleFileUpload(e.target.files[0], 'hdfssite')}
          disabled={uploading.hdfssite}
        />
        {uploading.hdfssite && <span>上传中...</span>}
        {fileIds.hdfssite && <span className="success">✓ 已上传</span>}
        {errors.hdfssite && <span className="error">✗ {errors.hdfssite}</span>}
      </div>
      
      {/* core-site.xml文件上传 */}
      <div className="file-upload-item">
        <label>core-site.xml (推荐):</label>
        <input
          type="file"
          accept=".xml"
          onChange={(e) => handleFileUpload(e.target.files[0], 'coresite')}
          disabled={uploading.coresite}
        />
        {uploading.coresite && <span>上传中...</span>}
        {fileIds.coresite && <span className="success">✓ 已上传</span>}
        {errors.coresite && <span className="error">✗ {errors.coresite}</span>}
      </div>
    </div>
  );
};
```

## 数据源配置

### 配置接口

**接口**: `POST /datasourceInfo/kerberos/configure/{datasourceId}`

**新增参数**:
- `hiveSiteFileId`: hive-site.xml文件ID（可选）
- `hdfsSiteFileId`: hdfs-site.xml文件ID（可选）
- `coreSiteFileId`: core-site.xml文件ID（可选）

### 配置示例

```javascript
// 完整的数据源配置
async function configureDatasourceWithAllFiles() {
  try {
    // 1. 上传Kerberos文件
    const kerberosFileIds = await uploadKerberosFiles(keytabFile, krb5ConfFile, jaasConfFile);
    
    // 2. 上传Hadoop配置文件
    const hadoopFileIds = await uploadHadoopConfigFiles(hiveSiteFile, hdfsSiteFile, coreSiteFile);
    
    // 3. 配置数据源
    const params = new URLSearchParams();
    params.append('principal', 'hive/<EMAIL>');
    
    // Kerberos文件ID
    params.append('keytabFileId', kerberosFileIds.keytab);
    params.append('krb5ConfFileId', kerberosFileIds.krb5conf);
    if (kerberosFileIds.jaasconf) {
      params.append('jaasConfFileId', kerberosFileIds.jaasconf);
    }
    
    // Hadoop配置文件ID
    if (hadoopFileIds.hivesite) {
      params.append('hiveSiteFileId', hadoopFileIds.hivesite);
    }
    if (hadoopFileIds.hdfssite) {
      params.append('hdfsSiteFileId', hadoopFileIds.hdfssite);
    }
    if (hadoopFileIds.coresite) {
      params.append('coreSiteFileId', hadoopFileIds.coresite);
    }
    
    const response = await fetch(`/datasourceInfo/kerberos/configure/${datasourceId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params
    });
    
    const result = await response.json();
    if (result.code === 200) {
      console.log('数据源配置成功');
    }
    
  } catch (error) {
    console.error('配置失败:', error);
  }
}
```

## 配置文件示例

### hive-site.xml示例

```xml
<?xml version="1.0"?>
<configuration>
  <property>
    <name>javax.jdo.option.ConnectionURL</name>
    <value>******************************************</value>
  </property>
  
  <property>
    <name>javax.jdo.option.ConnectionDriverName</name>
    <value>com.mysql.cj.jdbc.Driver</value>
  </property>
  
  <property>
    <name>hive.metastore.uris</name>
    <value>thrift://localhost:9083</value>
  </property>
  
  <property>
    <name>hive.security.authorization.enabled</name>
    <value>true</value>
  </property>
</configuration>
```

### hdfs-site.xml示例

```xml
<?xml version="1.0"?>
<configuration>
  <property>
    <name>dfs.nameservices</name>
    <value>mycluster</value>
  </property>
  
  <property>
    <name>dfs.ha.namenodes.mycluster</name>
    <value>nn1,nn2</value>
  </property>
  
  <property>
    <name>dfs.namenode.rpc-address.mycluster.nn1</name>
    <value>namenode1:8020</value>
  </property>
  
  <property>
    <name>dfs.namenode.rpc-address.mycluster.nn2</name>
    <value>namenode2:8020</value>
  </property>
</configuration>
```

### core-site.xml示例

```xml
<?xml version="1.0"?>
<configuration>
  <property>
    <name>fs.defaultFS</name>
    <value>hdfs://mycluster</value>
  </property>
  
  <property>
    <name>hadoop.security.authentication</name>
    <value>kerberos</value>
  </property>
  
  <property>
    <name>hadoop.security.authorization</name>
    <value>true</value>
  </property>
</configuration>
```

## 最佳实践

1. **文件完整性**: 确保上传的XML文件格式正确，可以被解析
2. **配置一致性**: 确保各配置文件之间的设置一致
3. **安全配置**: 在配置文件中正确设置Kerberos相关参数
4. **环境适配**: 根据不同环境调整配置文件内容
5. **版本兼容**: 确保配置文件与Hadoop/Hive版本兼容

## 故障排除

1. **XML格式错误**: 检查XML文件格式是否正确
2. **配置冲突**: 检查不同配置文件之间是否有冲突设置
3. **路径错误**: 确认配置文件中的路径和地址正确
4. **权限问题**: 确保配置的用户有相应的访问权限
