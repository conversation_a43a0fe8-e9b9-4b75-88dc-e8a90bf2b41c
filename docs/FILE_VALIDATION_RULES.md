# 文件校验规则说明

## 概述

系统采用基于文件扩展名的校验机制，确保上传的文件类型正确且与指定的fileType参数匹配。

## 校验机制

### 1. 文件扩展名校验

系统首先检查文件的扩展名是否在支持列表中：

**支持的文件扩展名**:
- `.keytab` - Kerberos密钥文件
- `.conf` - 配置文件
- `.xml` - XML配置文件
- `.properties` - 属性配置文件

### 2. 类型匹配校验

系统会验证文件扩展名与fileType参数是否匹配：

| 文件扩展名 | 允许的fileType | 文件说明 |
|-----------|---------------|----------|
| `.keytab` | `keytab` | Kerberos密钥文件 |
| `.conf` | `krb5conf`, `jaasconf` | krb5.conf, jaas.conf配置文件 |
| `.xml` | `hivesite`, `hdfssite`, `coresite` | hive-site.xml, hdfs-site.xml, core-site.xml |
| `.properties` | `hiveclient` | hiveClient.properties |

## 校验流程

```
1. 检查文件是否为空
   ↓
2. 检查文件名是否为空
   ↓
3. 提取文件扩展名
   ↓
4. 验证扩展名是否支持
   ↓
5. 验证扩展名与fileType是否匹配
   ↓
6. 通过校验，继续上传
```

## 错误信息

### 1. 不支持的文件扩展名

**错误信息**:
```
不支持的文件扩展名: .txt，文件: example.txt，支持的扩展名: [.keytab, .conf, .xml, .properties]
```

**解决方案**: 确保文件有正确的扩展名

### 2. 文件扩展名与类型不匹配

**错误信息**:
```
文件扩展名与类型不匹配: 文件 hive-site.xml (扩展名: .xml) 与类型 keytab 不匹配，期望的类型: hivesite, hdfssite, coresite
```

**解决方案**: 确保fileType参数与文件扩展名匹配

## 使用示例

### 正确的使用方式

```javascript
// 上传keytab文件
await uploadSingleKerberosFile(keytabFile, 'keytab');  // ✓ 正确

// 上传krb5.conf文件
await uploadSingleKerberosFile(krb5ConfFile, 'krb5conf');  // ✓ 正确

// 上传hive-site.xml文件
await uploadSingleKerberosFile(hiveSiteFile, 'hivesite');  // ✓ 正确

// 上传hiveClient.properties文件
await uploadSingleKerberosFile(hiveClientFile, 'hiveclient');  // ✓ 正确
```

### 错误的使用方式

```javascript
// 文件扩展名与类型不匹配
await uploadSingleKerberosFile(keytabFile, 'hivesite');  // ✗ 错误

// 不支持的文件扩展名
await uploadSingleKerberosFile(textFile, 'keytab');  // ✗ 错误 (.txt不支持)

// 文件类型错误
await uploadSingleKerberosFile(xmlFile, 'keytab');  // ✗ 错误 (.xml不能用于keytab)
```

## 前端校验建议

### 1. 文件选择器配置

```html
<!-- keytab文件 -->
<input type="file" accept=".keytab" />

<!-- 配置文件 -->
<input type="file" accept=".conf" />

<!-- XML配置文件 -->
<input type="file" accept=".xml" />

<!-- 属性文件 -->
<input type="file" accept=".properties" />
```

### 2. JavaScript预校验

```javascript
function validateFileBeforeUpload(file, expectedFileType) {
  // 检查文件是否存在
  if (!file) {
    throw new Error('请选择文件');
  }
  
  // 获取文件扩展名
  const fileName = file.name;
  const fileExtension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
  
  // 定义扩展名与类型的映射
  const extensionTypeMap = {
    '.keytab': ['keytab'],
    '.conf': ['krb5conf', 'jaasconf'],
    '.xml': ['hivesite', 'hdfssite', 'coresite'],
    '.properties': ['hiveclient']
  };
  
  // 检查扩展名是否支持
  if (!extensionTypeMap[fileExtension]) {
    throw new Error(`不支持的文件扩展名: ${fileExtension}`);
  }
  
  // 检查类型是否匹配
  if (!extensionTypeMap[fileExtension].includes(expectedFileType)) {
    throw new Error(`文件类型不匹配: ${fileName} 不能用于 ${expectedFileType}`);
  }
  
  return true;
}

// 使用示例
try {
  validateFileBeforeUpload(selectedFile, 'keytab');
  // 校验通过，可以上传
  await uploadSingleKerberosFile(selectedFile, 'keytab');
} catch (error) {
  console.error('文件校验失败:', error.message);
}
```

### 3. React组件示例

```jsx
const FileUploadWithValidation = ({ fileType, onFileUploaded }) => {
  const [error, setError] = useState('');
  
  const getAcceptedExtensions = (fileType) => {
    const typeExtensionMap = {
      'keytab': '.keytab',
      'krb5conf': '.conf',
      'jaasconf': '.conf',
      'hivesite': '.xml',
      'hdfssite': '.xml',
      'coresite': '.xml',
      'hiveclient': '.properties'
    };
    return typeExtensionMap[fileType] || '';
  };
  
  const handleFileChange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    setError('');
    
    try {
      // 前端预校验
      validateFileBeforeUpload(file, fileType);
      
      // 上传文件
      const fileId = await uploadSingleKerberosFile(file, fileType);
      onFileUploaded(fileId);
      
    } catch (error) {
      setError(error.message);
    }
  };
  
  return (
    <div>
      <input
        type="file"
        accept={getAcceptedExtensions(fileType)}
        onChange={handleFileChange}
      />
      {error && <div className="error">{error}</div>}
    </div>
  );
};
```

## 后端校验优势

1. **安全性**: 防止恶意文件上传
2. **一致性**: 确保文件类型与用途匹配
3. **可靠性**: 双重校验（扩展名+类型匹配）
4. **用户友好**: 提供详细的错误信息

## 扩展性

如需添加新的文件类型支持，只需：

1. 在`supportedExtensions`中添加新的扩展名
2. 在`validateFileTypeMatch`方法中添加新的匹配规则
3. 更新文档和前端校验逻辑

## 总结

新的校验机制通过文件扩展名确保文件类型的正确性，提供了更强的安全性和更好的用户体验。前端和后端的双重校验确保了系统的可靠性。
