# Kerberos单个文件上传指南

## 概述

本文档介绍如何使用单个文件上传接口来上传Kerberos认证文件。前端可以逐个上传文件，获取每个文件的ID，然后在保存数据源时配置Kerberos认证。

## API接口

### 单个文件上传接口

**接口**: `POST /datasourceInfo/kerberos/upload/single`

**参数**:
- `file`: 上传的文件 (必需)
- `fileType`: 文件类型 (必需)
  - `keytab`: keytab文件
  - `krb5conf`: krb5.conf文件
  - `jaasconf`: jaas.conf文件
  - `hivesite`: hive-site.xml文件
  - `hdfssite`: hdfs-site.xml文件
  - `coresite`: core-site.xml文件
  - `hiveclient`: hiveClient.properties文件

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "fileId": "file-id-123",
    "fileType": "keytab",
    "originalFileName": "hive.keytab",
    "standardFileName": "hive.keytab",
    "fileSize": 1024,
    "uploadTime": "2025-01-20T10:30:00Z",
    "success": true,
    "errorMessage": null
  }
}
```

## 前端实现示例

### 1. 基础上传函数

```javascript
/**
 * 上传单个Kerberos文件
 * @param {File} file - 要上传的文件
 * @param {string} fileType - 文件类型 (keytab, krb5conf, jaasconf, hivesite, hdfssite, coresite, hiveclient)
 * @returns {Promise<string>} 返回文件ID
 */
async function uploadSingleKerberosFile(file, fileType) {
  if (!file) {
    throw new Error('文件不能为空');
  }

  if (!['keytab', 'krb5conf', 'jaasconf', 'hivesite', 'hdfssite', 'coresite', 'hiveclient'].includes(fileType)) {
    throw new Error('不支持的文件类型: ' + fileType);
  }
  
  const formData = new FormData();
  formData.append('file', file);
  formData.append('fileType', fileType);
  
  try {
    const response = await fetch('/datasourceInfo/kerberos/upload/single', {
      method: 'POST',
      body: formData,
      headers: {
        // 不要设置Content-Type，让浏览器自动设置multipart/form-data
      }
    });
    
    const result = await response.json();
    
    if (result.code === 200 && result.data.success) {
      return result.data.fileId;
    } else {
      throw new Error(result.data.errorMessage || result.message || '上传失败');
    }
  } catch (error) {
    console.error(`${fileType}文件上传失败:`, error);
    throw error;
  }
}
```

### 2. React组件示例

```jsx
import React, { useState } from 'react';

const KerberosFileUpload = ({ onFileUploaded }) => {
  const [uploading, setUploading] = useState({});
  const [fileIds, setFileIds] = useState({});
  const [errors, setErrors] = useState({});

  const handleFileUpload = async (file, fileType) => {
    if (!file) return;
    
    setUploading(prev => ({ ...prev, [fileType]: true }));
    setErrors(prev => ({ ...prev, [fileType]: null }));
    
    try {
      const fileId = await uploadSingleKerberosFile(file, fileType);
      
      setFileIds(prev => {
        const newFileIds = { ...prev, [fileType]: fileId };
        onFileUploaded && onFileUploaded(newFileIds);
        return newFileIds;
      });
      
      console.log(`${fileType}文件上传成功:`, fileId);
    } catch (error) {
      setErrors(prev => ({ ...prev, [fileType]: error.message }));
    } finally {
      setUploading(prev => ({ ...prev, [fileType]: false }));
    }
  };

  return (
    <div className="kerberos-upload">
      <h3>Kerberos认证文件上传</h3>
      
      {/* keytab文件上传 */}
      <div className="file-upload-item">
        <label>Keytab文件 (必需):</label>
        <input
          type="file"
          accept=".keytab"
          onChange={(e) => handleFileUpload(e.target.files[0], 'keytab')}
          disabled={uploading.keytab}
        />
        {uploading.keytab && <span>上传中...</span>}
        {fileIds.keytab && <span className="success">✓ 已上传</span>}
        {errors.keytab && <span className="error">✗ {errors.keytab}</span>}
      </div>
      
      {/* krb5.conf文件上传 */}
      <div className="file-upload-item">
        <label>krb5.conf文件 (必需):</label>
        <input
          type="file"
          accept=".conf"
          onChange={(e) => handleFileUpload(e.target.files[0], 'krb5conf')}
          disabled={uploading.krb5conf}
        />
        {uploading.krb5conf && <span>上传中...</span>}
        {fileIds.krb5conf && <span className="success">✓ 已上传</span>}
        {errors.krb5conf && <span className="error">✗ {errors.krb5conf}</span>}
      </div>
      
      {/* jaas.conf文件上传 */}
      <div className="file-upload-item">
        <label>jaas.conf文件 (可选):</label>
        <input
          type="file"
          accept=".conf"
          onChange={(e) => handleFileUpload(e.target.files[0], 'jaasconf')}
          disabled={uploading.jaasconf}
        />
        {uploading.jaasconf && <span>上传中...</span>}
        {fileIds.jaasconf && <span className="success">✓ 已上传</span>}
        {errors.jaasconf && <span className="error">✗ {errors.jaasconf}</span>}
      </div>
      
      <div className="upload-status">
        <p>已上传文件ID:</p>
        <pre>{JSON.stringify(fileIds, null, 2)}</pre>
      </div>
    </div>
  );
};

export default KerberosFileUpload;
```

### 3. Vue组件示例

```vue
<template>
  <div class="kerberos-upload">
    <h3>Kerberos认证文件上传</h3>
    
    <!-- keytab文件上传 -->
    <div class="file-upload-item">
      <label>Keytab文件 (必需):</label>
      <input
        type="file"
        accept=".keytab"
        @change="(e) => handleFileUpload(e.target.files[0], 'keytab')"
        :disabled="uploading.keytab"
      />
      <span v-if="uploading.keytab">上传中...</span>
      <span v-if="fileIds.keytab" class="success">✓ 已上传</span>
      <span v-if="errors.keytab" class="error">✗ {{ errors.keytab }}</span>
    </div>
    
    <!-- krb5.conf文件上传 -->
    <div class="file-upload-item">
      <label>krb5.conf文件 (必需):</label>
      <input
        type="file"
        accept=".conf"
        @change="(e) => handleFileUpload(e.target.files[0], 'krb5conf')"
        :disabled="uploading.krb5conf"
      />
      <span v-if="uploading.krb5conf">上传中...</span>
      <span v-if="fileIds.krb5conf" class="success">✓ 已上传</span>
      <span v-if="errors.krb5conf" class="error">✗ {{ errors.krb5conf }}</span>
    </div>
    
    <!-- jaas.conf文件上传 -->
    <div class="file-upload-item">
      <label>jaas.conf文件 (可选):</label>
      <input
        type="file"
        accept=".conf"
        @change="(e) => handleFileUpload(e.target.files[0], 'jaasconf')"
        :disabled="uploading.jaasconf"
      />
      <span v-if="uploading.jaasconf">上传中...</span>
      <span v-if="fileIds.jaasconf" class="success">✓ 已上传</span>
      <span v-if="errors.jaasconf" class="error">✗ {{ errors.jaasconf }}</span>
    </div>
    
    <div class="upload-status">
      <p>已上传文件ID:</p>
      <pre>{{ JSON.stringify(fileIds, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KerberosFileUpload',
  data() {
    return {
      uploading: {},
      fileIds: {},
      errors: {}
    };
  },
  methods: {
    async handleFileUpload(file, fileType) {
      if (!file) return;
      
      this.$set(this.uploading, fileType, true);
      this.$set(this.errors, fileType, null);
      
      try {
        const fileId = await uploadSingleKerberosFile(file, fileType);
        
        this.$set(this.fileIds, fileType, fileId);
        this.$emit('file-uploaded', this.fileIds);
        
        console.log(`${fileType}文件上传成功:`, fileId);
      } catch (error) {
        this.$set(this.errors, fileType, error.message);
      } finally {
        this.$set(this.uploading, fileType, false);
      }
    }
  }
};
</script>
```

## 使用流程

### 1. 逐个上传文件
```javascript
// 用户选择并上传keytab文件
const keytabFileId = await uploadSingleKerberosFile(keytabFile, 'keytab');

// 用户选择并上传krb5.conf文件
const krb5ConfFileId = await uploadSingleKerberosFile(krb5ConfFile, 'krb5conf');

// 用户选择并上传jaas.conf文件（可选）
const jaasConfFileId = await uploadSingleKerberosFile(jaasConfFile, 'jaasconf');
```

### 2. 保存数据源时配置Kerberos
```javascript
// 构建Kerberos配置
const kerberosConfig = {
  principal: 'hive/<EMAIL>',
  keytabFileId: keytabFileId,
  krb5ConfFileId: krb5ConfFileId,
  jaasConfFileId: jaasConfFileId // 可选
};

// 保存数据源时配置Kerberos
await configureKerberos(datasourceId, kerberosConfig);
```

## 错误处理

### 常见错误及处理方式

1. **文件类型不支持**
   - 错误信息: "不支持的文件类型"
   - 解决方案: 检查fileType参数是否为 keytab, krb5conf, jaasconf 之一

2. **文件大小超限**
   - 错误信息: "文件大小超过限制"
   - 解决方案: 检查文件大小是否超过10MB限制

3. **文件格式错误**
   - 错误信息: "不支持的文件格式"
   - 解决方案: 确保文件扩展名正确（.keytab, .conf）

4. **网络错误**
   - 错误信息: "网络请求失败"
   - 解决方案: 检查网络连接，重试上传

## 最佳实践

1. **文件验证**: 上传前在前端验证文件类型和大小
2. **进度提示**: 显示上传进度和状态
3. **错误处理**: 提供清晰的错误信息和重试机制
4. **用户体验**: 支持拖拽上传和文件预览
5. **状态管理**: 妥善保存文件ID直到数据源配置完成
