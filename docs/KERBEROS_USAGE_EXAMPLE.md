# Hive Kerberos认证使用示例

## 概述

本文档展示如何在Hive连接器中集成和使用Kerberos认证功能。

## 核心流程

### 1. 文件上传阶段
```java
// 用户通过前端上传Kerberos文件
// 系统调用 joyadataAppFile.upload() 存储到统一文件系统
// 获得文件ID并保存到数据源节点配置中
```

### 2. 连接准备阶段
```java
// Hive连接器在建立连接前调用
String tempDir = datasourceInfoService.prepareKerberosFilesForConnection(datasourceId);

if (tempDir != null) {
    // 启用了Kerberos认证
    // 文件已下载到临时目录，可以使用
    String keytabPath = tempDir + "/hive.keytab";
    String krb5ConfPath = tempDir + "/krb5.conf";
    
    // 设置Kerberos系统属性
    System.setProperty("java.security.krb5.conf", krb5ConfPath);
    
    // 进行Kerberos认证
    // ... Hive连接逻辑
}
```

### 3. 连接清理阶段
```java
// 连接使用完毕后清理临时文件
try {
    // ... Hive连接逻辑
} finally {
    datasourceInfoService.cleanupKerberosFiles(datasourceId);
}
```

## 在Hive连接器中的集成示例

### 1. 修改Hive连接器

```java
@Component
public class HiveConnectionManager {
    
    @Autowired
    private DatasourceInfoService datasourceInfoService;
    
    public Connection createConnection(String datasourceId, DatasourceDTO datasourceDTO) {
        String kerberosDir = null;
        
        try {
            // 准备Kerberos文件
            kerberosDir = datasourceInfoService.prepareKerberosFilesForConnection(datasourceId);
            
            if (kerberosDir != null) {
                // 启用Kerberos认证
                return createKerberosConnection(datasourceDTO, kerberosDir);
            } else {
                // 普通连接
                return createNormalConnection(datasourceDTO);
            }
            
        } catch (Exception e) {
            log.error("创建Hive连接失败: {}", e.getMessage(), e);
            throw new AppErrorException("连接失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (kerberosDir != null) {
                datasourceInfoService.cleanupKerberosFiles(datasourceId);
            }
        }
    }
    
    private Connection createKerberosConnection(DatasourceDTO datasourceDTO, String kerberosDir) {
        try {
            // 设置Kerberos配置
            String krb5ConfPath = kerberosDir + "/krb5.conf";
            String keytabPath = kerberosDir + "/hive.keytab";
            
            System.setProperty("java.security.krb5.conf", krb5ConfPath);
            System.setProperty("java.security.auth.login.config", kerberosDir + "/jaas.conf");
            
            // 获取principal（从数据源配置中获取）
            String principal = getKerberosPrincipal(datasourceDTO);
            
            // 进行Kerberos认证
            Configuration conf = new Configuration();
            UserGroupInformation.setConfiguration(conf);
            UserGroupInformation.loginUserFromKeytab(principal, keytabPath);
            
            // 创建Hive连接
            String jdbcUrl = datasourceDTO.getJdbcUrl();
            return DriverManager.getConnection(jdbcUrl);
            
        } catch (Exception e) {
            log.error("创建Kerberos连接失败: {}", e.getMessage(), e);
            throw new AppErrorException("Kerberos认证失败: " + e.getMessage());
        }
    }
    
    private Connection createNormalConnection(DatasourceDTO datasourceDTO) {
        // 普通连接逻辑
        String jdbcUrl = datasourceDTO.getJdbcUrl();
        String username = datasourceDTO.getUsername();
        String password = datasourceDTO.getPassword();
        
        return DriverManager.getConnection(jdbcUrl, username, password);
    }
    
    private String getKerberosPrincipal(DatasourceDTO datasourceDTO) {
        // 从数据源配置中获取Kerberos principal
        // 这里需要根据实际的配置获取方式进行实现
        return "hive/<EMAIL>";
    }
}
```

### 2. 在连接测试中集成

```java
@Override
public DatasourceConnectProgressVO connect(DatasourceInfoSaveDTO datasourceInfoSaveDTO) {
    DatasourceConnectProgressVO result = new DatasourceConnectProgressVO();
    List<DatasourceConnectVO> datasourceConnectVOList = DatasourceConnectVO.buildDatasourceConnectVO();
    result.setDatasourceConnectVOList(datasourceConnectVOList);
    
    String datasourceId = datasourceInfoSaveDTO.getDatasourceInfo().getId();
    String kerberosDir = null;
    
    try {
        // 网络连通性测试
        // ... 现有的网络测试逻辑
        
        // 准备Kerberos文件（如果需要）
        kerberosDir = datasourceInfoService.prepareKerberosFilesForConnection(datasourceId);
        
        // 数据库连接测试
        DatasourceDTO datasourceDTO = DatasourceUtil.getDatasourceDTO(
            datasourceInfoSaveDTO.getDatasourceInfo(), 
            datasourceInfoSaveDTO.getDatasourceNode()
        );
        
        Boolean connectionResult;
        if (kerberosDir != null) {
            // 使用Kerberos认证测试连接
            connectionResult = testKerberosConnection(datasourceDTO, kerberosDir);
        } else {
            // 普通连接测试
            connectionResult = DatasourceUtils.checkConnectionWithConf(datasourceDTO, null, null);
        }
        
        if (!connectionResult) {
            processDatasourceConnect(datasourceConnectVOList, 
                DatasourceConnectVO.USERNAME_PASSWORD, "连接失败", DatasourceConnectVO.FAIL);
            return result;
        }
        
        processDatasourceConnect(datasourceConnectVOList, 
            DatasourceConnectVO.USERNAME_PASSWORD, null, DatasourceConnectVO.SUCCESS);
        
        // 其他测试逻辑...
        
    } catch (Exception e) {
        log.error("连接测试失败: {}", e.getMessage(), e);
        processDatasourceConnect(datasourceConnectVOList, 
            DatasourceConnectVO.USERNAME_PASSWORD, e.getMessage(), DatasourceConnectVO.FAIL);
    } finally {
        // 清理临时文件
        if (kerberosDir != null) {
            datasourceInfoService.cleanupKerberosFiles(datasourceId);
        }
    }
    
    return result;
}
```

## API使用示例

### 1. 上传Kerberos文件

```bash
curl -X POST \
  http://localhost:8080/datasourceInfo/kerberos/upload/ds-001 \
  -H 'Content-Type: multipart/form-data' \
  -F 'keytab=@hive.keytab' \
  -F 'krb5Conf=@krb5.conf' \
  -F 'jaasConf=@jaas.conf'
```

### 2. 查询Kerberos状态

```bash
curl -X GET \
  http://localhost:8080/datasourceInfo/kerberos/status/ds-001
```

### 3. 删除Kerberos文件

```bash
curl -X DELETE \
  http://localhost:8080/datasourceInfo/kerberos/ds-001
```

## 最佳实践

### 1. 错误处理
- 始终在finally块中清理临时文件
- 提供详细的错误信息帮助排查问题
- 记录关键操作的日志

### 2. 性能优化
- 临时文件使用完毕立即清理
- 避免重复下载相同的文件
- 考虑文件缓存机制

### 3. 安全考虑
- 临时文件设置适当的权限
- 定期清理过期的临时文件
- 监控文件访问日志

### 4. 监控和告警
- 监控Kerberos认证成功率
- 设置文件下载失败告警
- 监控临时目录磁盘使用情况
