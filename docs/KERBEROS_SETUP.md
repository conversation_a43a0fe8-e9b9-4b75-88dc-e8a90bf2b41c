# Hive Kerberos认证配置指南

## 概述

本文档介绍如何在数据源管理系统中配置Hive的Kerberos认证，支持多节点高可用部署模式。

## 功能特性

- ✅ 支持Kerberos认证文件上传（keytab、krb5.conf、jaas.conf）
- ✅ 基于统一文件存储系统，支持多节点高可用
- ✅ 文件安全校验和权限控制
- ✅ 自动化配置管理
- ✅ 按需下载到临时目录使用
- ✅ 租户隔离存储

## 存储架构

### 统一文件存储
- 文件通过 `joyadataAppFile.upload()` 上传到统一文件存储系统
- 获得唯一的文件ID，存储在数据源节点配置中
- 支持多节点共享访问，天然支持高可用

### 临时目录结构（按需创建）
```
{localKerberosPath}/
├── DatabaseCenter_{tenantCode}/
│   └── DatasourceInfoId_{datasourceId}/
│       ├── krb5.conf          # 从统一存储下载的临时文件
│       ├── hive.keytab        # 从统一存储下载的临时文件
│       └── jaas.conf          # 从统一存储下载的临时文件（可选）
```

## 配置说明

### 1. 应用配置

在 `local-dedp-dms-dsc.yml` 中配置：

```yaml
database:
  # 生产环境Kerberos文件路径
  kerberos_path: /plugins/kerberos_tmp
  # 本地开发环境路径
  local_kerberos_path: /Users/<USER>/IdeaProjects/joyadata/standard/datasourcex/build/kerberos_tmp
  # Kerberos文件配置
  kerberos_file_max_size: 10MB
  kerberos_cleanup_days: 30
  kerberos_backup_enabled: true
```

### 2. 数据源节点配置

Kerberos配置存储在 `DatasourceNode.advancedConfiguration` 字段中：

```json
{
  "kerberos": {
    "enabled": true,
    "principal": "hive/<EMAIL>",
    "files": {
      "keytab": {
        "fileId": "file-id-123",
        "fileName": "hive.keytab",
        "uploadTime": "2025-01-20T10:30:00Z",
        "fileSize": 1024
      },
      "krb5Conf": {
        "fileId": "file-id-456",
        "fileName": "krb5.conf",
        "uploadTime": "2025-01-20T10:30:00Z",
        "fileSize": 2048
      },
      "jaasConf": {
        "fileId": "file-id-789",
        "fileName": "jaas.conf",
        "uploadTime": "2025-01-20T10:30:00Z",
        "fileSize": 512
      }
    }
  }
}
```

## API接口

### 1. 上传Kerberos文件

```http
POST /datasourceInfo/kerberos/upload/{datasourceId}
Content-Type: multipart/form-data

Parameters:
- keytab: keytab文件 (必需)
- krb5Conf: krb5.conf文件 (必需)
- jaasConf: jaas.conf文件 (可选)
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "datasourceId": "ds-001",
    "uploadedFiles": [
      {
        "fileName": "hive.keytab",
        "fileSize": 1024,
        "fileType": "keytab",
        "fileHash": "sha256:abc123...",
        "filePath": "/path/to/hive.keytab",
        "uploaded": true
      }
    ],
    "uploadTime": "2025-01-20T10:30:00Z",
    "kerberosPath": "/path/to/kerberos/dir"
  }
}
```

### 2. 删除Kerberos文件

```http
DELETE /datasourceInfo/kerberos/{datasourceId}
```

### 3. 查询Kerberos状态

```http
GET /datasourceInfo/kerberos/status/{datasourceId}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "exists": true,
    "directoryExists": true,
    "enabled": true,
    "principal": "hive/<EMAIL>",
    "uploadTime": "2025-01-20T10:30:00Z",
    "files": ["hive.keytab", "krb5.conf"],
    "filesValid": true
  }
}
```

## 使用流程

### 1. 准备Kerberos文件

确保您有以下文件：
- `*.keytab`: Kerberos密钥文件
- `krb5.conf`: Kerberos配置文件
- `jaas.conf`: JAAS配置文件（可选）

### 2. 上传文件

通过API或前端界面上传Kerberos认证文件。

### 3. 配置数据源

系统会自动更新数据源节点的高级配置，添加Kerberos认证信息。

### 4. 测试连接

使用数据源连接测试功能验证Kerberos认证是否正常工作。

## 多节点高可用

### 共享存储模式（推荐）

所有节点挂载同一个共享存储（如NFS），确保Kerberos文件在所有节点上都可访问。

```bash
# 在所有节点上挂载共享存储
sudo mount -t nfs nfs-server:/path/to/kerberos /dsg/app/plugins/kerberos_tmp
```

### 文件同步模式

如果无法使用共享存储，可以通过文件同步机制确保文件在所有节点上保持一致。

## 安全考虑

1. **文件权限**: 自动设置为600（仅所有者可读写）
2. **目录隔离**: 按租户和数据源ID隔离存储
3. **文件校验**: 使用SHA256校验文件完整性
4. **访问控制**: 通过认证和授权机制控制访问

## 故障排除

### 1. 文件上传失败

- 检查文件大小是否超过限制（默认10MB）
- 检查文件格式是否正确（.keytab, .conf）
- 检查磁盘空间是否充足

### 2. 连接测试失败

- 验证Kerberos文件是否完整
- 检查principal配置是否正确
- 确认网络连接正常

### 3. 多节点同步问题

- 检查共享存储挂载状态
- 验证文件权限设置
- 检查网络连接

## 日志监控

相关日志位置：
- 应用日志: `logs/info.log`
- 错误日志: `logs/error.log`

关键日志关键词：
- `Kerberos文件上传`
- `Kerberos配置更新`
- `Kerberos文件校验`
