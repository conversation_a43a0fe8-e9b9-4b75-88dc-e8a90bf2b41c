# Hive Kerberos认证异步流程指南

## 概述

本文档介绍新的异步Kerberos认证文件上传和配置流程，支持前端先上传文件获取ID，然后在保存数据源时配置Kerberos认证。

## 新的流程设计

### 流程1：异步文件上传
```
前端上传文件 → 后端上传到统一存储 → 返回文件ID → 前端保存文件ID
```

### 流程2：数据源配置
```
前端保存数据源 → 传递文件ID和principal → 后端配置Kerberos → 更新节点配置
```

### 流程3：连接时文件准备
```
测试连接/获取连接 → 读取节点配置 → 下载文件到临时目录 → 使用文件进行连接 → 清理临时文件
```

## API接口详解

### 1. 异步上传Kerberos文件

**接口**: `POST /datasourceInfo/kerberos/upload`

**参数**:
- `keytab`: keytab文件 (必需)
- `krb5Conf`: krb5.conf文件 (必需)  
- `jaasConf`: jaas.conf文件 (可选)

**请求示例**:
```bash
curl -X POST \
  http://localhost:8080/datasourceInfo/kerberos/upload \
  -H 'Content-Type: multipart/form-data' \
  -F 'keytab=@hive.keytab' \
  -F 'krb5Conf=@krb5.conf' \
  -F 'jaasConf=@jaas.conf'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "uploadedFiles": [
      {
        "fileId": "file-id-123",
        "fileType": "keytab",
        "originalFileName": "hive.keytab",
        "standardFileName": "hive.keytab",
        "fileSize": 1024,
        "uploadTime": "2025-01-20T10:30:00Z",
        "success": true
      },
      {
        "fileId": "file-id-456",
        "fileType": "krb5conf",
        "originalFileName": "krb5.conf",
        "standardFileName": "krb5.conf",
        "fileSize": 2048,
        "uploadTime": "2025-01-20T10:30:00Z",
        "success": true
      }
    ],
    "uploadTime": "2025-01-20T10:30:00Z"
  }
}
```

### 2. 配置数据源Kerberos认证

**接口**: `POST /datasourceInfo/kerberos/configure/{datasourceId}`

**参数**:
- `principal`: Kerberos主体 (必需)
- `keytabFileId`: keytab文件ID (必需)
- `krb5ConfFileId`: krb5.conf文件ID (必需)
- `jaasConfFileId`: jaas.conf文件ID (可选)

**请求示例**:
```bash
curl -X POST \
  http://localhost:8080/datasourceInfo/kerberos/configure/ds-001 \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'principal=hive/<EMAIL>' \
  -d 'keytabFileId=file-id-123' \
  -d 'krb5ConfFileId=file-id-456' \
  -d 'jaasConfFileId=file-id-789'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": "Kerberos配置成功"
}
```

### 3. 查询Kerberos状态

**接口**: `GET /datasourceInfo/kerberos/status/{datasourceId}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "exists": true,
    "enabled": true,
    "principal": "hive/<EMAIL>",
    "files": {
      "keytab": {
        "fileId": "file-id-123",
        "fileName": "hive.keytab",
        "uploadTime": "2025-01-20T10:30:00Z",
        "fileSize": 1024
      },
      "krb5Conf": {
        "fileId": "file-id-456",
        "fileName": "krb5.conf",
        "uploadTime": "2025-01-20T10:30:00Z",
        "fileSize": 2048
      }
    },
    "configValid": true,
    "fileTypes": ["keytab", "krb5Conf"],
    "fileCount": 2
  }
}
```

## 前端集成示例

### 1. 文件上传组件

```javascript
// 上传Kerberos文件
async function uploadKerberosFiles(keytabFile, krb5ConfFile, jaasConfFile) {
  const formData = new FormData();
  formData.append('keytab', keytabFile);
  formData.append('krb5Conf', krb5ConfFile);
  if (jaasConfFile) {
    formData.append('jaasConf', jaasConfFile);
  }
  
  const response = await fetch('/datasourceInfo/kerberos/upload', {
    method: 'POST',
    body: formData
  });
  
  const result = await response.json();
  
  if (result.code === 200 && result.data.success) {
    // 保存文件ID供后续使用
    const fileIds = {};
    result.data.uploadedFiles.forEach(file => {
      if (file.success) {
        fileIds[file.fileType] = file.fileId;
      }
    });
    return fileIds;
  } else {
    throw new Error('文件上传失败');
  }
}

// 使用示例
const fileIds = await uploadKerberosFiles(keytabFile, krb5ConfFile, jaasConfFile);
// fileIds = { keytab: 'file-id-123', krb5conf: 'file-id-456', jaasconf: 'file-id-789' }
```

### 2. 数据源保存组件

```javascript
// 保存数据源时配置Kerberos
async function saveDatasourceWithKerberos(datasourceData, kerberosConfig) {
  // 1. 先保存数据源
  const datasource = await saveDatasource(datasourceData);
  
  // 2. 如果有Kerberos配置，则配置Kerberos
  if (kerberosConfig && kerberosConfig.enabled) {
    await configureKerberos(datasource.id, kerberosConfig);
  }
  
  return datasource;
}

async function configureKerberos(datasourceId, kerberosConfig) {
  const params = new URLSearchParams();
  params.append('principal', kerberosConfig.principal);
  params.append('keytabFileId', kerberosConfig.keytabFileId);
  params.append('krb5ConfFileId', kerberosConfig.krb5ConfFileId);
  if (kerberosConfig.jaasConfFileId) {
    params.append('jaasConfFileId', kerberosConfig.jaasConfFileId);
  }
  
  const response = await fetch(`/datasourceInfo/kerberos/configure/${datasourceId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: params
  });
  
  const result = await response.json();
  if (result.code !== 200) {
    throw new Error('Kerberos配置失败');
  }
}
```

### 3. 完整的前端流程

```javascript
// 完整的数据源创建流程
async function createDatasourceWithKerberos() {
  try {
    // 1. 用户选择文件并上传
    const fileIds = await uploadKerberosFiles(keytabFile, krb5ConfFile, jaasConfFile);
    
    // 2. 构建数据源数据
    const datasourceData = {
      name: 'Hive数据源',
      type: 'HIVE',
      // ... 其他数据源配置
    };
    
    // 3. 构建Kerberos配置
    const kerberosConfig = {
      enabled: true,
      principal: 'hive/<EMAIL>',
      keytabFileId: fileIds.keytab,
      krb5ConfFileId: fileIds.krb5conf,
      jaasConfFileId: fileIds.jaasconf
    };
    
    // 4. 保存数据源并配置Kerberos
    const datasource = await saveDatasourceWithKerberos(datasourceData, kerberosConfig);
    
    console.log('数据源创建成功:', datasource);
    
  } catch (error) {
    console.error('创建失败:', error);
  }
}
```

## 后端连接器集成

### 在Hive连接器中使用

```java
@Component
public class HiveConnectionManager {
    
    @Autowired
    private DatasourceInfoService datasourceInfoService;
    
    public Connection createConnection(String datasourceId, DatasourceDTO datasourceDTO) {
        String kerberosDir = null;
        
        try {
            // 准备Kerberos文件（如果需要）
            kerberosDir = datasourceInfoService.prepareKerberosFilesForConnection(datasourceId);
            
            if (kerberosDir != null) {
                // 使用Kerberos认证创建连接
                return createKerberosConnection(datasourceDTO, kerberosDir);
            } else {
                // 普通连接
                return createNormalConnection(datasourceDTO);
            }
            
        } finally {
            // 清理临时文件
            if (kerberosDir != null) {
                datasourceInfoService.cleanupKerberosFiles(datasourceId);
            }
        }
    }
    
    private Connection createKerberosConnection(DatasourceDTO datasourceDTO, String kerberosDir) {
        // 设置Kerberos配置文件路径
        System.setProperty("java.security.krb5.conf", kerberosDir + "/krb5.conf");
        
        // 进行Kerberos认证
        String keytabPath = kerberosDir + "/hive.keytab";
        String principal = getKerberosPrincipal(datasourceDTO);
        
        UserGroupInformation.loginUserFromKeytab(principal, keytabPath);
        
        // 创建连接
        return DriverManager.getConnection(datasourceDTO.getJdbcUrl());
    }
}
```

## 优势分析

### 相比同步方案的优势

1. **更好的用户体验**：文件上传和数据源保存分离，避免长时间等待
2. **更灵活的流程**：前端可以先验证文件，再决定是否保存数据源
3. **更好的错误处理**：文件上传失败不影响数据源配置的其他部分
4. **更高的可靠性**：利用统一文件存储的高可用特性

### 技术优势

1. **统一文件管理**：利用现有的`joyadataAppFile`基础设施
2. **按需下载**：只在需要连接时才下载文件到临时目录
3. **自动清理**：连接完成后自动清理临时文件
4. **多节点支持**：天然支持多节点高可用部署

## 注意事项

1. **文件ID管理**：前端需要妥善保存文件ID，直到数据源配置完成
2. **错误处理**：需要处理文件上传成功但配置失败的情况
3. **临时文件清理**：确保连接完成后及时清理临时文件
4. **权限控制**：确保只有授权用户可以访问Kerberos文件
