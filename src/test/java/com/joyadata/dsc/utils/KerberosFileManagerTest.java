package com.joyadata.dsc.utils;

import com.joyadata.dsc.config.DatabaseConfig;
import com.joyadata.dsc.model.datasoure.dto.KerberosConfigDTO;
import com.joyadata.dsc.model.datasoure.dto.KerberosFileUploadDTO;
import com.joyadata.dsc.model.datasoure.vo.KerberosUploadResultVO;
import com.joyadata.dsc.properties.DatabaseProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * KerberosFileManager测试类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@ExtendWith(MockitoExtension.class)
public class KerberosFileManagerTest {
    
    @Mock
    private DatabaseConfig databaseConfig;
    
    @Mock
    private DatabaseProperties databaseProperties;
    
    @InjectMocks
    private KerberosFileManager kerberosFileManager;
    
    @BeforeEach
    void setUp() {
        // 模拟配置
        when(databaseConfig.getLocalKerberosPath()).thenReturn("/tmp/test_kerberos");
        when(databaseProperties.getKerberosFileMaxSize()).thenReturn("10MB");
    }
    
    @Test
    void testCreateKerberosDirectory() {
        // 测试创建Kerberos目录
        String datasourceId = "test-datasource-001";
        
        String kerberosDir = kerberosFileManager.createKerberosDirectory(datasourceId);
        
        assertNotNull(kerberosDir);
        assertTrue(kerberosDir.contains(datasourceId));
        assertTrue(kerberosDir.contains("DatabaseCenter_"));
        assertTrue(kerberosDir.contains("DatasourceInfoId_"));
    }
    
    @Test
    void testGetKerberosDirectory() {
        // 测试获取Kerberos目录路径
        String datasourceId = "test-datasource-002";
        
        String kerberosDir = kerberosFileManager.getKerberosDirectory(datasourceId);
        
        assertNotNull(kerberosDir);
        assertTrue(kerberosDir.endsWith("DatasourceInfoId_" + datasourceId));
    }
    
    @Test
    void testGetKerberosFilePath() {
        // 测试获取Kerberos文件路径
        String datasourceId = "test-datasource-003";
        String fileName = "hive.keytab";
        
        String filePath = kerberosFileManager.getKerberosFilePath(datasourceId, fileName);
        
        assertNotNull(filePath);
        assertTrue(filePath.endsWith(fileName));
        assertTrue(filePath.contains(datasourceId));
    }
    
    @Test
    void testValidateKerberosFiles_Disabled() {
        // 测试未启用Kerberos时的校验
        String datasourceId = "test-datasource-004";
        KerberosConfigDTO config = new KerberosConfigDTO();
        config.setEnabled(false);
        
        boolean result = kerberosFileManager.validateKerberosFiles(datasourceId, config);
        
        assertTrue(result); // 未启用时应该返回true
    }
    
    @Test
    void testValidateKerberosFiles_NullConfig() {
        // 测试空配置的校验
        String datasourceId = "test-datasource-005";
        
        boolean result = kerberosFileManager.validateKerberosFiles(datasourceId, null);
        
        assertTrue(result); // 空配置时应该返回true
    }
    
    @Test
    void testKerberosConfigDTO_IsConfigComplete() {
        // 测试KerberosConfigDTO的配置完整性检查
        KerberosConfigDTO config = new KerberosConfigDTO();
        
        // 测试不完整的配置
        assertFalse(config.isConfigComplete());
        
        // 设置完整配置
        config.setEnabled(true);
        config.setPrincipal("hive/<EMAIL>");
        config.setKeytabFile("hive.keytab");
        config.setKrb5ConfFile("krb5.conf");
        
        assertTrue(config.isConfigComplete());
    }
    
    @Test
    void testKerberosConfigDTO_GetRequiredFiles() {
        // 测试获取必需文件列表
        KerberosConfigDTO config = new KerberosConfigDTO();
        config.setEnabled(false);
        
        // 未启用时应该返回空数组
        String[] files = config.getRequiredFiles();
        assertEquals(0, files.length);
        
        // 启用后设置文件
        config.setEnabled(true);
        config.setKeytabFile("hive.keytab");
        config.setKrb5ConfFile("krb5.conf");
        
        files = config.getRequiredFiles();
        assertEquals(2, files.length);
        
        // 添加JAAS配置文件
        config.setJaasConfFile("jaas.conf");
        files = config.getRequiredFiles();
        assertEquals(3, files.length);
    }
    
    @Test
    void testKerberosFileUploadDTO_Success() {
        // 测试成功的文件上传DTO创建
        String fileId = "file-id-123";
        String fileType = "keytab";
        String originalFileName = "test.keytab";
        String standardFileName = "hive.keytab";
        Long fileSize = 1024L;

        KerberosFileUploadDTO dto = KerberosFileUploadDTO.success(
            fileId, fileType, originalFileName, standardFileName, fileSize);

        assertTrue(dto.getSuccess());
        assertEquals(fileId, dto.getFileId());
        assertEquals(fileType, dto.getFileType());
        assertEquals(originalFileName, dto.getOriginalFileName());
        assertEquals(standardFileName, dto.getStandardFileName());
        assertEquals(fileSize, dto.getFileSize());
        assertNotNull(dto.getUploadTime());
    }

    @Test
    void testKerberosFileUploadDTO_Failure() {
        // 测试失败的文件上传DTO创建
        String fileType = "keytab";
        String originalFileName = "test.keytab";
        String errorMessage = "Upload failed";

        KerberosFileUploadDTO dto = KerberosFileUploadDTO.failure(
            fileType, originalFileName, errorMessage);

        assertFalse(dto.getSuccess());
        assertEquals(fileType, dto.getFileType());
        assertEquals(originalFileName, dto.getOriginalFileName());
        assertEquals(errorMessage, dto.getErrorMessage());
        assertNotNull(dto.getUploadTime());
    }

    @Test
    void testKerberosUploadResultVO_Success() {
        // 测试成功结果创建
        List<KerberosFileUploadDTO> uploadedFiles = new ArrayList<>();
        uploadedFiles.add(KerberosFileUploadDTO.success(
            "file-id-123", "keytab", "test.keytab", "hive.keytab", 1024L));

        KerberosUploadResultVO result = KerberosUploadResultVO.success(uploadedFiles);

        assertTrue(result.getSuccess());
        assertEquals(1, result.getUploadedFiles().size());
        assertNotNull(result.getUploadTime());
    }

    @Test
    void testKerberosUploadResultVO_Failure() {
        // 测试失败结果创建
        String errorMessage = "Upload failed";

        KerberosUploadResultVO result = KerberosUploadResultVO.failure(errorMessage);

        assertFalse(result.getSuccess());
        assertEquals(errorMessage, result.getErrorMessage());
        assertNotNull(result.getUploadTime());
    }

    @Test
    void testFileExtensionValidation() {
        // 测试文件扩展名校验功能

        // 测试支持的文件扩展名
        MockMultipartFile keytabFile = new MockMultipartFile(
            "file", "test.keytab", "application/octet-stream", "test content".getBytes());
        MockMultipartFile confFile = new MockMultipartFile(
            "file", "krb5.conf", "text/plain", "test content".getBytes());
        MockMultipartFile xmlFile = new MockMultipartFile(
            "file", "hive-site.xml", "text/xml", "test content".getBytes());
        MockMultipartFile propertiesFile = new MockMultipartFile(
            "file", "hiveClient.properties", "text/plain", "test content".getBytes());

        // 验证文件对象创建成功
        assertNotNull(keytabFile);
        assertEquals("test.keytab", keytabFile.getOriginalFilename());

        assertNotNull(confFile);
        assertEquals("krb5.conf", confFile.getOriginalFilename());

        assertNotNull(xmlFile);
        assertEquals("hive-site.xml", xmlFile.getOriginalFilename());

        assertNotNull(propertiesFile);
        assertEquals("hiveClient.properties", propertiesFile.getOriginalFilename());
    }

    @Test
    void testFileExtensionExtraction() {
        // 测试文件扩展名提取逻辑
        assertEquals(".keytab", getFileExtension("test.keytab"));
        assertEquals(".conf", getFileExtension("krb5.conf"));
        assertEquals(".xml", getFileExtension("hive-site.xml"));
        assertEquals(".properties", getFileExtension("hiveClient.properties"));
        assertEquals("", getFileExtension("noextension"));
        assertEquals("", getFileExtension(""));
    }

    // 辅助方法，模拟Service中的getFileExtension方法
    private String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex);
    }
}
