package com.joyadata.dsc.utils;

import com.joyadata.dsc.config.DatabaseConfig;
import com.joyadata.dsc.model.datasoure.dto.KerberosConfigDTO;
import com.joyadata.dsc.model.datasoure.vo.KerberosUploadResultVO;
import com.joyadata.dsc.properties.DatabaseProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * KerberosFileManager测试类
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@ExtendWith(MockitoExtension.class)
public class KerberosFileManagerTest {
    
    @Mock
    private DatabaseConfig databaseConfig;
    
    @Mock
    private DatabaseProperties databaseProperties;
    
    @InjectMocks
    private KerberosFileManager kerberosFileManager;
    
    @BeforeEach
    void setUp() {
        // 模拟配置
        when(databaseConfig.getLocalKerberosPath()).thenReturn("/tmp/test_kerberos");
        when(databaseProperties.getKerberosFileMaxSize()).thenReturn("10MB");
    }
    
    @Test
    void testCreateKerberosDirectory() {
        // 测试创建Kerberos目录
        String datasourceId = "test-datasource-001";
        
        String kerberosDir = kerberosFileManager.createKerberosDirectory(datasourceId);
        
        assertNotNull(kerberosDir);
        assertTrue(kerberosDir.contains(datasourceId));
        assertTrue(kerberosDir.contains("DatabaseCenter_"));
        assertTrue(kerberosDir.contains("DatasourceInfoId_"));
    }
    
    @Test
    void testGetKerberosDirectory() {
        // 测试获取Kerberos目录路径
        String datasourceId = "test-datasource-002";
        
        String kerberosDir = kerberosFileManager.getKerberosDirectory(datasourceId);
        
        assertNotNull(kerberosDir);
        assertTrue(kerberosDir.endsWith("DatasourceInfoId_" + datasourceId));
    }
    
    @Test
    void testGetKerberosFilePath() {
        // 测试获取Kerberos文件路径
        String datasourceId = "test-datasource-003";
        String fileName = "hive.keytab";
        
        String filePath = kerberosFileManager.getKerberosFilePath(datasourceId, fileName);
        
        assertNotNull(filePath);
        assertTrue(filePath.endsWith(fileName));
        assertTrue(filePath.contains(datasourceId));
    }
    
    @Test
    void testValidateKerberosFiles_Disabled() {
        // 测试未启用Kerberos时的校验
        String datasourceId = "test-datasource-004";
        KerberosConfigDTO config = new KerberosConfigDTO();
        config.setEnabled(false);
        
        boolean result = kerberosFileManager.validateKerberosFiles(datasourceId, config);
        
        assertTrue(result); // 未启用时应该返回true
    }
    
    @Test
    void testValidateKerberosFiles_NullConfig() {
        // 测试空配置的校验
        String datasourceId = "test-datasource-005";
        
        boolean result = kerberosFileManager.validateKerberosFiles(datasourceId, null);
        
        assertTrue(result); // 空配置时应该返回true
    }
    
    @Test
    void testKerberosConfigDTO_IsConfigComplete() {
        // 测试KerberosConfigDTO的配置完整性检查
        KerberosConfigDTO config = new KerberosConfigDTO();
        
        // 测试不完整的配置
        assertFalse(config.isConfigComplete());
        
        // 设置完整配置
        config.setEnabled(true);
        config.setPrincipal("hive/<EMAIL>");
        config.setKeytabFile("hive.keytab");
        config.setKrb5ConfFile("krb5.conf");
        
        assertTrue(config.isConfigComplete());
    }
    
    @Test
    void testKerberosConfigDTO_GetRequiredFiles() {
        // 测试获取必需文件列表
        KerberosConfigDTO config = new KerberosConfigDTO();
        config.setEnabled(false);
        
        // 未启用时应该返回空数组
        String[] files = config.getRequiredFiles();
        assertEquals(0, files.length);
        
        // 启用后设置文件
        config.setEnabled(true);
        config.setKeytabFile("hive.keytab");
        config.setKrb5ConfFile("krb5.conf");
        
        files = config.getRequiredFiles();
        assertEquals(2, files.length);
        
        // 添加JAAS配置文件
        config.setJaasConfFile("jaas.conf");
        files = config.getRequiredFiles();
        assertEquals(3, files.length);
    }
    
    @Test
    void testKerberosUploadResultVO_Success() {
        // 测试成功结果创建
        String datasourceId = "test-datasource-006";
        String kerberosPath = "/tmp/kerberos";
        
        KerberosUploadResultVO result = KerberosUploadResultVO.success(
            datasourceId, null, kerberosPath);
        
        assertTrue(result.getSuccess());
        assertEquals(datasourceId, result.getDatasourceId());
        assertEquals(kerberosPath, result.getKerberosPath());
        assertNotNull(result.getUploadTime());
    }
    
    @Test
    void testKerberosUploadResultVO_Failure() {
        // 测试失败结果创建
        String datasourceId = "test-datasource-007";
        String errorMessage = "Upload failed";
        
        KerberosUploadResultVO result = KerberosUploadResultVO.failure(
            datasourceId, errorMessage);
        
        assertFalse(result.getSuccess());
        assertEquals(datasourceId, result.getDatasourceId());
        assertEquals(errorMessage, result.getErrorMessage());
        assertNotNull(result.getUploadTime());
    }
}
