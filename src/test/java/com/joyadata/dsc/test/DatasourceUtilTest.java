package com.joyadata.dsc.test;

import com.dsg.database.datasource.dto.DatasourceDTO;
import com.joyadata.dsc.DscApp;
import com.joyadata.dsc.utils.DatasourceUtil;
import com.joyadata.util.ApplicationContextHelp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest()
public class DatasourceUtilTest {

    static {
        ApplicationContextHelp.defaultBaseClass = DscApp.class;
    }

    @Autowired
    private DatasourceUtil datasourceUtil;

    @Test
    public void testGetDatasourceDTO() {
        DatasourceDTO datasourceDTO = datasourceUtil.getDatasourceDTO("17223941011584");
        System.out.println(datasourceDTO);
        System.out.println(datasourceDTO.getDataJson());
    }
}
