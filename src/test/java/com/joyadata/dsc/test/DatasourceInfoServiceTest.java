package com.joyadata.dsc.test;

import cn.hutool.core.collection.CollUtil;
import com.joyadata.dsc.DscApp;
import com.joyadata.dsc.model.datasoure.DatasourceFieldData;
import com.joyadata.dsc.model.datasoure.dto.DatasourceInfoSaveDTO;
import com.joyadata.dsc.model.datasoure.dto.SchemaQuery;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.DatasourceNode;
import com.joyadata.dsc.model.datasoure.vo.DatasourceInfoDetailVO;
import com.joyadata.dsc.model.datasoure.vo.DbAndSchemaVO;
import com.joyadata.dsc.service.DatasourceInfoService;
import com.joyadata.model.IUser;
import com.joyadata.util.ApplicationContextHelp;
import com.joyadata.util.ThreadLocalUserUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;

@RunWith(SpringRunner.class)
@SpringBootTest()
public class DatasourceInfoServiceTest {


    static {
        ApplicationContextHelp.defaultBaseClass = DscApp.class;
    }

    @Autowired
    private DatasourceInfoService datasourceInfoService;

    @Test
    public void testGetAllSchemaByDatasourceId() {
        SchemaQuery schemaQuery = new SchemaQuery();
        schemaQuery.setDatasourceId("17223941011584");
        List<String> allSchema = datasourceInfoService.getAllSchema(schemaQuery);
        System.out.println(allSchema);
        assert !allSchema.isEmpty();
    }

    @Test
    public void testGetAllSchemaByDatasourceInfoSaveDTO() {
        SchemaQuery schemaQuery = new SchemaQuery();
        schemaQuery.setDatasourceInfo(getDatasourceInfo());
        schemaQuery.setDatasourceNode(schemaQuery.getDatasourceInfo().getDatasourceNodeList());
        List<String> allSchema = datasourceInfoService.getAllSchema(schemaQuery);
        System.out.println(allSchema);
        assert !allSchema.isEmpty();
    }

    /**
     * 测试克隆数据源功能
     */
    @Test
    public void testCloneDatasource() {
        // 1. 准备测试数据：创建并保存一个数据源
        DatasourceInfoSaveDTO saveDTO = new DatasourceInfoSaveDTO();
        DatasourceInfo datasourceInfo = getDatasourceInfo();
        datasourceInfo.setDatasourceName("Clone测试数据源" + System.currentTimeMillis());
        saveDTO.setDatasourceInfo(datasourceInfo);
        saveDTO.setDatasourceNode(datasourceInfo.getDatasourceNodeList());

        // 保存数据源
        String originalId = datasourceInfoService.saveDatasourceInfo(saveDTO);
        System.out.println("原始数据源ID: " + originalId);
        Assert.assertNotNull("原始数据源保存失败", originalId);

        try {
            // 2. 克隆数据源
            String clonedId = datasourceInfoService.cloneDatasource(originalId);
            System.out.println("克隆数据源ID: " + clonedId);
            Assert.assertNotNull("克隆数据源失败", clonedId);
            Assert.assertNotEquals("克隆数据源ID与原始数据源ID相同", originalId, clonedId);

            // 3. 验证克隆结果
            DatasourceInfo originalDatasource = datasourceInfoService.getById(originalId);
            DatasourceInfo clonedDatasource = datasourceInfoService.getById(clonedId);

            // 验证数据源名称
            Assert.assertEquals("克隆数据源名称不正确",
                    originalDatasource.getDatasourceName() + "_副本",
                    clonedDatasource.getDatasourceName());

            // 验证其他属性
            Assert.assertEquals("数据源类型不一致",
                    originalDatasource.getDatasourceTypeId(),
                    clonedDatasource.getDatasourceTypeId());
            Assert.assertEquals("业务系统不一致",
                    originalDatasource.getBusinessSystemId(),
                    clonedDatasource.getBusinessSystemId());
            Assert.assertEquals("驱动ID不一致",
                    originalDatasource.getDatasourceDriverId(),
                    clonedDatasource.getDatasourceDriverId());

            // 验证节点信息
            List<DatasourceNode> originalNodes = datasourceInfoService.getService(DatasourceNode.class).getQuery()
                    .eq("datasourceInfoId", originalId)
                    .eq("delFlag", 0)
                    .list();
            List<DatasourceNode> clonedNodes = datasourceInfoService.getService(DatasourceNode.class).getQuery()
                    .eq("datasourceInfoId", clonedId)
                    .eq("delFlag", 0)
                    .list();

            Assert.assertEquals("节点数量不一致", originalNodes.size(), clonedNodes.size());

            // 验证第一个节点的属性
            if (!originalNodes.isEmpty() && !clonedNodes.isEmpty()) {
                DatasourceNode originalNode = originalNodes.get(0);
                DatasourceNode clonedNode = clonedNodes.get(0);

                Assert.assertEquals("节点名称不一致",
                        originalNode.getNodeName(),
                        clonedNode.getNodeName());
                Assert.assertEquals("节点序号不一致",
                        originalNode.getSerialNumber(),
                        clonedNode.getSerialNumber());

                // 验证配置数据
                List<DatasourceFieldData> originalFieldData = datasourceInfoService.getService(DatasourceFieldData.class).getQuery()
                        .eq("datasourceNodeId", originalNode.getId())
                        .eq("delFlag", 0)
                        .list();
                List<DatasourceFieldData> clonedFieldData = datasourceInfoService.getService(DatasourceFieldData.class).getQuery()
                        .eq("datasourceNodeId", clonedNode.getId())
                        .eq("delFlag", 0)
                        .list();

                Assert.assertEquals("配置数据数量不一致", originalFieldData.size(), clonedFieldData.size());
            }

            System.out.println("克隆数据源测试成功");
        } finally {
            // 4. 清理测试数据
            try {
                datasourceInfoService.delete(originalId, null);
                System.out.println("删除原始数据源成功");
            } catch (Exception e) {
                System.out.println("删除原始数据源失败: " + e.getMessage());
            }
        }
    }

    @Test
    public void testGetDbNameOrSchema() {
        DbAndSchemaVO dbNameOrSchema = datasourceInfoService.getDbNameOrSchema("17182922564480");
        System.out.println(dbNameOrSchema);
    }

    @Test
    public void testGetDatasourceInfoDetailList() {
        DatasourceInfoDetailVO datasourceInfoDetailList = datasourceInfoService.getDatasourceInfoDetailList("17182922564480");
        System.out.println(datasourceInfoDetailList);
    }

    private DatasourceInfo getDatasourceInfo() {
        DatasourceInfo result = new DatasourceInfo();
        result.setDatasourceName("Oracle单元测试");
        result.setDatasourceDriverId("16847792506125");
        result.setDatasourceClassifyId("5");
        result.setDatasourceTypeId("5");
        result.setBusinessSystemId("16793142841472");
        result.setDatasourceNodeSerialNumber("tab0");
        result.setPersonCharge("16618279906304");
        result.setDatasourceNodeList(CollUtil.newArrayList(getDatasourceNode()));
        return result;
    }

    private DatasourceNode getDatasourceNode() {
        DatasourceNode result = new DatasourceNode();
        result.setNodeName("Oracle单元测试1");
        result.setSerialNumber("tab0");
        result.setDatasourceFieldData(getDatasourceFieldData());
        return result;
    }

    private List<DatasourceFieldData> getDatasourceFieldData() {
        List<DatasourceFieldData> result = new ArrayList<>();
        result.add(buildConfigTypeJDBC());
        result.add(buildJdbc());
        result.add(buildUsername());
        result.add(buildPassword());
        return result;
    }

    private DatasourceFieldData buildConfigTypeJDBC() {
        return DatasourceFieldData.builder()
                .label("configType")
                .value("jdbc")
                .build();
    }

    private DatasourceFieldData buildJdbc() {
        return DatasourceFieldData.builder()
                .label("jdbc")
                .value("**********************************************")
                .build();
    }

    private DatasourceFieldData buildUsername() {
        return DatasourceFieldData.builder()
                .label("username")
                .value("system")
                .build();
    }

    private DatasourceFieldData buildPassword() {
        return DatasourceFieldData.builder()
                .label("password")
                .value("joyadata")
                .build();
    }

}
