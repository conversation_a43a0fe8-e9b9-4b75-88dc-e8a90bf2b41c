package com.joyadata.dsc.test;

import com.joyadata.dsc.DscApp;
import com.joyadata.dsc.service.MetadataTableCommonService;
import com.joyadata.util.ApplicationContextHelp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest()
public class MetadataTableCommonServiceTest {

    static {
        ApplicationContextHelp.defaultBaseClass = DscApp.class;
    }

    @Autowired
    private MetadataTableCommonService metadataTableCommonService;

    @Test
    public void testGenerateQuerySql() {
        String id = "3c096311b11a135b2a68ded12c902bc3";
        System.out.println(metadataTableCommonService.generateQuerySql(id));
    }


}
