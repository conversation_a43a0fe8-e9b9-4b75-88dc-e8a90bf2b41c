spring:
  main:
    allow-bean-definition-overriding: true
  aop:
    proxy-target-class: true
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
      enabled: true
  datasource:
    type: ${app_db_pool_type}
    minimum-idle: ${app_db_pool_idle}
    maximum-pool-size: ${app_db_pool_size}
    driverClassName: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${app_db_ip}:${app_db_port}/${app_db_dbname}?${app_db_url_advanced_parameter}
    username: ${app_db_username}
    password: ${app_db_password}

mybatis-plus:
  mapperLocations: classpath*:mapper/**/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
    autoMappingBehavior: PARTIAL
    autoMappingUnknownColumnBehavior: NONE
    logImpl: ${app_db_mybatis_sql_log_impl}
  global-config:
    banner: false
    dbConfig:
      idType: NONE

management:
  endpoints:
    web:
      exposure:
        include: "*"
