# mysql ，旧配置使用default_mysql_db_
default_mysql_db_ip: mysql.dsg.com
default_mysql_db_port: 3306
default_mysql_db_user: root
default_mysql_db_password: Cdyanfa_123456
default_mysql_db_pool_type: com.alibaba.druid.pool.DruidDataSource
default_mysql_db_pool_idle: 3
default_mydefault_mysql_db_pool_size: 20
default_mysql_driver_class_name: com.mysql.cj.jdbc.Driver
default_mysql_url_advanced_parameter: useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&connectTimeout=60000&readTimeout=630000&socketTimeout=60000

# db
default_db_type: mysql
default_db_pool_type: com.alibaba.druid.pool.DruidDataSource
default_db_pool_idle: 3
default_db_pool_size: 20
default_db_ip: mysql.dsg.com
default_db_port: 3306
default_db_username: root
default_db_password: Cdyanfa_123456
default_db_driver_class_name: com.mysql.cj.jdbc.Driver
default_db_url_advanced_parameter: useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&connectTimeout=60000&readTimeout=630000&socketTimeout=60000
default_db_mybatis_sql_log_impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

#app 、#gateway
default_log_enable: false
default_autoddl_enable: false
default_bean_event_enable: false
default_auth_enable: true
default_executor_enable: false
default_app_gateway_http: http://gateway.dsg.com:8858

# kafka
default_kafka_enable: true
default_kafak_ips: kafka.dsg.com:9092
default_kafka_enable_jaas: true
default_kafka_user_jaas: admin
default_kafka_pwd_jaas: Cdyanfa_123456

#redis
default_redis_enable: true
default_redis_ips: redis.dsg.com:7361,redis.dsg.com:7362,redis.dsg.com:7363,redis.dsg.com:7364,redis.dsg.com:7365,redis.dsg.com:7366
default_redis_type: cluster
default_redis_database: 0
default_redis_password: Cdyanfa_123456