# module
project: dedp
module: dsc
version: v1


# app
app_log_enable: ${default_log_enable:false}
app_autoddl_enable: ${default_autoddl_enable:true}
app_bean_event_enable: ${default_bean_event_enable:false}
app_auth_enable: ${default_auth_enable:true}
app_gateway_http: ${default_app_gateway_http:127.0.0.1:8858}
app_executor_enable: ${default_executor_enable:false}

# db
app_db_type: mysql
app_db_pool_type: ${default_mysql_db_pool_type:com.alibaba.druid.pool.DruidDataSource}
app_db_pool_idle: ${default_mysql_db_pool_idle:3}
app_db_pool_size: ${default_mydefault_mysql_db_pool_size:20}

app_db_ip: ${default_mysql_db_ip:127.0.0.1}
app_db_port: ${default_mysql_db_port:3306}
app_db_dbname: business_dsc
app_db_username: ${default_mysql_db_user:root}
app_db_password: ${default_mysql_db_password:root}
app_db_driver_class_name: ${default_mysql_driver_class_name:com.mysql.cj.jdbc.Driver}
app_db_url_advanced_parameter: ${default_mysql_url_advanced_parameter:}

app_db_write_ip: ${default_mysql_db_ip:127.0.0.1}
app_db_write_port: ${default_mysql_db_port:3306}
app_db_write_dbname: joyadata
app_db_write_username: ${default_mysql_db_user:root}
app_db_write_password: ${default_mysql_db_password:root}
app_db_write_driver_class_name: ${default_mysql_driver_class_name:com.mysql.cj.jdbc.Driver}
app_db_write_url_advanced_parameter: ${default_mysql_url_advanced_parameter:}

app_db_read_ip: ${default_mysql_db_ip:127.0.0.1}
app_db_read_port: ${default_mysql_db_port:3306}
app_db_read_dbname: joyadata
app_db_read_username: ${default_mysql_db_user:root}
app_db_read_password: ${default_mysql_db_password:root}
app_db_read_driver_class_name: ${default_mysql_driver_class_name:com.mysql.cj.jdbc.Driver}
app_db_read_url_advanced_parameter: ${default_mysql_url_advanced_parameter:}

app_db_mybatis_sql_log_impl: ${default_db_mybatis_sql_log_impl:org.apache.ibatis.logging.slf4j.Slf4jImpl}

#kafka
app_kafka_enable: ${default_kafka_enable:false}
app_kafka_ips: ${default_kafak_ips:127.0.0.1:9200}
app_kafka_enable_jaas: true
app_kafka_user_jaas: admin
app_kafka_pwd_jaas: Cdyanfa_123456

#redis
app_redis_enable: ${default_redis_enable:false}
app_redis_type: ${default_redis_type:single}
app_redis_ips: ${default_redis_ips:127.0.0.1:6379}
app_redis_password: ${default_redis_password}
app_redis_database: ${default_redis_database:0}

#最大分页参数
app_sys_max_query_limit: 10000000

#获取部门列表接口配置
cms_department_list_url: "http://nginx.dsg.com:8000/dedp/v1/cms/depts"
#获取用户列表接口配置
cms_user_list_url: "http://nginx.dsg.com:8000/dedp/v1/cms/users?withs=deptId,deptName,fullDeptName,fullDeptIds"
#获取业务系统安全等级
dsh_system_security_level_url: "http://nginx.dsg.com:8000/dedp/v1/dah/securityClassifications?sortby=level"
# 数据源插件路径
database:
  # 相对路径，基于启动jar包的配置路径
  plugin_path: /plugins/datasourceX/pluginLibs
  kerberos_path: /plugins/kerberos_tmp
  # 本地idea开发时使用的路径，结合spring.profiles.active来使用
  local_plugin_path: /Users/<USER>/IdeaProjects/joyadata/standard/datasourcex/build/pluginLibs
  local_kerberos_path: /Users/<USER>/IdeaProjects/joyadata/standard/datasourcex/build/kerberos_tmp
  # 服务器插件起始路径 + plugin_path 等于服务器插件所在路径
  plugin_base_path: /dsg/app
  alert_msg_format: com.joyadata.csc.service.alarm.JoyadataAlarmFormat

jasypt:
  encryptor:
    # 加密算法
    algorithm: PBEWITHHMACSHA512ANDAES_256
    # 加密使用的盐
    password: NkVCQUQxMjBFQTI4QjY5NzVFQkYxRUNBRjEzMjc1Nzc=