<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <!-- 获取父pom profiles 版本分支-->
    <property name="profile.active" value="dedp"/>
    <!-- 日志存放路径 -->
<!--    <property name="log.path" value="${user.dir}/logs"/>-->
    <property name="log.path" value="logs"/>
    <!-- 日志输出格式 -->
    <property name="console.log.pattern"
              value="%red(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36})-%red(%line) - %msg%n"/>
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36}-%red(%line) - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${console.log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <!-- 例如：如果此处配置了INFO级别，则后面其他位置即使配置了DEBUG级别的日志，也不会被输出 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
    </appender>
    <!-- 日志级别排序为： TRACE < DEBUG < INFO < WARN < ERROR -->
    <!-- 系统日志输出-debug -->
    <appender name="file_debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/debug.log</file>
        <!-- 循环政策：按照每天和大小生成日志 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--每个文件最大50MB-->
            <maxFileSize>10MB</maxFileSize>
            <!--日志最长保存7天-->
            <maxHistory>7</maxHistory>
            <!--总的日志最多20G-->
            <totalSizeCap>1GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--临界值日志过滤级别配置 -->
<!--        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">&lt;!&ndash; 只打印DEBUG日志 &ndash;&gt;-->
<!--            &lt;!&ndash; 在日志配置级别的基础上过滤掉info级别以下的日志 &ndash;&gt;-->
<!--            <level>DEBUG</level>-->
<!--        </filter>-->
    </appender>

    <!-- 系统日志输出-info -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <!-- 循环政策：按照每天和大小生成日志 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--每个文件最大50MB-->
            <maxFileSize>10MB</maxFileSize>
            <!--日志最长保存15天-->
            <maxHistory>7</maxHistory>
            <!--总的日志最多20G-->
            <totalSizeCap>1GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--临界值日志过滤级别配置 -->
<!--        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">&lt;!&ndash; 只打印WARN以下日志 &ndash;&gt;-->
<!--            &lt;!&ndash; 在日志配置级别的基础上过滤掉info级别以下的日志 &ndash;&gt;-->
<!--            <level>INFO</level>-->
<!--        </filter>-->
    </appender>
    <!-- 系统日志输出-sql -->
    <appender name="file_sql" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sql.log</file>
        <!-- 循环政策：按照每天和大小生成日志 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/sql.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--每个文件最大50MB-->
            <maxFileSize>10MB</maxFileSize>
            <!--日志最长保存30天-->
            <maxHistory>7</maxHistory>
            <!--总的日志最多20G-->
            <totalSizeCap>1GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--临界值日志过滤级别配置 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter"><!-- 只打印WARN以下日志 -->
            <!-- 在日志配置级别的基础上过滤掉info级别以下的日志 -->
            <level>INFO</level>
        </filter>
    </appender>
    <appender name="file_warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/warn.log</file>
        <!-- 循环政策：按照每天和大小生成日志 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/warn.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--每个文件最大50MB-->
            <maxFileSize>10MB</maxFileSize>
            <!--日志最长保存45天-->
            <maxHistory>7</maxHistory>
            <!--总的日志最多20G-->
            <totalSizeCap>1GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--临界值日志过滤级别配置 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter"><!-- 只打印 ERROR 以下日志 -->
            <!-- 在日志配置级别的基础上过滤掉ERROR级别以下的日志 -->
            <level>WARN</level>
        </filter>
    </appender>
    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <!-- 循环政策：按照每天和大小生成日志 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--每个文件最大50MB-->
            <maxFileSize>10MB</maxFileSize>
            <!--日志最长保存60天-->
            <maxHistory>7</maxHistory>
            <!--总的日志最多20G-->
            <totalSizeCap>1GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--只打印ERROR级别的日志-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
        </filter>
    </appender>

    <!--
        <logger>用来设置某一个包或者具体的某一个类的日志打印级别、
        以及指定<appender>。<logger>仅有一个name属性，
        一个可选的level和一个可选的addtivity属性。
        name:用来指定受此logger约束的某一个包或者具体的某一个类。
        level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，
              还有一个特俗值INHERITED或者同义词NULL，代表强制执行上级的级别。
              如果未设置此属性，那么当前logger将会继承上级的级别。
        addtivity:是否向上级logger传递打印信息。默认是true。
    -->

    <springProfile name="local">
        <!--root节点 全局日志级别，用来指定最基础的日志输出级别-->
        <!--系统操作日志,不用区分开发和生产debug会有日志回收时间，console只打印INFO及以上的日志-->
        <root level="info">
            <appender-ref ref="console"/>
            <appender-ref ref="file_debug"/>
            <appender-ref ref="file_sql"/>
            <appender-ref ref="file_info"/>
            <appender-ref ref="file_warn"/>
            <appender-ref ref="file_error"/>
        </root>

        <!-- 子节点向上级传递 局部日志级别-->
        <logger level="WARN" name="org.springframework"/>
        <logger level="WARN" name="com.netflix"/>
        <logger level="DEBUG" name="org.hibernate.SQL"/>
    </springProfile>
    <springProfile name="prod">
        <root level="info">
            <appender-ref ref="file_info"/>
            <appender-ref ref="file_warn"/>
            <appender-ref ref="file_error"/>
        </root>
    </springProfile>
    <!--专门针对SQL打印内容输出一个文件-->
    <logger name="p6spy" level="info" additivity="false">
        <appender-ref ref="file_sql"/>
    </logger>
</configuration>
