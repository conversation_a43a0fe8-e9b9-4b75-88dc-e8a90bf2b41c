package com.joyadata.dsc.processor;

import com.joyadata.dsc.model.datasoure.DatasourceFieldData;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.DatasourceNode;
import com.joyadata.dsc.model.datasoure.dto.DatasourceInfoSaveDTO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectProgressVO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectVO;

import java.util.List;

public interface DatasourceProcessor {

    void process(DatasourceInfo datasourceInfo, DatasourceNode datasourceNode, List<DatasourceFieldData> datasourceFieldData);

    DatasourceConnectProgressVO connect(DatasourceInfoSaveDTO datasourceInfoSaveDTO);

    default void processDatasourceConnect(List<DatasourceConnectVO> datasourceConnectVOList, String type, String msg, String checkStatus) {
        for (DatasourceConnectVO datasourceConnectVO : datasourceConnectVOList) {
            if (datasourceConnectVO.getType().equals(type)) {
                datasourceConnectVO.setCheckStatus(checkStatus);
                datasourceConnectVO.setMsg(msg);
                break;
            }
        }
    }

    default DatasourceFieldData buildDatasourceFieldData(String datasource, String label, String value) {
        return DatasourceFieldData.builder().datasourceNodeId(datasource).label(label).value(value).build();
    }
}