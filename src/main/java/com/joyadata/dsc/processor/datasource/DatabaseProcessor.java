package com.joyadata.dsc.processor.datasource;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.exception.RdosDefineException;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.joyadata.dsc.model.datasoure.DatasourceFieldData;
import com.joyadata.dsc.model.datasoure.DatasourceFormField;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.DatasourceNode;
import com.joyadata.dsc.model.datasoure.dto.DatasourceInfoSaveDTO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectProgressVO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectVO;
import com.joyadata.dsc.processor.DatasourceProcessor;
import com.joyadata.dsc.utils.DatasourceUtil;
import com.joyadata.dsc.utils.TelnetUtils;
import com.joyadata.dsc.utils.jdbc.JdbcUrlBuilderUtil;
import com.joyadata.dsc.utils.jdbc.JdbcUrlParser;
import com.joyadata.exception.AppErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据库
 */
@Slf4j
public class DatabaseProcessor implements DatasourceProcessor {

    @Override
    public void process(DatasourceInfo datasourceInfo, DatasourceNode datasourceNode, List<DatasourceFieldData> datasourceFieldData) {
        buildDatasourceNodeJdbcUrl(datasourceInfo, datasourceNode, datasourceFieldData);
        datasourceNode.openGetJdbcUrl();
    }

    @Override
    public DatasourceConnectProgressVO connect(DatasourceInfoSaveDTO datasourceInfoSaveDTO) {
        DatasourceConnectProgressVO result = new DatasourceConnectProgressVO();
        List<DatasourceConnectVO> datasourceConnectVOList = DatasourceConnectVO.buildDatasourceConnectVO();
        DatasourceDTO datasourceDTO = DatasourceUtil.getDatasourceDTO(datasourceInfoSaveDTO.getDatasourceInfo(), datasourceInfoSaveDTO.getDatasourceNode());
        DatasourceNode datasourceNode = DatasourceUtil.obtainEffectiveNode(datasourceInfoSaveDTO.getDatasourceInfo().getDatasourceNodeSerialNumber(), CollUtil.isNotEmpty(datasourceInfoSaveDTO.getDatasourceNode()) ? datasourceInfoSaveDTO.getDatasourceNode() : datasourceInfoSaveDTO.getDatasourceInfo().getDatasourceNodeList());
        result.setDatasourceConnectVOList(datasourceConnectVOList);
        List<JdbcUrlBuilderUtil.HostPort> hostPortList = JdbcUrlParser.extractHostPorts(datasourceNode.getJdbcUrl());
        if (CollUtil.isEmpty(hostPortList)) {
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.HOST_NETWORK, "获取服务器地址失败", DatasourceConnectVO.FAIL);
        } else {
            for (JdbcUrlBuilderUtil.HostPort hostPort : hostPortList) {
                try {
                    boolean reachable = TelnetUtils.isReachable(hostPort.getHost(), hostPort.getPort());
                    if (!reachable) {
                        processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.HOST_NETWORK, "网络不可达", DatasourceConnectVO.FAIL);
                        return result;
                    }
                } catch (IllegalArgumentException e) {
                    processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.HOST_NETWORK, "请检查端口和域名填写的值", DatasourceConnectVO.FAIL);
                    return result;
                }
            }
        }
        processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.HOST_NETWORK, null, DatasourceConnectVO.SUCCESS);
        try {
            Boolean b = DatasourceUtils.checkConnectionWithConf(datasourceDTO, null, null);
            if (!b) {
                processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.USERNAME_PASSWORD, "用户名或密码错误", DatasourceConnectVO.FAIL);
                return result;
            }
        } catch (Exception e) {
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.USERNAME_PASSWORD, e.getMessage(), DatasourceConnectVO.FAIL);
            return result;
        }
        processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.USERNAME_PASSWORD, null, DatasourceConnectVO.SUCCESS);
        Integer maxConnections = DatasourceUtils.getMaxConnections(datasourceDTO);
        if (maxConnections == null) {
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.MAX_CONNECTION, "获取最大连接数失败", DatasourceConnectVO.FAIL);
        } else {
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.MAX_CONNECTION, "最大连接数：" + maxConnections, DatasourceConnectVO.SUCCESS);
        }
        Boolean metadataPrivileges = DatasourceUtils.getMetadataPrivileges(datasourceDTO);
        if (metadataPrivileges) {
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.METADATA_PERMISSION, null, DatasourceConnectVO.SUCCESS);
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.FIELD_PERMISSION, null, DatasourceConnectVO.SUCCESS);
        } else {
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.METADATA_PERMISSION, "未获取到元数据权限", DatasourceConnectVO.FAIL);
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.FIELD_PERMISSION, "未获取到字段权限", DatasourceConnectVO.FAIL);
        }
        return result;
    }

    /**
     * 构建数据源节点的 jdbcUrl
     * @param datasourceInfo 数据源信息
     * @param datasourceNode 数据源节点
     * @param datasourceFieldData 数据源节点配置数据
     */
    private void buildDatasourceNodeJdbcUrl(DatasourceInfo datasourceInfo, DatasourceNode datasourceNode, List<DatasourceFieldData> datasourceFieldData) {
        DatasourceFieldData jdbcUrl = DatasourceFieldData.builder().build();
        jdbcUrl.setDatasourceNodeId(datasourceNode.getId());
        jdbcUrl.setLabel(DatasourceFormField.PropsKeys.JDBC_URL);
        boolean isJdbcUrl = false;
        for (DatasourceFieldData datasourceFieldDatum : datasourceFieldData) {
            if (DatasourceFormField.PropsKeys.JDBC.equals(datasourceFieldDatum.getLabel())) {
                jdbcUrl.setValue(datasourceFieldDatum.getValue());
                isJdbcUrl = true;
                break;
            }
        }
        if (!isJdbcUrl) {
            JSONArray host = new JSONArray();
            String dbName = null;
            for (DatasourceFieldData datasourceFieldDatum : datasourceFieldData) {
                if (DatasourceFormField.PropsKeys.HOST_PORT.equals(datasourceFieldDatum.getLabel())) {
                    try {
                        host = JSONArray.parseArray(datasourceFieldDatum.getValue());
                    } catch (Exception e) {
                        log.error("JDBC URL 构建失败", e);
                        throw new AppErrorException("服务器地址传入参数有误");
                    }
                }
                if (DatasourceFormField.PropsKeys.DB_NAME.equals(datasourceFieldDatum.getLabel())) {
                    dbName = datasourceFieldDatum.getValue();
                }
            }
            if (host.isEmpty()) {
                throw new AppErrorException("服务器地址传入参数有误");
            }
            List<JdbcUrlBuilderUtil.HostPort> hostPortList = new ArrayList<>();
            for (int i = 0; i < host.size(); i++) {
                JSONObject hostPort = host.getJSONObject(i);
                String hostPortHost = hostPort.getString(DatasourceFormField.PropsKeys.HOST_PORT_HOST_KEY);
                String hostPortPort = hostPort.getString(DatasourceFormField.PropsKeys.HOST_PORT_PORT_KEY);
                if (StringUtils.isEmpty(hostPortHost) || StringUtils.isEmpty(hostPortPort)) {
                    throw new AppErrorException("服务器地址传入参数有误");
                }
                hostPortList.add(new JdbcUrlBuilderUtil.HostPort(hostPortHost, Integer.parseInt(hostPortPort)));
            }
            jdbcUrl.setValue(JdbcUrlBuilderUtil.buildJdbcUrl(datasourceInfo.getDataTypeCode(), hostPortList, dbName, null));
        }
        datasourceNode.setJdbcUrl(jdbcUrl.getValue());
        datasourceNode.setServerAddress(JdbcUrlParser.extractHostPorts(jdbcUrl.getValue()).toString().replace("[", "").replace("]", ""));
        datasourceFieldData.add(jdbcUrl);
    }
}
