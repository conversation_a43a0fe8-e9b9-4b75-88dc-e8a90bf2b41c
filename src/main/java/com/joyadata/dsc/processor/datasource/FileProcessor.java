package com.joyadata.dsc.processor.datasource;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.joyadata.dsc.model.datasoure.DatasourceFieldData;
import com.joyadata.dsc.model.datasoure.DatasourceFormField;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.DatasourceNode;
import com.joyadata.dsc.model.datasoure.dto.DatasourceInfoSaveDTO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectProgressVO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectVO;
import com.joyadata.dsc.processor.DatasourceProcessor;
import com.joyadata.dsc.utils.DatasourceUtil;
import com.joyadata.dsc.utils.TelnetUtils;
import com.joyadata.dsc.utils.jdbc.JdbcUrlParser;
import com.joyadata.exception.AppErrorException;

import java.util.List;

/**
 * 文件
 */
public class FileProcessor implements DatasourceProcessor {

    @Override
    public void process(DatasourceInfo datasourceInfo, DatasourceNode datasourceNode, List<DatasourceFieldData> datasourceFieldData) {
        // 文件类型需要获取host和port
        String host = null;
        String port = null;
        String protocol = null;
        for (DatasourceFieldData datasourceFieldDatum : datasourceFieldData) {
            if (DatasourceFormField.PropsKeys.HOST_PORT.equals(datasourceFieldDatum.getLabel())) {
                JSONArray hostPortArr = JSONArray.parseArray(datasourceFieldDatum.getValue());
                for (int i = 0; i < hostPortArr.size(); i++) {
                    JSONObject hostPort = hostPortArr.getJSONObject(i);
                    host = hostPort.getString(DatasourceFormField.PropsKeys.HOST_PORT_HOST_KEY);
                    port = hostPort.getString(DatasourceFormField.PropsKeys.HOST_PORT_PORT_KEY);
                }
                break;
            }
        }
        if (StringUtils.isEmpty(host)) {
            throw new AppErrorException("服务器地址传入参数有误");
        }
        // port 为空时根据 ftp/sftp 赋予默认值
        if (DataSourceTypeEnum.FTP.getVal().equals(datasourceInfo.getDataTypeCode())) {
            if (StringUtils.isEmpty(port)) {
                port = "21";
            }
            protocol = DataSourceTypeEnum.FTP.getDataType();
        } else if (DataSourceTypeEnum.SFTP.getVal().equals(datasourceInfo.getDataTypeCode())) {
            if (StringUtils.isEmpty(port)) {
                port = "22";
            }
            protocol = DataSourceTypeEnum.SFTP.getDataType();
        }
        datasourceFieldData.add(buildDatasourceFieldData(datasourceNode.getId(), DatasourceFormField.PropsKeys.PROTOCOL, protocol));
        datasourceFieldData.add(buildDatasourceFieldData(datasourceNode.getId(), DatasourceFormField.PropsKeys.HOST_PORT_HOST_KEY, host));
        datasourceFieldData.add(buildDatasourceFieldData(datasourceNode.getId(), DatasourceFormField.PropsKeys.HOST_PORT_PORT_KEY, port));
        datasourceNode.setServerAddress(host  + ":" + port);
    }

    @Override
    public DatasourceConnectProgressVO connect(DatasourceInfoSaveDTO datasourceInfoSaveDTO) {
        DatasourceConnectProgressVO result = new DatasourceConnectProgressVO();
        List<DatasourceConnectVO> datasourceConnectVOList = DatasourceConnectVO.buildDatasourceConnectVO();
        result.setDatasourceConnectVOList(datasourceConnectVOList);
        DatasourceDTO datasourceDTO = DatasourceUtil.getDatasourceDTO(datasourceInfoSaveDTO.getDatasourceInfo(), datasourceInfoSaveDTO.getDatasourceNode());
        DatasourceNode datasourceNode = DatasourceUtil.obtainEffectiveNode(datasourceInfoSaveDTO.getDatasourceInfo().getDatasourceNodeSerialNumber(), CollUtil.isNotEmpty(datasourceInfoSaveDTO.getDatasourceNode()) ? datasourceInfoSaveDTO.getDatasourceNode() : datasourceInfoSaveDTO.getDatasourceInfo().getDatasourceNodeList());
        String host = getHostOrPort(datasourceNode, DatasourceFormField.PropsKeys.HOST_PORT_HOST_KEY);
        Integer port = Integer.valueOf(getHostOrPort(datasourceNode, DatasourceFormField.PropsKeys.HOST_PORT_PORT_KEY));
        try {
            boolean reachable = TelnetUtils.isReachable(host, port);
            if (!reachable) {
                processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.HOST_NETWORK, "网络不可达", DatasourceConnectVO.FAIL);
                return result;
            } else {
                processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.HOST_NETWORK, null, DatasourceConnectVO.SUCCESS);
            }
        } catch (IllegalArgumentException e) {
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.HOST_NETWORK, "请检查端口和域名填写的值", DatasourceConnectVO.FAIL);
            return result;
        }
        try {
            Boolean checkUser = DatasourceUtils.checkConnectionWithConf(datasourceDTO, null, null);
            if (!checkUser) {
                processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.USERNAME_PASSWORD, "用户名或密码错误", DatasourceConnectVO.FAIL);
                return result;
            }
        } catch (Exception e) {
            processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.USERNAME_PASSWORD, e.getMessage(), DatasourceConnectVO.FAIL);
            return result;
        }
        processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.USERNAME_PASSWORD, null, DatasourceConnectVO.SUCCESS);
        processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.MAX_CONNECTION, null, DatasourceConnectVO.SUCCESS);
        processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.METADATA_PERMISSION, null, DatasourceConnectVO.SUCCESS);
        processDatasourceConnect(datasourceConnectVOList, DatasourceConnectVO.FIELD_PERMISSION, null, DatasourceConnectVO.SUCCESS);
        return result;
    }

    private String getHostOrPort(DatasourceNode datasourceNode, String key) {
        for (DatasourceFieldData datasourceFieldDatum : datasourceNode.getDatasourceFieldData()) {
            if (key.equals(datasourceFieldDatum.getLabel())) {
                return datasourceFieldDatum.getValue();
            }
        }
        return null;
    }

}
