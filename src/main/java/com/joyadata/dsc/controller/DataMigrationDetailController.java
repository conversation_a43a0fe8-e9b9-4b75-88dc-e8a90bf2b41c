package com.joyadata.dsc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.dsc.model.migration.DataMigrationDetail;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据迁移
 * <AUTHOR>
 */
@RestController
@CrossOrigin
@RequestMapping("/dataMigrationDetail")
public class DataMigrationDetailController extends BaseController<DataMigrationDetail> {



} 