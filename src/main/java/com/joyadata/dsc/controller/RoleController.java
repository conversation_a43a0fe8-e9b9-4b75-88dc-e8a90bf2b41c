package com.joyadata.dsc.controller;

import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.vo.RoleVO;
import com.joyadata.dsc.service.RoleService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName RoleController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/12 13:48
 * @Version 1.0
 **/
@RestController
@CrossOrigin
@RequestMapping("/role")
public class RoleController {

    @Autowired
    private RoleService roleService;

    @Auth
    @GetMapping("/list")
    public Response<List<RoleVO>> getRoles(String keywords,
                                           @RequestParam String datasourceInfoId,
                                           <PERSON><PERSON><PERSON> checked, Integer page, Integer pager) {
        Integer total = roleService.getRoleTotal(keywords, datasourceInfoId, checked);
        List<RoleVO> users = roleService.getRoles(keywords, datasourceInfoId, checked, page, pager);
        return ResponseFactory.makeSuccess(users, page, pager, total);
    }
}
