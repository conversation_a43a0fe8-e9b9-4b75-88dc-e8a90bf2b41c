package com.joyadata.dsc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.dsc.model.metadata.MetadataTableCommon;
import com.joyadata.dsc.service.MetadataTableCommonService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@RestController
@CrossOrigin
@RequestMapping("/metadataTableCommon")
public class MetadataTableCommonController extends BaseController<MetadataTableCommon> {

    @Autowired
    private MetadataTableCommonService metadataTableCommonService;

    /**
     *  根据tableId获取元数据信息-索引信息
     */
    @GetMapping("/getMetadataTableCommonByTableId")
    public Response getMetadataTableCommonByTableId(@RequestParam String metadataTableCommonId,
                                                    @RequestParam(required = false) Integer page,
                                                    @RequestParam(required = false) Integer pager) {
        List<MetadataTableCommon> indexByTableId = metadataTableCommonService.getIndexByTableId(metadataTableCommonId, page, pager);
        return ResponseFactory.makeSuccess(indexByTableId);
    }

    /**
     * 根据元数据表ID生成查询语句
     * @param metadataTableCommonId 元数据表ID
     * @return 生成的查询语句
     */
    @GetMapping("/generateQuerySql/{metadataTableCommonId}")
    public Response<String> generateQuerySql(@PathVariable String metadataTableCommonId) {
        try {
            String sql = metadataTableCommonService.generateQuerySql(metadataTableCommonId);
            return ResponseFactory.makeSuccess(sql);
        } catch (Exception e) {
            return ResponseFactory.makeError(e.getMessage());
        }
    }
}
