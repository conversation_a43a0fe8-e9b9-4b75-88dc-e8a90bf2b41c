package com.joyadata.dsc.controller;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.business.BusinessSystem;
import com.joyadata.dsc.model.business.BusinessSystemImportErrorVO;
import com.joyadata.dsc.model.business.BusinessSystemQuery;
import com.joyadata.dsc.service.BusinessSystemService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@RestController
@CrossOrigin
@RequestMapping("/business")
public class BusinessSystemController extends BaseController<BusinessSystem> {

    @Autowired
    private BusinessSystemService businessSystemService;


    /**
     * 判断业务名称是否重复
     */
    @Auth
    @PostMapping("/validBusinessSystemName")
    public Response validBusinessSystemName(@RequestBody BusinessSystem businessSystem) {
        JSONObject jsonObject = businessSystemService.validBusinessSystemName(businessSystem);
        return ResponseFactory.makeSuccess(jsonObject);
    }
    /**
     * 获取业务系统列表 但是不含所传业务系统id
     * @return
     */
    @Auth
    @GetMapping("/getBusinessSystemList")
    public Response getBusinessSystemList(@RequestParam(required = false) String id) {
        List<BusinessSystem> businessSystems = businessSystemService.getBusinessSystemList(id);
        return ResponseFactory.makeSuccess(businessSystems);
    }

    /**
     * 批量强制删除
     * @param ids
     * @return
     */
    @Auth
    @DeleteMapping("/batchDelete")
    public Response batchDelete(@RequestParam List<String> ids) {
        List<BusinessSystem> businessSystems = businessSystemService.batchDelete(ids);
        return ResponseFactory.makeSuccess(businessSystems);
    }

    /**
     * 业务系统导出模板
     */
    @Auth
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        businessSystemService.exportTemplate(response);
    }

    /**
     * 业务系统导出
     */
    @Auth
    @PostMapping("/exportBusiness")
    public void exportBusiness(@RequestBody(required = false) BusinessSystemQuery query, HttpServletResponse response) {
        businessSystemService.export(query,response);
    }

    /**
     * 业务系统-批量导入
     */
    @Auth
    @PostMapping("/businessImport")
    public Response businessImport(MultipartFile file) {
        try {
            List<BusinessSystemImportErrorVO> businessSystemImportErrorVoS = businessSystemService.businessImport(file);
            return ResponseFactory.makeSuccess(businessSystemImportErrorVoS);
        }catch (Exception e){
            if(e.getMessage() != null && e.getMessage().contains("datasourceManager.uploadinfoLimit")){
                return ResponseFactory.makeWarning("上传文件格式不满足要求，请上传xlsx、xls后缀的文件");
            }
             return ResponseFactory.makeError(e.getMessage());
        }
    }
    /**
     * 业务系统-导出错误的业务系统数据
     *
     */
    @Auth
    @PostMapping("/exportBusinessError")
    public void exportBusinessError(@RequestBody (required = false)  List<BusinessSystemImportErrorVO> businessSystemImportVoS,HttpServletResponse response) {
        businessSystemService.exportBusinessSystemErrorData(businessSystemImportVoS,response);
    }

    /**
     * 业务系统-获取系统等级
     */
    @Auth
    @GetMapping("/getSystemSecurityLevelList")
    public Response getSystemSecurityLevelList() {
        List<JSONObject> jsonObjects = businessSystemService.getSystemSecurityLevelList();
        return ResponseFactory.makeSuccess(jsonObjects);
    }

    /**
     * 业务系统-批量彻底删除业务系统
     */
    @Auth
    @PostMapping("/batchDeleteBusinessSystem")
    public Response batchDeleteBusinessSystem(@RequestBody List<String> ids) {
        List<BusinessSystem> businessSystems = businessSystemService.batchDeleteBusinessSystem(ids);
        return ResponseFactory.makeSuccess(businessSystems);
    }
}
