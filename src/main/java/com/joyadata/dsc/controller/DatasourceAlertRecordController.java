package com.joyadata.dsc.controller;

import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.vo.AlarmRecordVO;
import com.joyadata.dsc.service.DatasourceAlertRecordService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName DatasourceAlertRecordController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/14 16:30
 * @Version 1.0
 **/
@RestController
@CrossOrigin
@RequestMapping("/datasourceAlertRecord")
public class DatasourceAlertRecordController {
    @Autowired
    private DatasourceAlertRecordService datasourceAlertRecordService;

    @Auth
    @GetMapping("/list")
    public Response<List<AlarmRecordVO>> getDatasourceAlertRecords(String keywords, String datasourceInfoId,
                                                                   String alarmStrategyId, String eventCode,
                                                                   String alertLevel, Integer page, Integer pager) {

        Integer total = datasourceAlertRecordService.getAlertRecordTotal(keywords, datasourceInfoId, alarmStrategyId, eventCode, alertLevel);
        List<AlarmRecordVO> alarmRecordList = datasourceAlertRecordService.getAlarmRecordList(keywords, datasourceInfoId, alarmStrategyId, eventCode, alertLevel, page, pager);
        return ResponseFactory.makeSuccess(alarmRecordList, page, pager, total);
    }
}
