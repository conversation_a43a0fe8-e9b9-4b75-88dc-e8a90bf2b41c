package com.joyadata.dsc.controller;

import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.ClassifyTree;
import com.joyadata.dsc.model.datasoure.DatasourceClassify;
import com.joyadata.dsc.service.DatasourceClassifyService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据源分类控制器
 * <AUTHOR>
 * @date 2024/9/13
 */
@RestController
@CrossOrigin
@RequestMapping("/classify")
public class DatasourceClassifyController {

    @Autowired
    private DatasourceClassifyService datasourceClassifyService;

    /**
     * 获取数据源分类树
     * @return 数据源分类树
     */
    @GetMapping("/tree")
    public Response<List<ClassifyTree>> tree() {
        List<ClassifyTree> tree = datasourceClassifyService.tree();
        return ResponseFactory.makeSuccess(tree);
    }
    
    /**
     * 获取带图标信息的数据源分类树
     * @return 带图标信息的数据源分类树
     */
    @Auth
    @GetMapping("/treeWithIcons")
    public Response<List<ClassifyTree>> treeWithIcons() {
        List<ClassifyTree> tree = datasourceClassifyService.treeWithIcons();
        return ResponseFactory.makeSuccess(tree);
    }
    
    /**
     * 获取所有数据源分类列表
     * @return 所有数据源分类列表
     */
    @Auth
    @GetMapping("/listAll")
    public Response<List<DatasourceClassify>> listAll() {
        List<DatasourceClassify> classifies = datasourceClassifyService.getQuery()
                .eq("delFlag", Boolean.FALSE)
                .sortby("classifyName")
                .list();
        return ResponseFactory.makeSuccess(classifies);
    }
}
