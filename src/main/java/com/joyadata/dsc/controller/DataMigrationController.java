package com.joyadata.dsc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.DatasourceType;
import com.joyadata.dsc.model.migration.DataMigration;
import com.joyadata.dsc.model.migration.dto.DataMigrationDTO;
import com.joyadata.dsc.service.DataMigrationService;
import com.joyadata.dsc.service.DatasourceTypeService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据迁移
 * <AUTHOR>
 */
@RestController
@CrossOrigin
@RequestMapping("/dataMigration")
public class DataMigrationController extends BaseController<DataMigration> {
    @Autowired
    private DataMigrationService dataMigrationService;

    /**
     * 导出模板
     * @return
     */
    @Auth
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        dataMigrationService.downloadTemplate(response);
    }

    /**
     * 数据源迁移导出
     */
    @Auth
    @PostMapping("/export")
    public void export(@RequestBody DataMigrationDTO dataMigrationDTO, HttpServletResponse response) {
        dataMigrationService.exportDatasourceInfo(dataMigrationDTO, response);
    }

    /**
     * 数据源迁移导入
     */
    @Auth
    @PostMapping("/import")
    public Response importDatasourceInfo(@ModelAttribute DataMigration dataMigration,
                                         @RequestPart("file") MultipartFile file) {
        dataMigrationService.importDatasourceInfo(dataMigration, file);
        return ResponseFactory.makeSuccess("");
    }
    /**
     * 下载文件
     */
    @Auth
    @GetMapping("/download/{filePath}")
    public void downloadFile(@PathVariable String filePath) {
        dataMigrationService.downloadFile(filePath);
    }


    /**
     * 数据迁移 批量删除
     */
    @Auth
    @PostMapping("/batchDelete")
    public Response batchDelete(@RequestBody List<String> ids) {
        dataMigrationService.batchDelete(ids);
        return ResponseFactory.makeSuccess("");
    }

} 