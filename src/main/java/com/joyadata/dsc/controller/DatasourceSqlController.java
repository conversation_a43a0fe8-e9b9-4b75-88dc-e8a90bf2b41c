package com.joyadata.dsc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.enums.DatasourceTypeDatabase;
import com.joyadata.dsc.model.datasoure.DatasourceAuth;
import com.joyadata.dsc.model.datasoure.DatasourceSql;
import com.joyadata.dsc.model.datasoure.dto.DatasourceAuthDTO;
import com.joyadata.dsc.model.datasoure.dto.DatasourceSqlDTO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceAuthVO;
import com.joyadata.dsc.model.datasoure.vo.PreviewVO;
import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import com.joyadata.dsc.service.DatasourceInAuthService;
import com.joyadata.dsc.service.DatasourceSqlService;
import com.joyadata.dsc.utils.PaginationUtil;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@RestController
@CrossOrigin
@RequestMapping("/datasourceSql")
public class DatasourceSqlController extends BaseController<DatasourceSql> {


    @Autowired
    private DatasourceSqlService datasourceSqlService;


    /**
     * 预览数据--执行sql语句
     */
    @PostMapping("/preview")
    @Auth
    public Response preview(@RequestBody DatasourceSqlDTO datasourceSqlDTO) {
        PreviewVO previewVO = new PreviewVO();
        List<Map<String, Object>> list = datasourceSqlService.preview(datasourceSqlDTO);
        previewVO.setList(list);
        return ResponseFactory.makeSuccess(previewVO);
    }

    /**
     * 校验sql
     */
    @PostMapping("/parseSqlDDL")
    @Auth
    public Response parseSqlDDL(@RequestBody DatasourceSqlDTO datasourceSqlDTO) {
        Boolean flag = datasourceSqlService.parseSqlDDL(datasourceSqlDTO.getSql(),datasourceSqlDTO.getDbType());
        return ResponseFactory.makeSuccess(flag);
    }


}
