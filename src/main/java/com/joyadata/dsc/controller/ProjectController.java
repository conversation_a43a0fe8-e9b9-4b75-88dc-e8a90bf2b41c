package com.joyadata.dsc.controller;

import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.vo.ProjectVO;
import com.joyadata.dsc.service.ProjectService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName ProjectController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/12 14:31
 * @Version 1.0
 **/
@RestController
@CrossOrigin
@RequestMapping("/project")
public class ProjectController {

    @Autowired
    private ProjectService projectService;

    @Auth
    @GetMapping("/list")
    public Response<List<ProjectVO>> getProjects(String keywords,
                                                 @RequestParam String datasourceInfoId,
                                                 <PERSON><PERSON><PERSON> checked, Integer page, Integer pager) {
        Integer total = projectService.getProjectTotal(keywords, datasourceInfoId, checked);
        List<ProjectVO> users = projectService.getProjects(keywords, datasourceInfoId, checked, page, pager);
        return ResponseFactory.makeSuccess(users, page, pager, total);
    }
}
