package com.joyadata.dsc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.enums.DatasourceTypeDatabase;
import com.joyadata.dsc.model.datasoure.DatasourceAuth;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.dto.DatasourceAuthDTO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceAuthVO;
import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import com.joyadata.dsc.service.DatasourceInAuthService;
import com.joyadata.dsc.utils.PaginationUtil;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@RestController
@CrossOrigin
@RequestMapping("/datasourceAuth")
public class DatasourceAuthController extends BaseController<DatasourceAuth> {


    @Autowired
    private DatasourceInAuthService datasourceInAuthService;


    /**
     * 是否采集
     * @param datasourceInfoId
     * @return
     */
    @Auth
    @GetMapping("/isCollection")
    public Response<Boolean> isCollection(@RequestParam String datasourceInfoId) {
        Boolean collection = datasourceInAuthService.isCollection(datasourceInfoId);
        return ResponseFactory.makeSuccess(collection);
    }


    @Auth
    @GetMapping("/getDbNameOrSchemaNode")
    public Response<List<MetadataNodeVO>> getDbNameOrSchemaNode(@RequestParam String datasourceInfoId,
                                                                @RequestParam(required = false)String keywords) {
        List<MetadataNodeVO> metadataNodeVOS = datasourceInAuthService.getDbNameOrSchemaNode(datasourceInfoId,keywords);
        return ResponseFactory.makeSuccess(metadataNodeVOS);
    }
    /**
     * 采集后：dbname 或者schema
     * @param datasourceInfoId
     * @return
     */
    @Auth
    @GetMapping("/getDbNameOrSchemaList")
    public Response<List<DatasourceAuthVO>> getDbNameOrSchemaList(@RequestParam String datasourceInfoId,
                                                                  @RequestParam(required = false) String keywords,
                                                                  @RequestParam(required = false) Integer page,
                                                                  @RequestParam(required = false) Integer pager) {
        List<DatasourceAuthVO> dbNameOrSchemaList = datasourceInAuthService.getDbNameOrSchemaList(datasourceInfoId, keywords);
        if(page!=null&&pager!=null){
            //分页
            List<DatasourceAuthVO> paginate = PaginationUtil.paginate(dbNameOrSchemaList, page, pager);
            return ResponseFactory.makeSuccess(paginate, page, pager,dbNameOrSchemaList.size());
        }
        return ResponseFactory.makeSuccess(dbNameOrSchemaList);
    }

    @Auth
    @PostMapping("/getTableList")
    public Response getTableList(@RequestBody DatasourceAuthDTO auth,
                                 @RequestParam(required = false) Integer page,
                                 @RequestParam(required = false) Integer pager) {
        if(page==null||pager==null){
            page=0;
            pager=10;
        }
        List<DatasourceAuthVO> tableList = datasourceInAuthService.getTableList(auth,page,pager);
        Integer tableTotal = datasourceInAuthService.getTableTotal(auth);
        return ResponseFactory.makeSuccess(tableList,page,pager,tableTotal);
    }



    //批量新增数据源授权
    @Auth
    @PostMapping("/addBatch")
    public Response addBatch(@RequestBody DatasourceAuthDTO datasourceAuthDTO) {
      datasourceInAuthService.addBatch(datasourceAuthDTO);
        return ResponseFactory.makeSuccess("");
    }

    //取消数据源授权
    @Auth
    @PostMapping("/deleteBatch")
    public Response deleteBatch(@RequestBody DatasourceAuthDTO datasourceAuthDTO) {
        datasourceInAuthService.deleteBatch(datasourceAuthDTO);
        return ResponseFactory.makeSuccess("");
    }

    /**
     * 获取授权列表
     * @param auth
     * @param page
     * @param pager
     * @return
     */
    @Auth
    @PostMapping("/getAuthList")
    public Response getAuthList(@RequestBody DatasourceAuthDTO auth,
                                @RequestParam(required = false) Integer page,
                                @RequestParam(required = false) Integer pager) {
        List<DatasourceAuthVO> tableList = datasourceInAuthService.getAuthList(auth);
        if(page!=null&&pager!=null){
            //分页
            List<DatasourceAuthVO> paginate = PaginationUtil.paginate(tableList, page, pager);
            return ResponseFactory.makeSuccess(paginate, page, pager,tableList.size());
        }
        return ResponseFactory.makeSuccess(tableList);
    }

    //快速选表：判断表是否存在
    @Auth
    @PostMapping("/getNotExistTable")
    public Response getNotExistTable(@RequestBody DatasourceAuthDTO datasourceAuthDTO) {
        List<String> notExistTable = datasourceInAuthService.getNotExistTable(datasourceAuthDTO);
        return ResponseFactory.makeSuccess(notExistTable);
    }

}
