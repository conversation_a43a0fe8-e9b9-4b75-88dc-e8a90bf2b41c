package com.joyadata.dsc.controller;

import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.vo.UserVO;
import com.joyadata.dsc.service.UserService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName UserController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/12 10:53
 * @Version 1.0
 **/
@RestController
@CrossOrigin
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Auth
    @GetMapping("/list")
    public Response<List<UserVO>> getUsers(String deptId, String keywords,
                                           @RequestParam String datasourceInfoId,
                                           Bo<PERSON>an checked, Integer page, Integer pager) {
        Integer total = userService.getUserTotal(deptId, keywords, datasourceInfoId, checked);
        List<UserVO> users = userService.getUsers(deptId, keywords, datasourceInfoId, checked, page, pager);
        return ResponseFactory.makeSuccess(users, page, pager, total);
    }
}
