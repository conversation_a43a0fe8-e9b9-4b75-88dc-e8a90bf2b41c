package com.joyadata.dsc.controller;

import cn.hutool.core.collection.CollUtil;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.metadata.MetadataTaskRecord;
import com.joyadata.dsc.model.metadata.vo.MetadataTaskRecordResultVO;
import com.joyadata.dsc.service.MetadataTaskRecordService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@RestController
@CrossOrigin
@RequestMapping("/metadataTaskRecord")
public class MetadataTaskRecordController extends BaseController<MetadataTaskRecord> {


    @Autowired
    private MetadataTaskRecordService metadataTaskRecordService;

    @Auth
    @GetMapping("/getResultByTaskRecordId")
    public Response<List<MetadataTaskRecordResultVO>> getResultByTaskRecordId(@RequestParam String id,@RequestParam String opt) {
        List<MetadataTaskRecordResultVO> resultByTaskRecordId = metadataTaskRecordService.getResultByTaskRecordId(id,opt);
        return ResponseFactory.makeSuccess(resultByTaskRecordId);
    }

    /**
     * 批量强制删除
     *
     * @param ids
     * @return
     */
    @Auth
    @DeleteMapping("/batchDelete")
    public Response batchDelete(@RequestParam List<String> ids) {
       if(CollUtil.isNotEmpty(ids)){
           metadataTaskRecordService.deleteBatch(ids);
       }
        return ResponseFactory.makeSuccess(ids);
    }

}
