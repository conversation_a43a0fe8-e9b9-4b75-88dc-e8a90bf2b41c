package com.joyadata.dsc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.enums.DatasourceTypeDatabase;
import com.joyadata.dsc.model.metadata.MetadataTask;
import com.joyadata.dsc.model.metadata.dto.MetadataDbDTO;
import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import com.joyadata.dsc.service.MetadataTaskService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23
 */
@RestController
@CrossOrigin
@RequestMapping("/metadataTask")
public class MetadataTaskController extends BaseController<MetadataTask> {


    @Autowired
    private MetadataTaskService metadataTaskService;


    @Auth
    @GetMapping("/getInfoByDsId/{datasourceInfoId}")
    public Response getInfoByDsId(@PathVariable String datasourceInfoId) {
        MetadataTask infoById = metadataTaskService.getInfoByDsId(datasourceInfoId);
        return ResponseFactory.makeSuccess(infoById);
    }
    @Auth
    @GetMapping("/getAllDbsNode")
    public Response collectMetadataCatalogue(@RequestParam String datasourceInfoId) {
        List<MetadataNodeVO> metadataNodeVOS = metadataTaskService.getAllDbsNode(datasourceInfoId);
        return ResponseFactory.makeSuccess(metadataNodeVOS);
    }
    /**
     * 获取文件节点
     */
    @Auth
    @GetMapping("/getAllFileNode")
    public Response getAllFileNode(@RequestParam String datasourceInfoId, @RequestParam(required = false) String path) {
        List<MetadataNodeVO> metadataNodeVOS = metadataTaskService.getAllFileNode(datasourceInfoId, path);
        return ResponseFactory.makeSuccess(metadataNodeVOS);
    }
    @Auth
    @GetMapping("/getAllSchemaNode")
    public Response getAllSchemaNode(@RequestParam String datasourceInfoId, @RequestParam String dbName) {
        List<MetadataNodeVO> metadataNodeVOS = metadataTaskService.getAllSchemaNode(datasourceInfoId,dbName);
        return ResponseFactory.makeSuccess(metadataNodeVOS);
    }
    @Auth
    @GetMapping("/getCommonNode")
    public Response getCommonNode(@RequestParam String datasourceInfoId) {
        List<MetadataNodeVO> metadataNodeVOS = metadataTaskService.getCommonNode(datasourceInfoId);
        return ResponseFactory.makeSuccess(metadataNodeVOS);
    }

    @Auth
    @PostMapping("/getTableNodesByDb")
    public Response<List<MetadataNodeVO>> getTableNodesByDb(@RequestBody MetadataDbDTO dto) {
        List<MetadataNodeVO> nodesByDb = metadataTaskService.getTableNodesByDb(dto);
        return ResponseFactory.makeSuccess(nodesByDb);
    }
    @Auth
    @PostMapping("/getIndexNodesByDb")
    public Response<List<MetadataNodeVO>> getIndexNodesByDb(@RequestBody MetadataDbDTO dto) {
        List<MetadataNodeVO> nodesByDb = metadataTaskService.getIndexNodesByDb(dto);
        return ResponseFactory.makeSuccess(nodesByDb);
    }

    @Auth
    @PostMapping("/getFunctionNodesByDb")
    public Response<List<MetadataNodeVO>> getFunctionNodesByDb(@RequestBody MetadataDbDTO dto) {
        List<MetadataNodeVO> nodesByDb = metadataTaskService.getFunctionNodesByDb(dto);
        return ResponseFactory.makeSuccess(nodesByDb);
    }

    @Auth
    @PostMapping("/start")
    public Response startAsyncTask(@RequestBody MetadataTask task) {
        metadataTaskService.startTask(task);
        return ResponseFactory.makeSuccess("");
    }

    @Auth
    @GetMapping("/stop/{metadataTaskRecordId}")
    public Response<String> stopAsyncTask(@PathVariable String metadataTaskRecordId) {
        String s = metadataTaskService.stopTask(metadataTaskRecordId);
        return ResponseFactory.makeSuccess(s);
    }

    @Auth
    @PostMapping("/startNoRungTask")
    public Response startNoRungTask(@RequestBody MetadataTask task) {
        metadataTaskService.startFailedTask(task);
        return ResponseFactory.makeSuccess("");
    }

    //获取不存在的表
    @Auth
    @PostMapping("/isTablesExists")
    public Response isTablesExists(@RequestBody MetadataTask task) {
        List<String> tablesExists = metadataTaskService.isTablesExists(task);
        return ResponseFactory.makeSuccess(tablesExists);
    }
    @Auth
    @GetMapping("/getInfoByRecordId/{metadataTaskRecordId}")
    public Response getInfoByRecordId(@PathVariable String metadataTaskRecordId) {
        MetadataTask infoById = metadataTaskService.getInfoByRecordId(metadataTaskRecordId);
        return ResponseFactory.makeSuccess(infoById);
    }

    //获取有schema的数据源类型
    @Auth
    @GetMapping("/getSchemaDatasourceType")
    public Response getSchemaDatasourceType() {
        List<String> schemaDatasourceType = DatasourceTypeDatabase.schemaDataTypeList();
        return ResponseFactory.makeSuccess(schemaDatasourceType);
    }

}
