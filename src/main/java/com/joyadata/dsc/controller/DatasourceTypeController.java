package com.joyadata.dsc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.DatasourceType;
import com.joyadata.dsc.service.DatasourceTypeService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据源类型控制器
 */
@RestController
@CrossOrigin
@RequestMapping("/datasourceType")
public class DatasourceTypeController extends BaseController<DatasourceType> {

    @Autowired
    private DatasourceTypeService datasourceTypeService;

    /**
     * 根据分类ID获取数据源类型列表
     *
     * @param classifyId 分类ID
     * @return 数据源类型列表
     */
    @Auth
    @GetMapping("/listByClassifyId")
    public Response<List<DatasourceType>> listByClassifyId(@RequestParam String classifyId) {
        List<DatasourceType> types = datasourceTypeService.getQuery()
                .eq("datasourceClassifyId", classifyId)
                .eq("delFlag", Boolean.FALSE)
                .sortby("dataType")
                .list();
        return ResponseFactory.makeSuccess(types);
    }

    /**
     * 获取所有数据源类型列表
     *
     * @return 所有数据源类型列表
     */
    @Auth
    @GetMapping("/listAll")
    public Response<List<DatasourceType>> listAll() {
        List<DatasourceType> types = datasourceTypeService.getQuery()
                .eq("delFlag", Boolean.FALSE)
                .sortby("dataType")
                .list();
        return ResponseFactory.makeSuccess(types);
    }
} 