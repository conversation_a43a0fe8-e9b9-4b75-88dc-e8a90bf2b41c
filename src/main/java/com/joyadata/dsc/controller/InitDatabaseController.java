package com.joyadata.dsc.controller;

import com.joyadata.dsc.service.InitDatabaseService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 **/
@RestController
@RequestMapping("init")
@CrossOrigin
public class InitDatabaseController {

    @Autowired
    private InitDatabaseService initDatabaseService;

    @GetMapping("database")
    public Response<Object> init(@RequestParam String tenantCode, @RequestParam Boolean write) {
        Map<String, Object> map = initDatabaseService.init(tenantCode, write);
        if ((Boolean) map.get("flag")) {
            return ResponseFactory.makeSuccess(map.get("data"));
        }
        return ResponseFactory.makeError(map.get("data"));
    }

}
