package com.joyadata.dsc.controller;

import cn.hutool.core.collection.CollUtil;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.joyadata.annotation.request.*;
import com.joyadata.cms.model.User;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.dto.DatasourceInfoSaveDTO;
import com.joyadata.dsc.model.datasoure.dto.KerberosFileUploadDTO;
import com.joyadata.dsc.model.datasoure.dto.SchemaQuery;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectProgressVO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceImportResultVO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceInfoDetailVO;
import com.joyadata.dsc.model.datasoure.vo.DbAndSchemaVO;
import com.joyadata.dsc.model.datasoure.vo.KerberosUploadResultVO;
import com.joyadata.dsc.service.DatasourceInfoService;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 数据源管理
 */
@RestController
@CrossOrigin
@RequestMapping("/datasourceInfo")
public class DatasourceInfoController extends BaseController<DatasourceInfo> {

    @Autowired
    private DatasourceInfoService datasourceInfoService;

    /**
     * 批量修改数据源的业务系统信息
     * @param datasourceInfos
     * @return
     */
    @Auth
    @PostMapping("/batchUpdateBusinessSystem")
    public Response batchUpdateBusinessSystem(@RequestBody List<DatasourceInfo> datasourceInfos) {
        datasourceInfoService.batchUpdateBusinessSystem(datasourceInfos);
        return ResponseFactory.makeSuccess("修改成功");
    }

    @Auth
    @PostMapping("/save")
    public Response<String> saveDatasourceInfo(@RequestBody DatasourceInfoSaveDTO datasourceInfoSaveDTO) {
        return ResponseFactory.makeSuccess("保存成功", datasourceInfoService.saveDatasourceInfo(datasourceInfoSaveDTO));
    }

    @GetMapping("/datasource/dto/{id}")
    public Response<DatasourceDTO> getDatasourceDTO(@PathVariable String id) {
        return ResponseFactory.makeSuccess(datasourceInfoService.getDatasourceDTO(id));
    }

    @Auth
    @GetMapping("/user/list")
    public Response<List<User>> getUserList() {
        return ResponseFactory.makeSuccess(datasourceInfoService.getUserList());
    }


    /**
     * 批量强制删除
     *
     * @param ids
     * @return
     */
    @Auth
    @DeleteMapping("/batchDelete")
    public Response batchDelete(@RequestParam List<String> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            datasourceInfoService.batchDelete(ids);
        }
        return ResponseFactory.makeSuccess(ids);
    }

    /**
     * 测试连接
     *
     * @param datasourceInfoSaveDTO 数据源及节点信息
     * @return 连接状态
     */
    @Auth
    @PostMapping("/connect")
    public Response<DatasourceConnectProgressVO> connect(@RequestBody DatasourceInfoSaveDTO datasourceInfoSaveDTO) {
        return ResponseFactory.makeSuccess(datasourceInfoService.connect(datasourceInfoSaveDTO));
    }

    /**
     * 测试连接ById
     *
     * @param id 数据源id
     * @return 连接状态
     */
    @Auth
    @PostMapping("/connect/{id}")
    public Response<DatasourceConnectProgressVO> connectById(@PathVariable String id) {
        return ResponseFactory.makeSuccess(datasourceInfoService.connectById(id));
    }

    /**
     * 获取 schema
     */
    @Auth
    @PostMapping("/getAllSchema")
    public Response<List<String>> getAllSchema(@RequestBody SchemaQuery schemaQuery) {
        List<String> result = null;
        try {
            result = datasourceInfoService.getAllSchema(schemaQuery);
        } catch (Exception e) {
            Response<List<String>> failResponse = ResponseFactory.makeWarning("获取schema失败请检查配置项");
            failResponse.setCode(2);
            return failResponse;
        }
        return ResponseFactory.makeSuccess(result);
    }

    @PostMapping("/getDatasourceInfo/{id}")
    public Response<DatasourceDTO> getDatasourceInfoById(@PathVariable String id) {
        return ResponseFactory.makeSuccess(datasourceInfoService.getDatasourceInfoById(id));
    }

    /**
     * 获取数据源详情
     * @param datasourceInfoId 数据源id
     * @return 详细信息
     */
    @Auth
    @GetMapping("/detail/{datasourceInfoId}")
    public Response<DatasourceInfoDetailVO> getDatasourceInfoDetailList(@PathVariable String datasourceInfoId) {
        return ResponseFactory.makeSuccess(datasourceInfoService.getDatasourceInfoDetailList(datasourceInfoId));
    }

    /**
     * 克隆数据源
     * @param id 数据源id
     * @return 克隆后的数据源id
     */
    @Auth
    @PostMapping("/clone/{id}")
    public Response<String> cloneDatasource(@PathVariable String id) {
        return ResponseFactory.makeSuccess("克隆成功", datasourceInfoService.cloneDatasource(id));
    }

    /**
     * 获取数据库名或schema
     * @param id 数据源id
     * @return 数据库名或schema
     */
    @GetMapping("/getDbNameOrSchema/{id}")
    public Response<DbAndSchemaVO> getDbNameOrSchema(@PathVariable String id) {
        return ResponseFactory.makeSuccess(datasourceInfoService.getDbNameOrSchema(id));
    }

    /**
     * 导出数据源信息到Excel
     * @return Excel文件
     */
    @Auth
    @PostMapping("/export/all")
    public void exportDatasourceInfo(HttpServletResponse response) {
        datasourceInfoService.exportDatasourceInfo(response);
    }

    /**
     * 导出选中的数据源信息到Excel
     * @param ids 选中的数据源ID列表
     * @return Excel文件
     */
    @Auth
    @PostMapping("/export/selected")
    public void exportSelectedDatasourceInfo(@RequestBody List<String> ids, HttpServletResponse response) {
        datasourceInfoService.exportSelectedDatasourceInfo(ids, response);
    }

    /**
     * 导出搜索结果到Excel
     *
     * @param conditionGroupList 搜索条件
     * @param groupByCondition 搜索条件
     * @param orConditionGroupList 搜索条件
     * @param queryFilter 搜索条件
     * @param page 页码
     * @param pager 页容量
     * @return Excel文件
     */
    @Auth
    @GetMapping("/export/search")
    public void exportSearchDatasourceInfo(@SearchBy @Period @Frame @ParamKeys @Exists @NotExists List<ConditionGroup> conditionGroupList, @MustKeys @ShouldKeys List<ConditionGroup> orConditionGroupList, @GroupBy GroupByCondition groupByCondition, @QueryFilter com.joyadata.model.sql.QueryFilter queryFilter, @RequestParam(required = false) Integer page, @RequestParam(required = false) Integer pager, HttpServletResponse response) {
        datasourceInfoService.exportSearchDatasourceInfo(conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager, response);
    }

    /**
     * 导入数据源信息
     * @param file Excel文件
     * @return 导入结果
     */
    @Auth
    @PostMapping("/import")
    public Response<DatasourceImportResultVO> importDatasourceInfo(@RequestParam("file") MultipartFile file) {
        return ResponseFactory.makeSuccess(datasourceInfoService.importDatasourceInfo(file));
    }

    /**
     * 下载数据源导入模板
     * @param response HTTP响应
     */
    @Auth
    @GetMapping("/import/template")
    public void downloadImportTemplate(HttpServletResponse response) {
        datasourceInfoService.downloadImportTemplate(response);
    }

    /**
     * 数据源-批量彻底删除 并清空相关数据
     */
    @Auth
    @PostMapping("/batchDeleteDatasource")
    public Response batchDeleteDatasource(@RequestBody List<String> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            datasourceInfoService.batchDeleteDatasource(ids);
        }
        return ResponseFactory.makeSuccess(ids);
    }

    /**
     * 异步上传单个Kerberos认证文件
     * 前端单个文件上传获取文件ID，保存数据源时再传递ID
     *
     * @param file 上传的文件
     * @param fileType 文件类型 (keytab, krb5conf, jaasconf)
     * @return 上传结果，包含文件ID
     */
    @Auth
    @PostMapping("/kerberos/upload/single")
    public Response<KerberosFileUploadDTO> uploadSingleKerberosFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("fileType") String fileType) {

        return ResponseFactory.makeSuccess(
            datasourceInfoService.uploadSingleKerberosFile(file, fileType)
        );
    }

    /**
     * 保存数据源时配置Kerberos认证和Hadoop配置文件
     *
     * @param datasourceId 数据源ID
     * @param principal Kerberos主体
     * @param keytabFileId keytab文件ID
     * @param krb5ConfFileId krb5.conf文件ID
     * @param jaasConfFileId jaas.conf文件ID（可选）
     * @param hiveSiteFileId hive-site.xml文件ID（可选）
     * @param hdfsSiteFileId hdfs-site.xml文件ID（可选）
     * @param coreSiteFileId core-site.xml文件ID（可选）
     * @return 配置结果
     */
    @Auth
    @PostMapping("/kerberos/configure/{datasourceId}")
    public Response<String> configureKerberos(
            @PathVariable String datasourceId,
            @RequestParam("principal") String principal,
            @RequestParam("keytabFileId") String keytabFileId,
            @RequestParam("krb5ConfFileId") String krb5ConfFileId,
            @RequestParam(value = "jaasConfFileId", required = false) String jaasConfFileId,
            @RequestParam(value = "hiveSiteFileId", required = false) String hiveSiteFileId,
            @RequestParam(value = "hdfsSiteFileId", required = false) String hdfsSiteFileId,
            @RequestParam(value = "coreSiteFileId", required = false) String coreSiteFileId) {

        datasourceInfoService.configureKerberos(datasourceId, principal, keytabFileId, krb5ConfFileId,
                jaasConfFileId, hiveSiteFileId, hdfsSiteFileId, coreSiteFileId);
        return ResponseFactory.makeSuccess("Kerberos和Hadoop配置成功");
    }

    /**
     * 删除Kerberos认证文件
     *
     * @param datasourceId 数据源ID
     * @return 删除结果
     */
    @Auth
    @DeleteMapping("/kerberos/{datasourceId}")
    public Response<String> deleteKerberosFiles(@PathVariable String datasourceId) {
        datasourceInfoService.deleteKerberosFiles(datasourceId);
        return ResponseFactory.makeSuccess("Kerberos文件删除成功");
    }

    /**
     * 检查Kerberos文件状态
     *
     * @param datasourceId 数据源ID
     * @return 文件状态信息
     */
    @Auth
    @GetMapping("/kerberos/status/{datasourceId}")
    public Response<Map<String, Object>> getKerberosStatus(@PathVariable String datasourceId) {
        return ResponseFactory.makeSuccess(datasourceInfoService.getKerberosStatus(datasourceId));
    }


}
