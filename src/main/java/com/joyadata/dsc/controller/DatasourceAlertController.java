package com.joyadata.dsc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.DatasourceAlertEvent;
import com.joyadata.dsc.service.DatasourceAlertService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName DatasourceAlertController
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/10 16:01
 * @Version 1.0
 **/
@RestController
@CrossOrigin
@RequestMapping("/datasourceAlert")
public class DatasourceAlertController extends BaseController<DatasourceAlert> {

    /**
     * 获取数据源中心告警事件列表信息
     * @return
     */
    @Auth
    @GetMapping("/events")
    public Response<List<DatasourceAlertEvent>> getDatasourceAlertEvents() {
        return ResponseFactory.makeSuccess(((DatasourceAlertService) getService()).datasourceAlertEvents());
    }

    /**
     * 新增数据源告警配置
     * @param datasourceAlert
     * @return
     */
    @Auth
    @PostMapping("/add")
    public Response<DatasourceAlert> add(@RequestBody DatasourceAlert datasourceAlert) {
        DatasourceAlert alert = ((DatasourceAlertService) getService()).save(datasourceAlert);
        return ResponseFactory.makeSuccess(alert);
    }

    /**
     * 编辑数据源告警配置
     * @param datasourceAlert
     * @return
     */
    @Auth
    @PutMapping("/edit")
    public Response<Void> edit(@RequestBody DatasourceAlert datasourceAlert) {
        ((DatasourceAlertService) getService()).modify(datasourceAlert);
        return ResponseFactory.makeSuccess(Response.Msg.Success);
    }

    @Auth
    @GetMapping("/telnet")
    public Response<Boolean> telnet(String ip, Integer port) {
        return ResponseFactory.makeSuccess(((DatasourceAlertService) getService()).telnet(ip, port, 3));
    }
}
