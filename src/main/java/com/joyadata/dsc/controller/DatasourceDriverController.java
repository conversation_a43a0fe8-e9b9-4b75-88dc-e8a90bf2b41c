package com.joyadata.dsc.controller;

import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.dsc.model.datasoure.ClassifyTree;
import com.joyadata.dsc.model.datasoure.DatasourceDriver;
import com.joyadata.dsc.service.DatasourceDriverService;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
@RestController
@CrossOrigin
@RequestMapping("/datasourceDriver")
public class DatasourceDriverController extends BaseController<DatasourceDriver> {

    /**
     * 获取数据源分类树
     * @return 数据源分类树
     */
    @Auth
    @GetMapping("/tree")
    public Response<Object> tree() {
        List<ClassifyTree> tree = ((DatasourceDriverService) getService()).tree();
        return ResponseFactory.makeSuccess(tree);
    }

    /**
     * 新增驱动
     * @param datasourceDriver
     * @return
     */
    @Auth
    @PostMapping("/add")
    public Response<DatasourceDriver> add(@RequestBody DatasourceDriver datasourceDriver) {
        DatasourceDriver driver = ((DatasourceDriverService) getService()).save(datasourceDriver);
        return ResponseFactory.makeSuccess(driver);
    }

    @Auth
    @DeleteMapping("/del/{id}")
    public Response<Void> remove(@PathVariable String id) {
        ((DatasourceDriverService) getService()).del(id);
        return ResponseFactory.makeSuccess(Response.Msg.Success);
    }

    @Auth
    @PutMapping("/edit")
    public Response<Void> edit(@RequestBody DatasourceDriver datasourceDriver) {
        ((DatasourceDriverService) getService()).modify(datasourceDriver);
        return ResponseFactory.makeSuccess(Response.Msg.Success);
    }
}
