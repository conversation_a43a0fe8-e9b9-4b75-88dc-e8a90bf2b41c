package com.joyadata.dsc.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.SpelEvaluationException;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class SpELParserUtils {

    private static final ExpressionParser parser = new SpelExpressionParser();
    private static final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    // 私有构造方法，防止实例化
    private SpELParserUtils() {
    }

    public static String parse(String spel, Method method, Object[] args, Object result) {
        // 1. 创建上下文
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 2. 绑定方法参数
        String[] paramNames = parameterNameDiscoverer.getParameterNames(method);
        Map<String, Object> variables = new HashMap<>();
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                variables.put(paramNames[i], args[i]);
            }
        }

        // 3. 添加空值保护
        variables.forEach((k, v) -> {
            if (v == null) {
                log.warn("Variable {} is null", k);
            }
        });

        // 4. 设置上下文变量
        context.setVariables(variables);
        context.setVariable("result", result);

        // 5. 解析表达式
        try {
            return parser.parseExpression(spel).getValue(context, String.class);
        } catch (SpelEvaluationException e) {
            log.error("SpEL 解析失败: {}", e.getMessage());
        }
        return null;
    }
}