package com.joyadata.dsc.utils.jdbc;

import java.util.regex.Pattern;

/**
 * Utility class for validating JDBC URLs.
 */
public class JdbcUrlValidator {

    private static final Pattern MYSQL_URL_PATTERN = 
            Pattern.compile("^jdbc:mysql://[^:/]+:\\d+/[^?]+(?:\\?.*)?$");
    
    private static final Pattern POSTGRESQL_URL_PATTERN = 
            Pattern.compile("^jdbc:postgresql://[^:/]+:\\d+/[^?]+(?:\\?.*)?$");
    
    private static final Pattern ORACLE_SERVICE_NAME_PATTERN = 
            Pattern.compile("^jdbc:oracle:thin:@//[^:/]+:\\d+/[^?]+(?:\\?.*)?$");
    
    private static final Pattern ORACLE_SID_PATTERN = 
            Pattern.compile("^jdbc:oracle:thin:@[^:/]+:\\d+:[^?]+(?:\\?.*)?$");
    
    private static final Pattern SQLSERVER_URL_PATTERN = 
            Pattern.compile("^jdbc:sqlserver://[^:/]+:\\d+(?:;.*)?$");
    
    private static final Pattern SQLSERVER_JTDS_URL_PATTERN = 
            Pattern.compile("^jdbc:jtds:sqlserver://[^:/]+:\\d+(?:/[^;]+)?(?:;.*)?$");
    
    private static final Pattern INFORMIX_URL_PATTERN = 
            Pattern.compile("^jdbc:informix-sqli://[^:/]+:\\d+/[^:]+:INFORMIXSERVER=[^:]+(?::.*)?$");
    
    private static final Pattern GREENPLUM6_URL_PATTERN = 
            Pattern.compile("^jdbc:pivotal:greenplum://[^:/]+:\\d+/[^?]+(?:\\?.*)?$");

    // Kingbase8正则表达式: *****************************,host2:port2/database
    private static final Pattern KINGBASE8_PATTERN =
            Pattern.compile("jdbc:kingbase8://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");
    private static final Pattern DM_PATTERN =
            Pattern.compile("jdbc:dm://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");

    private static final Pattern CLICKHOUSE_PATTERN =
            Pattern.compile("jdbc:clickhouse://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");
    
    private static final Pattern DB2_URL_PATTERN =
            Pattern.compile("^jdbc:db2://[^:/]+:\\d+/[^?]+(?:\\?.*)?$");
    private static final Pattern OCEANMYSQL_PATTERN =
            Pattern.compile("jdbc:oceanbase://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");
    private static final Pattern SAP_PATTERN =
            Pattern.compile("jdbc:sap://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");

    private JdbcUrlValidator() {
        // Utility class, no instantiation
    }
    
    /**
     * Validates if the provided JDBC URL is valid for the specified database type.
     * 
     * @param url the JDBC URL to validate
     * @param databaseType the expected database type
     * @return true if the URL is valid, false otherwise
     */
    public static boolean isValid(String url, DatabaseJdbcUrlType databaseType) {
        if (url == null || url.isEmpty()) {
            return false;
        }
        
        switch (databaseType) {
            case MYSQL:
            case TiDB:
            case TDSQL_FOR_MySQL:
                return MYSQL_URL_PATTERN.matcher(url).matches();
            case POSTGRESQL:
            case GaussDB:
            case ADB_PostgreSQL:
            case TBase:
            case TDSQL_FOR_PG:
            case TDSQL_FOR_ORACLE:
                return POSTGRESQL_URL_PATTERN.matcher(url).matches();
            case GREENPLUM6:
                return GREENPLUM6_URL_PATTERN.matcher(url).matches();
            case GREENPLUM_PostgreSQL:
                return POSTGRESQL_URL_PATTERN.matcher(url).matches();
            case ORACLE:
                return ORACLE_SERVICE_NAME_PATTERN.matcher(url).matches() || 
                       ORACLE_SID_PATTERN.matcher(url).matches();
            case SQLSERVER:
                return SQLSERVER_URL_PATTERN.matcher(url).matches();
            case SQLSERVER_JTDS:
                return SQLSERVER_JTDS_URL_PATTERN.matcher(url).matches();
            case INFORMIX:
                return INFORMIX_URL_PATTERN.matcher(url).matches();
            case KINGBASE8:
                return KINGBASE8_PATTERN.matcher(url).matches();
            case DMDB:
                return DM_PATTERN.matcher(url).matches();
            case ClickHouse:
                return CLICKHOUSE_PATTERN.matcher(url).matches();
            case DB2:
                return DB2_URL_PATTERN.matcher(url).matches();
            case OceanBase_FOR_MySQL:
                return OCEANMYSQL_PATTERN.matcher(url).matches();
            case SAP_HANA:
                return SAP_PATTERN.matcher(url).matches();
            default:
                return false;
        }
    }
    
    /**
     * Attempts to determine the database type from a JDBC URL.
     * 
     * @param url the JDBC URL to analyze
     * @return the detected DatabaseJdbcUrlType or null if the URL format is not recognized
     */
    public static DatabaseJdbcUrlType detectDatabaseType(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }
        
        if (url.startsWith("jdbc:mysql:")) {
            return DatabaseJdbcUrlType.MYSQL;
        } else if (url.startsWith("jdbc:postgresql:")) {
            return DatabaseJdbcUrlType.POSTGRESQL;
        } else if (url.startsWith("jdbc:oracle:")) {
            return DatabaseJdbcUrlType.ORACLE;
        } else if (url.startsWith("jdbc:sqlserver:")) {
            return DatabaseJdbcUrlType.SQLSERVER;
        } else if (url.startsWith("jdbc:jtds:sqlserver:")) {
            return DatabaseJdbcUrlType.SQLSERVER_JTDS;
        } else if (url.startsWith("jdbc:goldendb:")) {
            return DatabaseJdbcUrlType.GoldenDB;
        } else if (url.startsWith("jdbc:informix-sqli:")) {
            return DatabaseJdbcUrlType.INFORMIX;
        } else if (url.startsWith("jdbc:pivotal:greenplum:")) {
            return DatabaseJdbcUrlType.GREENPLUM6;
        }else if (url.startsWith("jdbc:kingbase8:")) {
            return DatabaseJdbcUrlType.KINGBASE8;
        }else if (url.startsWith("jdbc:dm:")) {
            return DatabaseJdbcUrlType.DMDB;
        }else if (url.startsWith("jdbc:clickhouse:")) {
            return DatabaseJdbcUrlType.ClickHouse;
        }else if (url.startsWith("jdbc:db2:")) {
            return DatabaseJdbcUrlType.DB2;
        }else if (url.startsWith("jdbc:oceanbase:")) {
            return DatabaseJdbcUrlType.OceanBase_FOR_MySQL;
        }else if(url.startsWith("jdbc:sap:")){
            return DatabaseJdbcUrlType.SAP_HANA;
        }
        
        return null;
    }
} 