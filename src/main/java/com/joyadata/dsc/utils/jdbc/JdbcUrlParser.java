package com.joyadata.dsc.utils.jdbc;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * JDBC URL解析工具类，用于从JDBC URL中提取主机、端口和数据库名等信息
 */
public class JdbcUrlParser {

    // MySQL正则表达式: ************************,host2:port2/database
    private static final Pattern MYSQL_PATTERN = 
            Pattern.compile("jdbc:mysql(?::loadbalance)?://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");

    private static final Pattern GOLDENDB_PATTERN =
            Pattern.compile("jdbc:goldendb://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");
    
    // PostgreSQL正则表达式: *****************************,host2:port2/database
    private static final Pattern POSTGRESQL_PATTERN = 
            Pattern.compile("jdbc:postgresql://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");
    
    // Oracle正则表达式 (标准格式): *************************************
    private static final Pattern ORACLE_STANDARD_PATTERN = 
            Pattern.compile("jdbc:oracle:thin:@//([^/]+)(?:/([^?]+))?(?:\\?(.*))?");

    // Oracle正则表达式 (SID格式): *******************************
    private static final Pattern ORACLE_SID_PATTERN =
            Pattern.compile("jdbc:oracle:thin:@([^:]+):(\\d+):([^:?]+)(?:\\?(.*))?");

    // Oracle正则表达式 (RAC格式): ******************************=...
    private static final Pattern ORACLE_RAC_PATTERN = 
            Pattern.compile("*******************************************=([^)]+)\\)\\(PORT=(\\d+)\\).*?\\(SERVICE_NAME=([^)]+)\\)");

    // 用于提取RAC格式中的所有主机和端口
    private static final Pattern ORACLE_RAC_HOST_PORT_PATTERN =
            Pattern.compile("\\(HOST=([^)]+)\\)\\(PORT=(\\d+)\\)");

    // SQL Server (Microsoft)正则表达式: ************************************************
    private static final Pattern SQLSERVER_PATTERN = 
            Pattern.compile("jdbc:sqlserver://([^;]+)(?:;(.*))?");
    
    // SQL Server (jTDS)正则表达式: ****************************************
    private static final Pattern SQLSERVER_JTDS_PATTERN = 
            Pattern.compile("jdbc:jtds:sqlserver://([^/]+)(?:/([^;]+))?(?:;(.*))?");

    // Informix正则表达式: jdbc:informix-sqli://host:port/database:INFORMIXSERVER=server
    private static final Pattern INFORMIX_PATTERN = 
            Pattern.compile("jdbc:informix-sqli://([^/]+)/([^:]+):INFORMIXSERVER=([^:]+)(?::(.*))?");

    // Greenplum6正则表达式: *******************************************
    private static final Pattern GREENPLUM6_PATTERN = 
            Pattern.compile("jdbc:pivotal:greenplum://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");

    // Kingbase8正则表达式: *****************************,host2:port2/database
    private static final Pattern KINGBASE8_PATTERN =
            Pattern.compile("jdbc:kingbase8://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");
    private static final Pattern DM_PATTERN =
            Pattern.compile("jdbc:dm://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");

    private static final Pattern CLICKHOUSE_PATTERN =
            Pattern.compile("jdbc:clickhouse://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");
    
    private static final Pattern DB2_PATTERN =
            Pattern.compile("jdbc:db2://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");

    private static final Pattern OCEANMYSQL_PATTERN =
            Pattern.compile("jdbc:oceanbase://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");

    private static final Pattern SAP_PATTERN =
            Pattern.compile("jdbc:sap://([^/]+)(?:/([^?]+))?(?:\\?(.*))?");

    /**
     * 从JDBC URL中提取主机和端口信息
     *
     * @param jdbcUrl JDBC URL
     * @return 主机和端口列表，如果解析失败则返回空列表
     */
    public static List<JdbcUrlBuilderUtil.HostPort> extractHostPorts(String jdbcUrl) {
        if (jdbcUrl == null || jdbcUrl.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        
        // 检测数据库类型并应用相应的解析逻辑
        DatabaseJdbcUrlType dbType = JdbcUrlValidator.detectDatabaseType(jdbcUrl);
        if (dbType == null) {
            return hostPorts; // 无法识别的URL类型
        }
        
        switch (dbType) {
            case MYSQL:
                hostPorts = extractMySqlHostPorts(jdbcUrl);
                break;
            case GoldenDB:
                hostPorts = extractGoldenDbHostPorts(jdbcUrl);
                break;
            case POSTGRESQL:
            case ADB_PostgreSQL:
            case TDSQL_FOR_PG:
            case TDSQL_FOR_ORACLE:
            case TBase:
                hostPorts = extractPostgreSqlHostPorts(jdbcUrl);
                break;
            case GREENPLUM6:
                hostPorts = extractGreenplum6HostPorts(jdbcUrl);
                break;
            case GREENPLUM_PostgreSQL:
                hostPorts = extractPostgreSqlHostPorts(jdbcUrl);
                break;
            case ORACLE:
                hostPorts = extractOracleHostPorts(jdbcUrl);
                break;
            case SQLSERVER:
                hostPorts = extractSqlServerHostPorts(jdbcUrl);
                break;
            case SQLSERVER_JTDS:
                hostPorts = extractSqlServerJtdsHostPorts(jdbcUrl);
                break;
            case INFORMIX:
                hostPorts = extractInformixHostPorts(jdbcUrl);
                break;
            case KINGBASE8:
                hostPorts = extractKingBaseHostPorts(jdbcUrl);
                break;
            case DMDB:
                hostPorts = extractDMHostPorts(jdbcUrl);
                break;
            case ClickHouse:
                hostPorts = extractCKHostPorts(jdbcUrl);
                break;
            case DB2:
                hostPorts = extractDB2HostPorts(jdbcUrl);
                break;
            case OceanBase_FOR_MySQL:
            case OceanBase_FOR_ORACLE:
                hostPorts = extractOcMysqlHostPorts(jdbcUrl);
                break;
            case SAP_HANA:
                hostPorts = extractSapHostPorts(jdbcUrl);
                break;

        }
        
        return hostPorts;
    }
    
    /**
     * 从JDBC URL中提取数据库名
     *
     * @param jdbcUrl JDBC URL
     * @return 数据库名，如果解析失败则返回null
     */
    public static String extractDatabase(String jdbcUrl) {
        if (jdbcUrl == null || jdbcUrl.trim().isEmpty()) {
            return null;
        }
        
        // 检测数据库类型并应用相应的解析逻辑
        DatabaseJdbcUrlType dbType = JdbcUrlValidator.detectDatabaseType(jdbcUrl);
        if (dbType == null) {
            return null; // 无法识别的URL类型
        }
        
        switch (dbType) {
            case MYSQL:
            case GoldenDB:
                return extractMySqlDatabase(jdbcUrl);
            case POSTGRESQL:
            case ADB_PostgreSQL:
            case TDSQL_FOR_PG:
            case TDSQL_FOR_ORACLE:
            case TBase:
                return extractPostgreSqlDatabase(jdbcUrl);
            case GREENPLUM6:
                return extractGreenplum6Database(jdbcUrl);
            case GREENPLUM_PostgreSQL:
                return extractPostgreSqlDatabase(jdbcUrl);
            case ORACLE:
                return extractOracleDatabase(jdbcUrl);
            case SQLSERVER:
                return extractSqlServerDatabase(jdbcUrl);
            case SQLSERVER_JTDS:
                return extractSqlServerJtdsDatabase(jdbcUrl);
            case INFORMIX:
                return extractInformixDatabase(jdbcUrl);
            case KINGBASE8:
                return extractKingBaseDatabase(jdbcUrl);
            case DMDB:
                return extractDMDatabase(jdbcUrl);
            case ClickHouse:
                return extractCkDatabase(jdbcUrl);
            case DB2:
                return extractDB2Database(jdbcUrl);
            case OceanBase_FOR_MySQL:
            case OceanBase_FOR_ORACLE:
                return extractOcMysqlDatabase(jdbcUrl);
            case SAP_HANA:
                return extractSapDatabase(jdbcUrl);
            default:
                return null;
        }
    }
    
    /**
     * 从JDBC URL中提取连接属性
     *
     * @param jdbcUrl JDBC URL
     * @return 连接属性列表，如果解析失败则返回空列表
     */
    public static List<String> extractProperties(String jdbcUrl) {
        if (jdbcUrl == null || jdbcUrl.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> properties = new ArrayList<>();
        
        // 检测数据库类型并应用相应的解析逻辑
        DatabaseJdbcUrlType dbType = JdbcUrlValidator.detectDatabaseType(jdbcUrl);
        if (dbType == null) {
            return properties; // 无法识别的URL类型
        }
        
        switch (dbType) {
            case MYSQL:
            case GoldenDB:
            case TiDB:
            case TDSQL_FOR_MySQL:
            case ADS:
            case StarRocks:
            case DWS_MySQL:
            case GaussDB_FOR_MySQL:
            case Sequoiadb_FOR_MYSQL:
            case MariaDB:
            case GaussDB:
            case TBase:
            case TDSQL_FOR_PG:
            case TDSQL_FOR_ORACLE:
            case KINGBASE8:
            case DMDB:
            case POSTGRESQL:
            case ClickHouse:
            case ADB_PostgreSQL:
            case OceanBase_FOR_MySQL:
            case OceanBase_FOR_ORACLE:
            case SAP_HANA:
                // 格式: ?param1=value1&param2=value2
                int questionMarkIndex = jdbcUrl.indexOf('?');
                if (questionMarkIndex != -1 && questionMarkIndex < jdbcUrl.length() - 1) {
                    String[] params = jdbcUrl.substring(questionMarkIndex + 1).split("&");
                    for (String param : params) {
                        properties.add(param);
                    }
                }
                break;
            case DB2:
                // 格式: ?param1=value1&param2=value2
                int db2QuestionMarkIndex = jdbcUrl.indexOf('?');
                if (db2QuestionMarkIndex != -1 && db2QuestionMarkIndex < jdbcUrl.length() - 1) {
                    String[] params = jdbcUrl.substring(db2QuestionMarkIndex + 1).split("&");
                    for (String param : params) {
                        properties.add(param);
                    }
                }
                break;
            case GREENPLUM6:
                // 格式: ?param1=value1&param2=value2
                int gp6QuestionMarkIndex = jdbcUrl.indexOf('?');
                if (gp6QuestionMarkIndex != -1 && gp6QuestionMarkIndex < jdbcUrl.length() - 1) {
                    String[] params = jdbcUrl.substring(gp6QuestionMarkIndex + 1).split("&");
                    for (String param : params) {
                        properties.add(param);
                    }
                }
                break;
            case GREENPLUM_PostgreSQL:
                // 格式: ?param1=value1&param2=value2
                int greenplumQuestionMarkIndex = jdbcUrl.indexOf('?');
                if (greenplumQuestionMarkIndex != -1 && greenplumQuestionMarkIndex < jdbcUrl.length() - 1) {
                    String[] params = jdbcUrl.substring(greenplumQuestionMarkIndex + 1).split("&");
                    for (String param : params) {
                        properties.add(param);
                    }
                }
                break;
            case ORACLE:
                // Oracle通常没有URL参数，但可能有TNS参数
                break;
            case SQLSERVER:
                // 格式: ;param1=value1;param2=value2
                int semicolonIndex = jdbcUrl.indexOf(';');
                if (semicolonIndex != -1 && semicolonIndex < jdbcUrl.length() - 1) {
                    String[] params = jdbcUrl.substring(semicolonIndex + 1).split(";");
                    for (String param : params) {
                        if (!param.startsWith("databaseName=")) {
                            properties.add(param);
                        }
                    }
                }
                break;
            case SQLSERVER_JTDS:
                // 格式: ;param1=value1;param2=value2
                int jtdsSemicolonIndex = jdbcUrl.indexOf(';');
                if (jtdsSemicolonIndex != -1 && jtdsSemicolonIndex < jdbcUrl.length() - 1) {
                    String[] params = jdbcUrl.substring(jtdsSemicolonIndex + 1).split(";");
                    for (String param : params) {
                        properties.add(param);
                    }
                }
                break;
            case INFORMIX:
                // 格式: :param1=value1:param2=value2
                Matcher informixMatcher = INFORMIX_PATTERN.matcher(jdbcUrl);
                if (informixMatcher.find() && informixMatcher.group(4) != null) {
                    String[] params = informixMatcher.group(4).split(":");
                    for (String param : params) {
                        if (!param.isEmpty()) {
                            properties.add(param);
                        }
                    }
                }
                break;
        }
        
        return properties;
    }
    
    // ========== 私有辅助方法 ==========
    
    private static List<JdbcUrlBuilderUtil.HostPort> extractMySqlHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = MYSQL_PATTERN.matcher(jdbcUrl);
        
        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");
            
            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 3306;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }
        
        return hostPorts;
    }
    
    private static String extractMySqlDatabase(String jdbcUrl) {
        Matcher matcher = MYSQL_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }
    
    private static List<JdbcUrlBuilderUtil.HostPort> extractPostgreSqlHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = POSTGRESQL_PATTERN.matcher(jdbcUrl);
        
        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");
            
            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 5432;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }
        
        return hostPorts;
    }


    private static List<JdbcUrlBuilderUtil.HostPort> extractGoldenDbHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = GOLDENDB_PATTERN.matcher(jdbcUrl);

        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");

            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 5432;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }

        return hostPorts;
    }
    
    private static String extractPostgreSqlDatabase(String jdbcUrl) {
        Matcher matcher = POSTGRESQL_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }

    private static List<JdbcUrlBuilderUtil.HostPort> extractOracleHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();

        // 尝试标准格式
        Matcher standardMatcher = ORACLE_STANDARD_PATTERN.matcher(jdbcUrl);
        if (standardMatcher.find()) {
            String hostPortList = standardMatcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");

            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 1521;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
            return hostPorts;
        }

        // 尝试SID格式 (*******************************)
        Matcher sidMatcher = ORACLE_SID_PATTERN.matcher(jdbcUrl);
        if (sidMatcher.find()) {
            String host = sidMatcher.group(1);
            int port = Integer.parseInt(sidMatcher.group(2));
            hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            return hostPorts;
        }

        // 尝试RAC格式
        if (jdbcUrl.contains("(DESCRIPTION=")) {
            // 使用专门的模式提取所有的主机和端口
            Matcher hostPortMatcher = ORACLE_RAC_HOST_PORT_PATTERN.matcher(jdbcUrl);
            while (hostPortMatcher.find()) {
                String host = hostPortMatcher.group(1);
                int port = Integer.parseInt(hostPortMatcher.group(2));
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
            return hostPorts;
        }

        return hostPorts;
    }

    private static String extractOracleDatabase(String jdbcUrl) {
        // 尝试标准格式
        Matcher standardMatcher = ORACLE_STANDARD_PATTERN.matcher(jdbcUrl);
        if (standardMatcher.find() && standardMatcher.group(2) != null) {
            return standardMatcher.group(2);
        }

        // 尝试SID格式 (*******************************)
        Matcher sidMatcher = ORACLE_SID_PATTERN.matcher(jdbcUrl);
        if (sidMatcher.find() && sidMatcher.group(3) != null) {
            return sidMatcher.group(3);
        }

        // 尝试RAC格式
        Matcher racMatcher = ORACLE_RAC_PATTERN.matcher(jdbcUrl);
        if (racMatcher.find() && racMatcher.group(3) != null) {
            return racMatcher.group(3);
        }

        return null;
    }
    
    private static List<JdbcUrlBuilderUtil.HostPort> extractSqlServerHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = SQLSERVER_PATTERN.matcher(jdbcUrl);
        
        if (matcher.find()) {
            String hostPort = matcher.group(1);
            String[] parts = hostPort.split(":");
            String host = parts[0].trim();
            int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 1433;
            hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            
            // 检查是否有failoverPartner
            if (matcher.group(2) != null) {
                String params = matcher.group(2);
                for (String param : params.split(";")) {
                    if (param.startsWith("failoverPartner=")) {
                        String failoverHost = param.substring("failoverPartner=".length());
                        hostPorts.add(new JdbcUrlBuilderUtil.HostPort(failoverHost, port)); // 使用相同的端口
                    }
                }
            }
        }
        
        return hostPorts;
    }
    
    private static String extractSqlServerDatabase(String jdbcUrl) {
        Matcher matcher = SQLSERVER_PATTERN.matcher(jdbcUrl);
        if (matcher.find() && matcher.group(2) != null) {
            String params = matcher.group(2);
            for (String param : params.split(";")) {
                if (param.startsWith("databaseName=")) {
                    return param.substring("databaseName=".length());
                }
            }
        }
        return null;
    }
    
    private static List<JdbcUrlBuilderUtil.HostPort> extractSqlServerJtdsHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = SQLSERVER_JTDS_PATTERN.matcher(jdbcUrl);
        
        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");
            
            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 1433;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
            
            // 检查是否有failoverpartner
            if (matcher.group(3) != null) {
                String params = matcher.group(3);
                for (String param : params.split(";")) {
                    if (param.startsWith("failoverpartner=")) {
                        String failoverHost = param.substring("failoverpartner=".length());
                        hostPorts.add(new JdbcUrlBuilderUtil.HostPort(failoverHost, 1433)); // 使用默认端口
                    }
                }
            }
        }
        
        return hostPorts;
    }
    
    private static String extractSqlServerJtdsDatabase(String jdbcUrl) {
        Matcher matcher = SQLSERVER_JTDS_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }

    private static List<JdbcUrlBuilderUtil.HostPort> extractInformixHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = INFORMIX_PATTERN.matcher(jdbcUrl);
        
        if (matcher.find()) {
            String hostPortPair = matcher.group(1);
            String[] parts = hostPortPair.split(":");
            String host = parts[0].trim();
            int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 9088;
            hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
        }
        
        return hostPorts;
    }

    private static String extractInformixDatabase(String jdbcUrl) {
        Matcher matcher = INFORMIX_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }

    private static List<JdbcUrlBuilderUtil.HostPort> extractGreenplum6HostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = GREENPLUM6_PATTERN.matcher(jdbcUrl);
        
        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");
            
            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 5432;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }
        
        return hostPorts;
    }

    private static String extractGreenplum6Database(String jdbcUrl) {
        Matcher matcher = GREENPLUM6_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }

    private static String extractKingBaseDatabase(String jdbcUrl) {
        Matcher matcher = KINGBASE8_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }

    private static List<JdbcUrlBuilderUtil.HostPort> extractKingBaseHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = KINGBASE8_PATTERN.matcher(jdbcUrl);

        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");

            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 5432;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }

        return hostPorts;
    }
    private static String extractDMDatabase(String jdbcUrl) {
        Matcher matcher = DM_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }

    private static List<JdbcUrlBuilderUtil.HostPort> extractDMHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = DM_PATTERN.matcher(jdbcUrl);

        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");

            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 5432;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }

        return hostPorts;
    }
    private static String extractCkDatabase(String jdbcUrl) {
        Matcher matcher = CLICKHOUSE_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }

    private static List<JdbcUrlBuilderUtil.HostPort> extractCKHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = CLICKHOUSE_PATTERN.matcher(jdbcUrl);

        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");

            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 5432;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }

        return hostPorts;
    }

    private static List<JdbcUrlBuilderUtil.HostPort> extractDB2HostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = DB2_PATTERN.matcher(jdbcUrl);
        
        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");
            
            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 50000;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }
        
        return hostPorts;
    }

    private static String extractDB2Database(String jdbcUrl) {
        Matcher matcher = DB2_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }
    private static List<JdbcUrlBuilderUtil.HostPort> extractOcMysqlHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = OCEANMYSQL_PATTERN.matcher(jdbcUrl);

        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");

            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 50000;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }

        return hostPorts;
    }

    private static String extractOcMysqlDatabase(String jdbcUrl) {
        Matcher matcher = OCEANMYSQL_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }

    private static List<JdbcUrlBuilderUtil.HostPort> extractSapHostPorts(String jdbcUrl) {
        List<JdbcUrlBuilderUtil.HostPort> hostPorts = new ArrayList<>();
        Matcher matcher = SAP_PATTERN.matcher(jdbcUrl);

        if (matcher.find()) {
            String hostPortList = matcher.group(1);
            String[] hostPortPairs = hostPortList.split(",");

            for (String hostPortPair : hostPortPairs) {
                String[] parts = hostPortPair.split(":");
                String host = parts[0].trim();
                int port = (parts.length > 1) ? Integer.parseInt(parts[1].trim()) : 50000;
                hostPorts.add(new JdbcUrlBuilderUtil.HostPort(host, port));
            }
        }

        return hostPorts;
    }

    private static String extractSapDatabase(String jdbcUrl) {
        Matcher matcher = SAP_PATTERN.matcher(jdbcUrl);
        return matcher.find() && matcher.group(2) != null ? matcher.group(2) : null;
    }
} 