package com.joyadata.dsc.utils.jdbc;

/**
 * Enum representing the supported database types for JDBC URL building.
 */
public enum DatabaseJdbcUrlType {

    MYSQL("jdbc:mysql://%s:%d/%s", "******************************", 3306, 1, true),
    POSTGRESQL("jdbc:postgresql://%s:%d/%s", null, 5432, 4, true),  // PostgreSQL使用原生URL格式支持负载均衡
    ORACLE("****************************", "***************************************************************************************************************************************)))))", 1521, 2, true),
    ORACLE_19C("****************************", "***************************************************************************************************************************************)))))", 1521, 2015, true),
    SQLSERVER_JTDS("******************************", null, 1433, 3, false),  // jTDS 驱动
    SQLSERVER("**************************************", null, 1433, 32, false), // Microsoft SQLServerDriver 驱动
    TiDB("jdbc:mysql://%s:%d/%s", "******************************", 3306, 31, true),
    TDSQL_FOR_MySQL("jdbc:mysql://%s:%d/%s", "******************************", 3306, 1007, true),
    ADS("jdbc:mysql://%s:%d/%s", "******************************", 3306, 15, true),
    StarRocks("jdbc:mysql://%s:%d/%s", "******************************", 3306, 72, true),
    GoldenDB("jdbc:goldendb://%s:%d/%s", "", 3306, 73, true),
    DWS_MySQL("jdbc:mysql://%s:%d/%s", "", 3306, 2009, true),
    GaussDB_FOR_MySQL("jdbc:mysql://%s:%d/%s", "", 3306, 2003, true),
    Sequoiadb_FOR_MYSQL("jdbc:mysql://%s:%d/%s", "", 3306, 1005, true),
    MariaDB("jdbc:mysql://%s:%d/%s", "", 3306, 63, true),

    GaussDB("jdbc:postgresql://%s:%d/%s", null, 5432, 2000, true),
    INFORMIX("jdbc:informix-sqli://%s:%d/%s:INFORMIXSERVER=%s", null, 9088, 1003, true),
    GREENPLUM6("*********************************", null, 5432, 36, true),
    GREENPLUM_PostgreSQL("jdbc:postgresql://%s:%d/%s", null, 5432, 66, true),
    TBase("jdbc:postgresql://%s:%d/%s", null, 5432, 2018, true),
    TDSQL_FOR_PG("jdbc:postgresql://%s:%d/%s", null, 5432, 2019, true),
    TDSQL_FOR_ORACLE("jdbc:postgresql://%s:%d/%s", null, 5432, 2020, true),
    KINGBASE8("jdbc:kingbase8://%s:%d/%s", null, 54321, 40, true),
    DMDB("jdbc:dm://%s:%d/%s", null, 5236, 35, true),
    ClickHouse("jdbc:clickhouse://%s:%d/%s", null, 8123, 25, true),
    DB2("jdbc:db2://%s:%d/%s", null, 50000, 50, true),
    ADB_PostgreSQL("jdbc:postgresql://%s:%d/%s", null, 5432, 54, true),
    OceanBase_FOR_MySQL("jdbc:oceanbase://%s:%d/%s", null, 2881, 49, false),
    OceanBase_FOR_ORACLE("jdbc:oceanbase://%s:%d/%s", null, 2881, 1006, false),
    SAP_HANA("jdbc:sap://%s:%d/%s", null, 39015, 76, false),
    HIVE1("jdbc:hive2://%s:%d/%s", null, 10000, 27, false),
    HIVE2("jdbc:hive2://%s:%d/%s", null, 10000, 7, false),
    HIVE3("jdbc:hive2://%s:%d/%s", null, 10000, 50, false),
    HIVE3X_MRS("jdbc:hive2://%s:%d/%s", null, 10000, 74, false),
    ;

    private final String prefix;
    private final String loadBalancePrefix;  // 负载均衡前缀
    private final int defaultPort;
    private final int dataTypeCode;
    private final boolean requiresDatabase;  // 是否必须指定数据库名

    DatabaseJdbcUrlType(String prefix, String loadBalancePrefix, int defaultPort, int dataTypeCode, boolean requiresDatabase) {
        this.prefix = prefix;
        this.loadBalancePrefix = loadBalancePrefix;
        this.defaultPort = defaultPort;
        this.dataTypeCode = dataTypeCode;
        this.requiresDatabase = requiresDatabase;
    }

    /**
     * Get the JDBC URL prefix for this database type.
     *
     * @return the JDBC URL prefix
     */
    public String getPrefix() {
        return prefix;
    }

    /**
     * Get the load balance prefix for this database type.
     *
     * @return the load balance prefix, or null if not supported
     */
    public String getLoadBalancePrefix() {
        return loadBalancePrefix;
    }

    /**
     * Check if this database type supports load balancing.
     *
     * @return true if load balancing is supported
     */
    public boolean supportsLoadBalance() {
        return true; // 所有数据库都支持负载均衡，但实现方式不同
    }

    /**
     * Get the default port for this database type.
     *
     * @return the default port number
     */
    public int getDefaultPort() {
        return defaultPort;
    }

    /**
     * Get the data type code for this database type.
     *
     * @return the data type code
     */
    public int getDataTypeCode() {
        return dataTypeCode;
    }

    /**
     * Check if this database type requires a database name.
     *
     * @return true if database name is required
     */
    public boolean requiresDatabase() {
        return requiresDatabase;
    }

    /**
     * Get the default database name for this database type.
     *
     * @return the default database name
     */
    public String getDefaultDatabase() {
        switch (this) {
            case MYSQL:
            case TiDB:
            case TDSQL_FOR_MySQL:
            case StarRocks:
            case ADS:
            case GoldenDB:
            case Sequoiadb_FOR_MYSQL:
            case DWS_MySQL:
            case GaussDB_FOR_MySQL:
                return "mysql";
            case POSTGRESQL:
            case GaussDB:
            case TDSQL_FOR_PG:
            case ADB_PostgreSQL:
            case TDSQL_FOR_ORACLE:
                return "postgres";
            case ORACLE:
                return "ORCL";
            case SQLSERVER:
            case SQLSERVER_JTDS:
                return "master";
            case INFORMIX:
                return "sysmaster";
            case GREENPLUM6:
            case GREENPLUM_PostgreSQL:
                return "postgres";
            case KINGBASE8:
                return "test";
            case DMDB:
                return "SYSDBA";
            case ClickHouse:
                return "default";
            case DB2:
                return "DB2";
            default:
                return null;
        }
    }

    /**
     * Get DatabaseJdbcUrlType by data type code.
     *
     * @param dataTypeCode the data type code
     * @return the corresponding DatabaseJdbcUrlType
     * @throws IllegalArgumentException if no matching type is found
     */
    public static DatabaseJdbcUrlType fromDataTypeCode(int dataTypeCode) {
        for (DatabaseJdbcUrlType type : values()) {
            if (type.dataTypeCode == dataTypeCode) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的数据库类型代码: " + dataTypeCode + 
                "。有效的代码为: 1 (MySQL), 2 (Oracle), 3 (SQLServer jTDS), " +
                "4 (PostgreSQL), 25 (ClickHouse), 32 (SQLServer Microsoft), " +
                "35 (DMDB), 36 (Greenplum6), 40 (KingBase8), 50 (DB2), " +
                "66 (Greenplum PostgreSQL), 1003 (Informix)");
    }
}