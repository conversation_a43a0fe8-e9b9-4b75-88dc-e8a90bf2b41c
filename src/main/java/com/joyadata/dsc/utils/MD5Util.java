package com.joyadata.dsc.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5加密工具类
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
public class MD5Util {

    private static final Logger LOGGER = LoggerFactory.getLogger(MD5Util.class);

    /**
     * 获得表的md5值
     *
     * @param dbName  库名
     * @param tableName 表名
     * @param datasourceId 数据源uuid
     * @return 文件的md5
     */
    public static String tableIdToMD5(String dbName,String tableName, String datasourceId) {
        byte[] secretBytes = null;
        try {
            secretBytes =
                    MessageDigest.getInstance("md5").digest((dbName+"_"+tableName+"_"+datasourceId).getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有这个md5算法！");
        }
        String md5code = new BigInteger(1, secretBytes).toString(16);
        for (int i = 0; i < 32 - md5code.length(); i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }
    /**
     * 获得字段的md5值
     *
     * @param dbName  库名
     * @param tableId 表id
     * @param columnName 字段名
     * @param datasourceId 数据源uuid
     * @return 文件的md5
     */
    public static String columnToMD5(String dbName,String tableId,String columnName, String datasourceId) {
        byte[] secretBytes = null;
        try {
            secretBytes =
                    MessageDigest.getInstance("md5").digest((dbName+"_"+tableId+"_"+columnName+"_"+datasourceId).getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有这个md5算法！");
        }
        String md5code = new BigInteger(1, secretBytes).toString(16);
        for (int i = 0; i < 32 - md5code.length(); i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    /**
     * 获取资源md5 id
     * @param datasourceId
     * @param dbName
     * @param schema
     * @param tableName
     * @param type
     * @return
     */
    public static String resourceToMD5(String datasourceId,
                                       String dbName,
                                       String schema,
                                       String name,
                                       String type,
                                       String tableName
                                       ) {
        byte[] secretBytes = null;
        try {
            secretBytes =
                    MessageDigest.getInstance("md5").digest((datasourceId+dbName+schema+name+type+tableName).getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有这个md5算法！");
        }
        String md5code = new BigInteger(1, secretBytes).toString(16);
        for (int i = 0; i < 32 - md5code.length(); i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }
}