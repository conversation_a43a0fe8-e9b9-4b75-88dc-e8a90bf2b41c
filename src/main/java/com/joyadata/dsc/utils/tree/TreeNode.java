package com.joyadata.dsc.utils.tree;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName TreeNode
 * @Description 定义 TreeNode 类
 * <AUTHOR>
 * @Date 2025/2/26 16:00
 * @Version 1.0
 **/
@Data
public class TreeNode {
    private String id;
    private String name;
    private String pid;
    private Integer level;
    private Integer count;
    private List<TreeNode> children;

    public TreeNode() {
    }

    public TreeNode(String id, String name, String pid, Integer level) {
        this.id = id;
        this.name = name;
        this.pid = pid;
        this.level = level;
        this.count = 0;
        this.children = new ArrayList<>();
    }

    public void addChild(TreeNode child) {
        this.children.add(child);
    }
}
