package com.joyadata.dsc.utils.tree;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.dsc.model.datasoure.ClassifyTree;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName TreeUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/2/26 16:06
 * @Version 1.0
 **/
public class TreeUtil {

    /**
     * 构建树结构
     * @param nodes
     * @param rootId
     * @param calcCount
     * @return
     */
    public static TreeNode buildTree(List<TreeNode> nodes, String rootId, boolean calcCount) {
        Map<String, TreeNode> nodeMap = new HashMap<>();
        TreeNode root = null;

        // 先将所有节点存入 map 中
        for (TreeNode node : nodes) {
            nodeMap.put(node.getId(), node);
        }

        // 构建树结构
        for (TreeNode node : nodes) {
            String pid = node.getPid();
            if (rootId.equals(pid)) {
                root = node;
            } else {
                TreeNode parent = nodeMap.get(pid);
                if (parent != null) {
                    parent.addChild(node);
                }
            }
        }

        // 计算父节点的 count 值
        if (calcCount && null != root) {
            calculateParentCounts(root);
        }

        return root;
    }

    // 计算父节点的 count 值
    public static int calculateParentCounts(TreeNode node) {
        int totalCount = 0;
        for (TreeNode child : node.getChildren()) {
            totalCount += calculateParentCounts(child);
        }
        node.setCount(totalCount + node.getCount());
        return node.getCount();
    }

    /**
     * updateLeafCounts(node, "children", "leafCount")
     * @param node
     * @param childrenKey
     * @param countKey
     */
    public static void updateLeafCounts(JSONObject node, String childrenKey, String countKey) {
        if (null != node.getJSONArray(childrenKey)) {
            JSONArray children = node.getJSONArray(childrenKey);
            for (int i = 0; i < children.size(); i++) {
                JSONObject jsonObject = children.getJSONObject(i);
                jsonObject.put(countKey, getLeafNodeCount(jsonObject, childrenKey, countKey));
                updateLeafCounts(jsonObject, childrenKey, countKey);
            }
        }
    }

    /**
     *
     * @param node
     * @param childrenKey
     * @param countKey
     * @return
     */
    private static int getLeafNodeCount(JSONObject node, String childrenKey, String countKey) {
        if (null == node.getJSONArray(childrenKey)) {
            return node.getInteger(countKey);
        }
        int count = node.getInteger(countKey);
        JSONArray children = node.getJSONArray(childrenKey);
        for (int i = 0; i < children.size(); i++) {
            count += getLeafNodeCount(children.getJSONObject(i), childrenKey, countKey);
        }
        return count;
    }

    /**
     * 目录树断层则平铺成一级目录
     * @param nodeList
     * @return
     */
    public static List<TreeNode> treeList(List<TreeNode> nodeList) {
        Map<String, TreeNode> nodeMap = nodeList.stream().collect(Collectors.toMap(TreeNode::getId, Function.identity()));
        return nodeList.stream()
                .filter(menu -> null == nodeMap.get(menu.getPid()))
                .map(menu -> covertMenuNode(menu, nodeList))
                .collect(Collectors.toList());
    }

    /**
     * 目录树根据指定节点返回目录结构，断层舍弃
     * @param nodeList
     * @param pid
     * @return
     */
    public static List<TreeNode> treeList(List<TreeNode> nodeList, String pid) {
        return nodeList.stream()
                .filter(menu -> menu.getPid().equals(pid))
                .map(menu -> covertMenuNode(menu, nodeList))
                .collect(Collectors.toList());
    }

    private static TreeNode covertMenuNode(TreeNode menu, List<TreeNode> menuList) {
        TreeNode treeNode = new TreeNode();
        BeanUtils.copyProperties(menu, treeNode);
        List<TreeNode> children = menuList.stream()
                .filter(subMenu -> subMenu.getPid().equals(menu.getId()))
                .map(subMenu -> covertMenuNode(subMenu, menuList)).collect(Collectors.toList());
        treeNode.setChildren(children);
        return treeNode;
    }

    public static List<ClassifyTree> classifyTreeList(List<ClassifyTree> nodeList, String pid) {
        return nodeList.stream()
                .filter(menu -> menu.getPid().equals(pid))
                .map(menu -> covertMenuNode(menu, nodeList))
                .collect(Collectors.toList());
    }

    private static ClassifyTree covertMenuNode(ClassifyTree menu, List<ClassifyTree> menuList) {
        //ClassifyTree treeNode = new ClassifyTree();
        //BeanUtils.copyProperties(menu, treeNode);
        List<ClassifyTree> children = menuList.stream()
                .filter(subMenu -> subMenu.getPid().equals(menu.getId()))
                .map(subMenu -> covertMenuNode(subMenu, menuList)).collect(Collectors.toList());
        //treeNode.setChildren(children);
        menu.setChildren(children);
        return menu;
    }

    // 计算父节点的 count 值
    public static int calculateParentCounts(ClassifyTree node) {
        int totalCount = 0;
        for (ClassifyTree child : node.getChildren()) {
            totalCount += calculateParentCounts(child);
        }
        node.setCount(totalCount + node.getCount());
        return node.getCount();
    }

    public static void main(String[] args) {
        List<TreeNode> nodeList = new ArrayList<>();
        TreeNode treeNode0 = new TreeNode("001", "关系型", "-1", 0);
        TreeNode treeNode1 = new TreeNode("002", "大数据", "-1", 0);
        TreeNode treeNode2 = new TreeNode("003", "分析型", "-1", 0);
        TreeNode treeNode3 = new TreeNode("004", "分布式", "-1", 0);

        TreeNode treeNode4 = new TreeNode("000001", "MySQL", "001", 1);
        TreeNode treeNode5 = new TreeNode("000002", "Oracle", "001", 1);
        TreeNode treeNode6 = new TreeNode("000003", "sqlServer", "001", 1);

        TreeNode treeNode7 = new TreeNode("000004", "hive", "002", 1);
        TreeNode treeNode8 = new TreeNode("000005", "hdfs", "002", 1);

        TreeNode treeNode9 = new TreeNode("000006", "doris", "004", 1);
        TreeNode treeNode10 = new TreeNode("000007", "greenplum", "004", 1);

        TreeNode treeNode11 = new TreeNode("000000001", "数据源A", "000001", 2);
        treeNode11.setCount(5);
        TreeNode treeNode12 = new TreeNode("000000002", "数据源B", "000001", 2);
        treeNode12.setCount(3);

        nodeList.add(treeNode0);
        nodeList.add(treeNode1);
        nodeList.add(treeNode2);
        nodeList.add(treeNode3);
        nodeList.add(treeNode4);
        nodeList.add(treeNode5);
        nodeList.add(treeNode6);
        nodeList.add(treeNode7);
        nodeList.add(treeNode8);
        nodeList.add(treeNode9);
        nodeList.add(treeNode10);
        nodeList.add(treeNode11);
        nodeList.add(treeNode12);

        // 方式一 原结构不变，只会在原结构上setCount值
        List<TreeNode> treeNodes = treeList(nodeList, "-1");
        //for (TreeNode treeNode : treeNodes) {
        //    calculateParentCounts(treeNode);
        //}
        TreeNode root = new TreeNode("-1", "全部", "x00", 0);
        root.setChildren(treeNodes);
        calculateParentCounts(root);
        System.out.println(JSON.toJSONString(root));
        System.out.println(JSON.toJSONString(nodeList));

        // 方式二 会改变原结构
        root.setChildren(new ArrayList<>());
        nodeList.add(root);
        buildTree(nodeList, "x00", true);
        System.out.println(JSON.toJSONString(root));
    }
}
