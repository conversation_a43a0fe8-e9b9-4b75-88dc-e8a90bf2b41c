package com.joyadata.dsc.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Set;

/**
 * @ClassName FileUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/2/28 10:33
 * @Version 1.0
 **/
@Slf4j
public class FileUtil {

    /**
     * 删除文件夹，会递归地遍历文件夹并删除文件夹中的所有文件和子文件夹，直到最终删除目标文件夹。
     * 等于FileSystemUtils.deleteRecursively(folder);
     * @param folderPath
     * @throws IOException
     */
    public static void deleteFolder(Path folderPath) throws IOException {
        Files.walkFileTree(folderPath, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Files.delete(file);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                Files.delete(dir);
                return FileVisitResult.CONTINUE;
            }
        });
    }

    /**
     * 用于创建目标文件夹。如果文件夹已存在，则不进行任何操作。
     * @param dirPath
     * @return
     */
    public static boolean createDirectory(Path dirPath) {
        try {
            // 如果文件夹不存在，则创建文件夹
            if (Files.notExists(dirPath)) {
                Files.createDirectories(dirPath);
                return true;
            }
            return false;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    // 复制文件夹中的所有文件到新的文件夹
    public static void copyFilesToDirectory(Path sourceDir, Path targetDir) throws IOException {
        // 确保目标文件夹存在
        if (Files.notExists(targetDir)) {
            createDirectory(targetDir);
        }

        // 遍历源目录中的所有文件和子目录
        Files.walkFileTree(sourceDir, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                // 计算目标文件路径
                Path targetFile = targetDir.resolve(sourceDir.relativize(file));
                // 如果目标文件的父目录不存在，则创建它
                Files.createDirectories(targetFile.getParent());
                // 复制文件
                Files.copy(file, targetFile, StandardCopyOption.REPLACE_EXISTING);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                return FileVisitResult.CONTINUE;
            }
        });
    }

    /**
     * 复制文件夹中的所有文件到新的文件夹，排除某些文件
     * 此方法添加了一个参数 excludeFiles，它是一个包含要排除文件名的集合。如果文件名在集合中，则跳过该文件的复制。
     * @param sourceDir
     * @param targetDir
     * @param excludeFiles
     * @throws IOException
     */
    public static void copyFilesToDirectory(Path sourceDir, Path targetDir, Set<String> excludeFiles) throws IOException {
        if (Files.notExists(targetDir)) {
            createDirectory(targetDir);
        }

        // 遍历源目录中的所有文件和子目录
        Files.walkFileTree(sourceDir, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                // 排除指定的文件
                if (shouldExclude(file, excludeFiles)) {
                    return FileVisitResult.CONTINUE; // 跳过此文件
                }

                // 计算目标文件路径
                Path targetFile = targetDir.resolve(sourceDir.relativize(file));

                // 如果目标文件的父目录不存在，则创建它
                Files.createDirectories(targetFile.getParent());

                // 复制文件
                Files.copy(file, targetFile, StandardCopyOption.REPLACE_EXISTING);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                return FileVisitResult.CONTINUE;
            }
        });
    }

    /**
     * 判断文件是否在排除名单中
     * @param file
     * @param excludeFiles
     * @return
     */
    private static boolean shouldExclude(Path file, Set<String> excludeFiles) {
        String fileName = file.getFileName().toString();
        return excludeFiles.contains(fileName);
    }

    // 使用 FileOutputStream 将 byte[] 写入文件
    public static void writeToFile(byte[] content, String filePath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(content);
        }
    }

    // 使用 NIO Files.write 将 byte[] 写入文件
    public static void writeToFileNIO(byte[] content, String filePath) throws IOException {
        Path path = new File(filePath).toPath();
        Files.write(path, content);
    }
}
