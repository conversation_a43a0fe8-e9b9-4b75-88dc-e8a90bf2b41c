package com.joyadata.dsc.utils;

import java.util.ArrayList;
import java.util.List;

public class PaginationUtil {
    /**
     * 将列表按指定的页码和页面大小进行分页。
     *
     * @param list       要分页的列表
     * @param page 页码（从0开始）
     * @param pager   页面大小
     * @param <T>        列表元素类型
     * @return 分页后的列表
     */
    public static <T> List<T> paginate(List<T> list, int page, int pager) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        int fromIndex = page  * pager;
        if (fromIndex >= list.size()) {
            return new ArrayList<>();
        }

        int toIndex = Math.min(fromIndex + pager, list.size());
        return new ArrayList<>(list.subList(fromIndex, toIndex));

    }

}
