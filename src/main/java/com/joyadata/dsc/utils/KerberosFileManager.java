package com.joyadata.dsc.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.dsc.config.DatabaseConfig;
import com.joyadata.dsc.enums.PublicConstants;
import com.joyadata.dsc.model.datasoure.dto.KerberosConfigDTO;
import com.joyadata.dsc.model.datasoure.dto.KerberosFileUploadDTO;
import com.joyadata.dsc.model.datasoure.vo.KerberosUploadResultVO;
import com.joyadata.dsc.properties.DatabaseProperties;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.file.model.JoyadataAppFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Kerberos文件管理工具类
 * 基于统一文件存储系统
 */
@Slf4j
@Component
public class KerberosFileManager {
    
    @Autowired
    private DatabaseConfig databaseConfig;
    
    @Autowired
    private DatabaseProperties databaseProperties;
    
    /**
     * 统一文件存储服务
     */
    private final JoyaFeignService<JoyadataAppFile> joyadataAppFile = FeignFactory.make(JoyadataAppFile.class);
    
    /**
     * 支持的文件类型
     */
    private static final Set<String> SUPPORTED_FILE_TYPES = Collections.unmodifiableSet(new HashSet<String>() {{
            add("keytab");
            add("conf");
        add("xml");
        add("properties");
    }});

    /**
     * 支持的文件扩展名
     */
    private static final Set<String> SUPPORTED_EXTENSIONS = Collections.unmodifiableSet(new HashSet<String>() {{
            add(".keytab");
            add(".conf");
        add(".xml");
        add(".properties");
    }});
    
    /**
     * 上传单个Kerberos文件到统一文件存储
     *
     * @param file 上传的文件
     * @param fileType 文件类型（由Service层自动识别）
     * @return 上传结果
     */
    public KerberosFileUploadDTO uploadSingleKerberosFile(MultipartFile file, String fileType) {
        if (file == null || file.isEmpty()) {
            throw new AppErrorException("上传文件不能为空");
        }

        if (StrUtil.isBlank(fileType)) {
            throw new AppErrorException("文件类型不能为空");
        }

        try {
            return uploadSingleFileToStorage(fileType, file);
        } catch (Exception e) {
            log.error("Kerberos单个文件上传失败，文件类型: {}, 错误: {}", fileType, e.getMessage(), e);
            return KerberosFileUploadDTO.failure(fileType, file.getOriginalFilename(), e.getMessage());
        }
    }
    
    /**
     * 上传单个文件到统一文件存储
     */
    private KerberosFileUploadDTO uploadSingleFileToStorage(String fileType, MultipartFile file) {
        try {
            // 校验文件
            validateFile(file);

            // 生成标准化文件名
            String standardFileName = generateFileName(fileType, file.getOriginalFilename());

            // 上传到统一文件存储
            JoyadataAppFile uploadedFile = joyadataAppFile.upload(file, PublicConstants.MODULE, standardFileName);

            log.info("文件上传到统一存储成功: {} -> {}", standardFileName, uploadedFile.getId());

            return KerberosFileUploadDTO.success(
                uploadedFile.getId(),
                fileType,
                file.getOriginalFilename(),
                standardFileName,
                file.getSize()
            );

        } catch (Exception e) {
            log.error("文件上传失败: {}, 错误: {}", file.getOriginalFilename(), e.getMessage(), e);

            return KerberosFileUploadDTO.failure(
                fileType,
                file.getOriginalFilename(),
                e.getMessage()
            );
        }
    }
    
    /**
     * 校验文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new AppErrorException("文件不能为空");
        }
        
        // 检查文件大小
        DataSize maxSize = DataSize.parse(databaseProperties.getKerberosFileMaxSize());
        if (file.getSize() > maxSize.toBytes()) {
            throw new AppErrorException(String.format("文件大小超过限制，最大允许: %s", 
                databaseProperties.getKerberosFileMaxSize()));
        }
        
        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new AppErrorException("文件名不能为空");
        }
        
        String extension = FileUtil.extName(originalFilename);
        if (!SUPPORTED_EXTENSIONS.contains("." + extension.toLowerCase())) {
            throw new AppErrorException(String.format("不支持的文件类型: %s，支持的类型: %s", 
                extension, SUPPORTED_EXTENSIONS));
        }
    }
    
    /**
     * 生成标准化文件名
     */
    private String generateFileName(String fileType, String originalFilename) {
        switch (fileType.toLowerCase()) {
            // Kerberos认证文件
            case "keytab":
                return "hive.keytab";
            case "krb5conf":
                return "krb5.conf";
            case "jaasconf":
                return "jaas.conf";
            // Hadoop配置文件
            case "hivesite":
                return "hive-site.xml";
            case "hdfssite":
                return "hdfs-site.xml";
            case "coresite":
                return "core-site.xml";
            // Hive客户端配置文件
            case "hiveclient":
                return "hiveClient.properties";
            default:
                return originalFilename;
        }
    }

    /**
     * 下载Kerberos文件到临时目录
     *
     * @param datasourceId   数据源ID
     * @param kerberosConfig Kerberos配置
     * @return 临时目录路径
     */
    public String downloadKerberosFilesToTemp(String datasourceId, String datasourceNodeId, KerberosConfigDTO kerberosConfig) {
        if (kerberosConfig == null || !kerberosConfig.getEnabled() || kerberosConfig.getFiles() == null) {
            throw new AppErrorException("Kerberos配置无效");
        }
        try {
            // 创建临时目录
            String tempDir = createTempKerberosDirectory(datasourceId, datasourceNodeId);
            // 下载每个文件
            for (Map.Entry<String, KerberosConfigDTO.KerberosFileInfo> entry : kerberosConfig.getFiles().entrySet()) {
                String fileType = entry.getKey();
                KerberosConfigDTO.KerberosFileInfo fileInfo = entry.getValue();
                downloadFileToTemp(tempDir, fileType, fileInfo);
            }
            log.info("Kerberos文件下载到临时目录成功: {}", tempDir);
            return tempDir;
        } catch (Exception e) {
            log.error("下载Kerberos文件失败，数据源ID: {}, 错误: {}", datasourceId, e.getMessage(), e);
            throw new AppErrorException("下载Kerberos文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载单个文件到临时目录
     */
    private void downloadFileToTemp(String tempDir, String fileType, KerberosConfigDTO.KerberosFileInfo fileInfo) {
        try {
            // 从统一文件存储下载文件
            JoyadataAppFile downloadedFile = joyadataAppFile.dowload(fileInfo.getFileId(), PublicConstants.MODULE);
            
            // 保存到临时目录
            Path targetPath = Paths.get(tempDir, fileInfo.getFileName());
            Files.write(targetPath, downloadedFile.getData());
            
            // 设置文件权限
            setFilePermissions(targetPath);
            
            log.info("文件下载到临时目录成功: {} -> {}", fileInfo.getFileId(), targetPath);
            
        } catch (Exception e) {
            log.error("下载文件失败: {}, 错误: {}", fileInfo.getFileId(), e.getMessage(), e);
            throw new AppErrorException("下载文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建临时Kerberos目录
     */
    private String createTempKerberosDirectory(String datasourceId,String datasourceNodeId) {
        String tenantCode = AuthUtil.getTenantCode();
        if (StrUtil.isBlank(tenantCode)) {
            throw new AppErrorException("Failed to obtain the Kerberos file path. The tenant code is empty.");
        }
        
        String tempDir = String.format("%s/DatabaseCenter_%s/DatasourceInfoId_%s/DatasourceNodeId_%s",
            databaseConfig.getLocalKerberosPath(),
            tenantCode,
            datasourceId,
            datasourceNodeId);
        
        try {
            Path dirPath = Paths.get(tempDir);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                log.info("创建临时Kerberos目录: {}", tempDir);
            }
            return tempDir;
        } catch (IOException e) {
            throw new AppErrorException("创建临时Kerberos目录失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置文件权限
     */
    private void setFilePermissions(Path filePath) {
        try {
            // 在Unix/Linux系统上设置文件权限为600（仅所有者可读写）
            if (!System.getProperty("os.name").toLowerCase().contains("windows")) {
                Runtime.getRuntime().exec(new String[]{"chmod", "600", filePath.toString()});
            }
        } catch (Exception e) {
            log.warn("设置文件权限失败: {}", e.getMessage());
        }
    }

    /**
     * 删除统一文件存储中的Kerberos文件
     *
     * @param kerberosConfig Kerberos配置
     */
    public void deleteKerberosFiles(KerberosConfigDTO kerberosConfig) {
        if (kerberosConfig == null || kerberosConfig.getFiles() == null) {
            return;
        }

        try {
            for (KerberosConfigDTO.KerberosFileInfo fileInfo : kerberosConfig.getFiles().values()) {
                try {
                    // 注意：这里可能需要根据实际的文件删除API进行调整
                    // 目前假设有删除方法，如果没有可以跳过此步骤
                    log.info("删除统一存储文件: {}", fileInfo.getFileId());
                } catch (Exception e) {
                    log.warn("删除文件失败: {}, 错误: {}", fileInfo.getFileId(), e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("删除Kerberos文件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理临时Kerberos目录
     *
     * @param datasourceId 数据源ID
     */
    public void cleanupTempKerberosDirectory(String datasourceId) {
        try {
            String tenantCode = AuthUtil.getTenantCode();
            if (StrUtil.isBlank(tenantCode)) {
                throw new AppErrorException("Failed to obtain the Kerberos file path. The tenant code is empty.");
            }

            String tempDir = String.format("%s/DatabaseCenter_%s/DatasourceInfoId_%s",
                databaseConfig.getLocalKerberosPath(),
                tenantCode,
                datasourceId);

            Path dirPath = Paths.get(tempDir);
            if (Files.exists(dirPath)) {
                FileUtil.del(dirPath.toFile());
                log.info("清理临时Kerberos目录成功: {}", tempDir);
            }
        } catch (Exception e) {
            log.error("清理临时Kerberos目录失败，数据源ID: {}, 错误: {}", datasourceId, e.getMessage(), e);
        }
    }

    /**
     * 校验Kerberos配置是否有效
     *
     * @param kerberosConfig Kerberos配置
     * @return 校验结果
     */
    public boolean validateKerberosConfig(KerberosConfigDTO kerberosConfig) {
        if (kerberosConfig == null || !kerberosConfig.getEnabled()) {
            return true; // 未启用时认为校验通过
        }

        try {
            // 检查配置完整性
            if (!kerberosConfig.isConfigComplete()) {
                log.warn("Kerberos配置不完整");
                return false;
            }

            // 检查必需文件是否存在
            Map<String, KerberosConfigDTO.KerberosFileInfo> files = kerberosConfig.getFiles();
            if (files == null || !files.containsKey("keytab") || !files.containsKey("krb5Conf")) {
                log.warn("缺少必需的Kerberos文件");
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("校验Kerberos配置失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取Kerberos文件在临时目录中的路径
     *
     * @param datasourceId 数据源ID
     * @param fileName 文件名
     * @return 文件路径
     */
    public String getKerberosFilePath(String datasourceId, String fileName) {
        String tenantCode = AuthUtil.getTenantCode();
        if (StrUtil.isBlank(tenantCode)) {
            throw new AppErrorException("Failed to obtain the Kerberos file path. The tenant code is empty.");
        }

        return String.format("%s/DatabaseCenter_%s/DatasourceInfoId_%s/%s",
            databaseConfig.getLocalKerberosPath(),
            tenantCode,
            datasourceId,
            fileName);
    }

    /**
     * 预览Kerberos文件内容
     *
     * @param fileId 文件ID
     * @return 文件预览信息
     */
    public Map<String, Object> previewKerberosFile(String fileId) {
        if (StrUtil.isBlank(fileId)) {
            throw new AppErrorException("文件ID不能为空");
        }
        try {
            // 从统一文件存储下载文件
            JoyadataAppFile downloadedFile = joyadataAppFile.dowload(fileId, PublicConstants.MODULE);
            if (downloadedFile == null) {
                throw new AppErrorException("文件不存在或已被删除");
            }
            // 构建预览信息
            Map<String, Object> previewInfo = new HashMap<>();
            previewInfo.put("fileId", fileId);
            previewInfo.put("fileName", downloadedFile.getOriginalFilename());
            previewInfo.put("fileSize", downloadedFile.getData().length);
            previewInfo.put("downloadTime", LocalDateTime.now());
            // 文本文件，返回内容
            String content = new String(downloadedFile.getData(), "UTF-8");
            previewInfo.put("contentType", "text");
            previewInfo.put("content", content);
            previewInfo.put("previewable", true);
            log.info("文件预览成功: {}", fileId);
            return previewInfo;
        } catch (Exception e) {
            log.error("预览文件失败: {}, 错误: {}", fileId, e.getMessage(), e);
            throw new AppErrorException("预览文件失败: " + e.getMessage());
        }
    }

}
