package com.joyadata.dsc.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.dsc.config.DatabaseConfig;
import com.joyadata.dsc.model.datasoure.dto.KerberosConfigDTO;
import com.joyadata.dsc.model.datasoure.vo.KerberosUploadResultVO;
import com.joyadata.dsc.properties.DatabaseProperties;
import com.joyadata.exception.AppErrorException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

/**
 * Kerberos文件管理工具类
 */
@Slf4j
@Component
public class KerberosFileManager {
    
    @Autowired
    private DatabaseConfig databaseConfig;
    
    @Autowired
    private DatabaseProperties databaseProperties;
    
    /**
     * 支持的Kerberos文件类型
     */
    private static final Set<String> SUPPORTED_FILE_TYPES = Set.of(
        "keytab", "conf"
    );
    
    /**
     * 支持的文件扩展名
     */
    private static final Set<String> SUPPORTED_EXTENSIONS = Set.of(
        ".keytab", ".conf"
    );
    
    /**
     * 保存Kerberos文件
     * 
     * @param datasourceId 数据源ID
     * @param files 文件映射 (文件类型 -> 文件对象)
     * @return 上传结果
     */
    public KerberosUploadResultVO saveKerberosFiles(String datasourceId, Map<String, MultipartFile> files) {
        if (StrUtil.isBlank(datasourceId)) {
            throw new AppErrorException("数据源ID不能为空");
        }
        
        if (files == null || files.isEmpty()) {
            throw new AppErrorException("上传文件不能为空");
        }
        
        try {
            // 创建Kerberos目录
            String kerberosDir = createKerberosDirectory(datasourceId);
            
            List<KerberosUploadResultVO.FileInfo> uploadedFiles = new ArrayList<>();
            
            // 处理每个文件
            for (Map.Entry<String, MultipartFile> entry : files.entrySet()) {
                String fileType = entry.getKey();
                MultipartFile file = entry.getValue();
                
                KerberosUploadResultVO.FileInfo fileInfo = processFile(kerberosDir, fileType, file);
                uploadedFiles.add(fileInfo);
            }
            
            log.info("Kerberos文件上传成功，数据源ID: {}, 文件数量: {}", datasourceId, uploadedFiles.size());
            return KerberosUploadResultVO.success(datasourceId, uploadedFiles, kerberosDir);
            
        } catch (Exception e) {
            log.error("Kerberos文件上传失败，数据源ID: {}, 错误: {}", datasourceId, e.getMessage(), e);
            return KerberosUploadResultVO.failure(datasourceId, e.getMessage());
        }
    }
    
    /**
     * 处理单个文件
     */
    private KerberosUploadResultVO.FileInfo processFile(String kerberosDir, String fileType, MultipartFile file) 
            throws IOException {
        
        // 校验文件
        validateFile(file);
        
        // 生成文件名
        String fileName = generateFileName(fileType, file.getOriginalFilename());
        Path targetPath = Paths.get(kerberosDir, fileName);
        
        // 保存文件
        Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
        
        // 设置文件权限（仅所有者可读写）
        setFilePermissions(targetPath);
        
        // 计算文件哈希
        String fileHash = DigestUtil.sha256Hex(file.getInputStream());
        
        // 创建文件信息
        KerberosUploadResultVO.FileInfo fileInfo = new KerberosUploadResultVO.FileInfo();
        fileInfo.setFileName(fileName);
        fileInfo.setFileSize(file.getSize());
        fileInfo.setFileType(fileType);
        fileInfo.setFileHash(fileHash);
        fileInfo.setFilePath(targetPath.toString());
        fileInfo.setUploaded(true);
        
        return fileInfo;
    }
    
    /**
     * 校验文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new AppErrorException("文件不能为空");
        }
        
        // 检查文件大小
        DataSize maxSize = DataSize.parse(databaseProperties.getKerberosFileMaxSize());
        if (file.getSize() > maxSize.toBytes()) {
            throw new AppErrorException(String.format("文件大小超过限制，最大允许: %s", 
                databaseProperties.getKerberosFileMaxSize()));
        }
        
        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new AppErrorException("文件名不能为空");
        }
        
        String extension = FileUtil.extName(originalFilename);
        if (!SUPPORTED_EXTENSIONS.contains("." + extension.toLowerCase())) {
            throw new AppErrorException(String.format("不支持的文件类型: %s，支持的类型: %s", 
                extension, SUPPORTED_EXTENSIONS));
        }
    }
    
    /**
     * 生成标准化文件名
     */
    private String generateFileName(String fileType, String originalFilename) {
        String extension = FileUtil.extName(originalFilename);
        
        switch (fileType.toLowerCase()) {
            case "keytab":
                return "hive.keytab";
            case "krb5conf":
                return "krb5.conf";
            case "jaasconf":
                return "jaas.conf";
            default:
                return originalFilename;
        }
    }
    
    /**
     * 设置文件权限
     */
    private void setFilePermissions(Path filePath) {
        try {
            // 在Unix/Linux系统上设置文件权限为600（仅所有者可读写）
            if (!System.getProperty("os.name").toLowerCase().contains("windows")) {
                Runtime.getRuntime().exec(new String[]{"chmod", "600", filePath.toString()});
            }
        } catch (Exception e) {
            log.warn("设置文件权限失败: {}", e.getMessage());
        }
    }

    /**
     * 创建Kerberos目录
     * 目录结构：{localKerberosPath}/DatabaseCenter_{tenantCode}/DatasourceInfoId_{datasourceId}/
     *
     * @param datasourceId 数据源ID
     * @return 创建的目录路径
     */
    public String createKerberosDirectory(String datasourceId) {
        String tenantCode = AuthUtil.getTenantCode();
        if (StrUtil.isBlank(tenantCode)) {
            throw new AppErrorException("租户编码不能为空");
        }
        String kerberosDir = String.format("%s/DatabaseCenter_%s/DatasourceInfoId_%s",
            databaseConfig.getLocalKerberosPath(),
            tenantCode,
            datasourceId);

        try {
            Path dirPath = Paths.get(kerberosDir);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                log.info("创建Kerberos目录: {}", kerberosDir);
            }
            return kerberosDir;
        } catch (IOException e) {
            throw new AppErrorException("创建Kerberos目录失败: " + e.getMessage());
        }
    }

    /**
     * 获取Kerberos目录路径
     *
     * @param datasourceId 数据源ID
     * @return 目录路径
     */
    public String getKerberosDirectory(String datasourceId) {
        String tenantCode = AuthUtil.getTenantCode();
        if (StrUtil.isBlank(tenantCode)) {
            throw new AppErrorException("租户编码不能为空");
        }

        return String.format("%s/DatabaseCenter_%s/DatasourceInfoId_%s",
            databaseConfig.getLocalKerberosPath(),
            tenantCode,
            datasourceId);
    }

    /**
     * 获取Kerberos文件完整路径
     *
     * @param datasourceId 数据源ID
     * @param fileName 文件名
     * @return 文件完整路径
     */
    public String getKerberosFilePath(String datasourceId, String fileName) {
        return Paths.get(getKerberosDirectory(datasourceId), fileName).toString();
    }

    /**
     * 校验Kerberos文件是否存在且有效
     *
     * @param datasourceId 数据源ID
     * @param kerberosConfig Kerberos配置
     * @return 校验结果
     */
    public boolean validateKerberosFiles(String datasourceId, KerberosConfigDTO kerberosConfig) {
        if (kerberosConfig == null || !kerberosConfig.getEnabled()) {
            return true; // 未启用Kerberos时认为校验通过
        }

        try {
            String[] requiredFiles = kerberosConfig.getRequiredFiles();
            for (String fileName : requiredFiles) {
                String filePath = getKerberosFilePath(datasourceId, fileName);
                Path path = Paths.get(filePath);

                if (!Files.exists(path)) {
                    log.warn("Kerberos文件不存在: {}", filePath);
                    return false;
                }

                if (!Files.isReadable(path)) {
                    log.warn("Kerberos文件不可读: {}", filePath);
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("校验Kerberos文件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 清理Kerberos文件
     *
     * @param datasourceId 数据源ID
     */
    public void cleanupKerberosFiles(String datasourceId) {
        try {
            String kerberosDir = getKerberosDirectory(datasourceId);
            Path dirPath = Paths.get(kerberosDir);

            if (Files.exists(dirPath)) {
                FileUtil.del(dirPath.toFile());
                log.info("清理Kerberos文件成功: {}", kerberosDir);
            }
        } catch (Exception e) {
            log.error("清理Kerberos文件失败，数据源ID: {}, 错误: {}", datasourceId, e.getMessage(), e);
        }
    }

    /**
     * 检查Kerberos目录是否存在
     *
     * @param datasourceId 数据源ID
     * @return 是否存在
     */
    public boolean kerberosDirectoryExists(String datasourceId) {
        String kerberosDir = getKerberosDirectory(datasourceId);
        return Files.exists(Paths.get(kerberosDir));
    }
}
