package com.joyadata.dsc.utils;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;

/**
 * telnet util
 */
public class TelnetUtils {

    // 默认超时时间（3秒）
    private static final int DEFAULT_TIMEOUT_MS = 3000;

    // 检测主机和端口是否可达（使用默认超时时间）
    public static boolean isReachable(String host, int port) {
        return isReachable(host, port, DEFAULT_TIMEOUT_MS);
    }

    // 检测主机和端口是否可达（自定义超时时间）
    public static boolean isReachable(String host, int port, int timeoutMs) {
        validateParameters(host, port, timeoutMs);
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), timeoutMs);
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    // 参数校验
    private static void validateParameters(String host, int port, int timeoutMs) {
        if (host == null || host.trim().isEmpty()) {
            throw new IllegalArgumentException("Host must not be null or empty");
        }
        if (port < 1 || port > 65535) {
            throw new IllegalArgumentException("Port must be between 1 and 65535");
        }
        if (timeoutMs <= 0) {
            throw new IllegalArgumentException("Timeout must be a positive value");
        }
    }

}