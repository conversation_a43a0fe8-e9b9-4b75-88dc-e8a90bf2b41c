package com.joyadata.dsc.utils.excel;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.joyadata.cms.model.Dept;
import com.joyadata.cms.model.User;
import com.joyadata.dsc.enums.BusinessEnum;
import com.joyadata.dsc.enums.BusinessStatusEnum;
import com.joyadata.dsc.model.business.BusinessSystem;
import com.joyadata.dsc.model.migration.dto.BusinessSystemTemplate;
import com.joyadata.dsc.service.BusinessSystemService;
import com.joyadata.dsc.utils.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import com.joyadata.dsc.model.migration.vo.BusinessSystemImportResult;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据迁移模板导入监听器
 * 用于导入业务系统相关数据
 * <AUTHOR>
 */
@Slf4j
public class SheetForDataMigrationBusinessListener extends AnalysisEventListener<BusinessSystemTemplate> {

    private final BusinessSystemService businessSystemService;

    private final List<BusinessSystemTemplate> rawDataList = new ArrayList<>();
    private final List<BusinessSystem> addedList = new ArrayList<>();
    private final List<BusinessSystem> updatedList = new ArrayList<>();
    private final Map<String, String> errBusinessSystemMap = new HashMap<>();

    public SheetForDataMigrationBusinessListener(BusinessSystemService businessSystemService) {
        this.businessSystemService = businessSystemService;
    }

    @Override
    public void invoke(BusinessSystemTemplate template, AnalysisContext context) {
        rawDataList.add(template);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel解析完成，共计 {} 条记录，开始处理...", rawDataList.size());
        processImportData();
    }

    private void processImportData() {
        List<BusinessSystem> existList = businessSystemService.getListByDel(false);
        //是否在恢复里面
        List<BusinessSystem> businessSystemList = businessSystemService.getListByDel(true);
        Map<String, BusinessSystem> existMap = new HashMap<>();
        if(CollUtil.isNotEmpty(existList)){
            existList.forEach(item -> existMap.put(item.getBusinessName(), item));
        }
        Map<String, Dept> deptMap = businessSystemService.getDeptMap(true);
        Map<String, User> userMap = businessSystemService.getUserMap(true);

        List<String> businessNames =new ArrayList<>();
        List<String> delBusinessNames =new ArrayList<>();
        List<String> businessNamesImport =new ArrayList<>();
        //获取业务名称 生成新只有业务名称得集合List<String>
        if(CollUtil.isNotEmpty(existList)){
            businessNames = existList.stream().map(BusinessSystem::getBusinessName).collect(Collectors.toList());
        }
        if(CollUtil.isNotEmpty(businessSystemList)){
            delBusinessNames = businessSystemList.stream().map(BusinessSystem::getBusinessName).collect(Collectors.toList());
        }
        for (BusinessSystemTemplate vo : rawDataList) {
            StringBuilder errMsg = new StringBuilder();
            String businessName = vo.getBusinessName();

            if (StringUtils.isBlank(businessName)) {
                errMsg.append("业务系统名称为空；");
            }
//            //业务系统名称，哪些是重复得
//            if(businessNames.contains(businessName)){
//                errMsg.append("和平台业务系统名称重复;");
//            }

            //业务系统被删除
            if(delBusinessNames.contains(businessName)){
                errMsg.append("此业务系统被删除,请自行恢复;");
            }
            //新导入得业务系统名称，哪些是重复得
            if (businessNamesImport.contains(businessName)) {
                errMsg.append("文件中业务系统名称重复;");
            }
            businessNamesImport.add(businessName);

            String departName = vo.getDepartName();
            String manager = vo.getManager();
            //校验部门和负责人
            if (StringUtils.isEmpty(departName) ){
                errMsg.append("所属部门为空;");
            }else{
                //todo 部门数据处理
                //校验部门名称是否在部门表内
                if(!deptMap.containsKey(departName)){
                    errMsg.append("所属部门不存在系统内;");
                }else{
                    //部门id
                    Dept dept = deptMap.get(departName);
                    vo.setDepartName(dept.getId());
                    //获取部门id
                    String deptId = dept.getId();
                    if(StringUtils.isEmpty(manager)){
                        errMsg.append("负责人为空;");
                    }else{
                        //todo 负责人数据处理
                        //校验负责人名称是否在用户表内
                        manager=manager+"-"+deptId;
                        if(!userMap.containsKey(manager)){
                            errMsg.append("负责人为空不在系统内;");
                        }else{
                            //负责人id
                            User user = userMap.get(manager);
                            vo.setManager(user.getId());
                        }
                    }
                }

            }


            // 状态校验
            String businessStatus = vo.getBusinessStatus();
            if (StringUtils.isBlank(businessStatus)) {
                vo.setBusinessStatus(BusinessStatusEnum.RUNNING.getType());
            } else if (!BusinessStatusEnum.getBusinessStatusList().contains(businessStatus)) {
                errMsg.append("业务状态不合法；");
            } else {
                vo.setBusinessStatus(BusinessStatusEnum.getByChineseName(businessStatus).getType());
            }

            // 类型校验
            String businessType = vo.getBusinessType();
            if (StringUtils.isBlank(businessType)) {
                vo.setBusinessType(BusinessEnum.BUSINESSSYSTEM.getType());
            } else if (!BusinessEnum.getBusinessTypeList().contains(businessType)) {
                errMsg.append("业务类型不合法；");
            } else {
                vo.setBusinessType(BusinessEnum.getByChineseName(businessType).getType());
            }

            //安全等级处理
            String securityLevel = vo.getSecurityLevel();
            if (StringUtils.isNotEmpty(securityLevel)) {
                //去掉第一个L
                securityLevel = securityLevel.substring(1);
                vo.setSecurityLevel(securityLevel);
            }
            // 错误处理
            if (errMsg.length() > 0) {
                errBusinessSystemMap.put(businessName, errMsg.toString());
                continue;
            }

            // 区分新增/更新:在存在业务系统表中
            if (existMap.containsKey(businessName) ) {
                BusinessSystem exist = existMap.get(businessName);
                BeanUtils.copyProperties(vo, exist);
                updatedList.add(exist);
            } else {
                BusinessSystem newSystem = new BusinessSystem();
                //设置一个id
                newSystem.setId(IdUtils.simpleUUID());
                BeanUtils.copyProperties(vo, newSystem);
                addedList.add(newSystem);
            }
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        log.error("Excel读取异常", exception);
        throw new RuntimeException("Excel读取异常：" + exception.getMessage(), exception);
    }

    /**
     * 获取导入结果
     */
    public BusinessSystemImportResult getImportResult() {
        BusinessSystemImportResult result = new BusinessSystemImportResult();
        result.setAddedList(addedList);
        result.setUpdatedList(updatedList);
        result.setErrBusinessSystemMap(errBusinessSystemMap);
        return result;
    }

    /**
     * 校验日期格式 yyyy-MM-dd
     */
    private boolean isValidDate(String dateStr) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate.parse(dateStr, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }
}
