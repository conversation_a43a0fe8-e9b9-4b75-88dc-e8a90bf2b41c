package com.joyadata.dsc.utils.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.joyadata.dsc.model.migration.dto.DatasourceInfoTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 数据源导入监听器（仅校验 数据源名称 和 业务系统名称）
 * <AUTHOR>
 */
@Slf4j
public class SheetForDataMigrationDatasourceListener extends AnalysisEventListener<DatasourceInfoTemplate> {

    private final List<DatasourceInfoTemplate> rawList = new ArrayList<>();

    // 校验通过的有效数据：分组存储
    private final Map<String, List<DatasourceInfoTemplate>> validGroupMap = new LinkedHashMap<>();

    // 校验失败数据及原因
    private final Map<DatasourceInfoTemplate, String> errorMap = new LinkedHashMap<>();

    @Override
    public void invoke(DatasourceInfoTemplate data, AnalysisContext context) {
        rawList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 分组 key: 业务系统名称 + 数据源名称
        Map<String, List<DatasourceInfoTemplate>> groupMap = new LinkedHashMap<>();

        for (DatasourceInfoTemplate item : rawList) {
            String businessName = item.getBusinessName();
            String datasourceName = item.getDatasourceName();

            if (!StringUtils.hasText(businessName) || !StringUtils.hasText(datasourceName)) {
                errorMap.put(item, "业务系统名称或数据源名称为空");
                continue;
            }

            String key = businessName.trim() + "::" + datasourceName.trim();
            groupMap.computeIfAbsent(key, k -> new ArrayList<>()).add(item);
        }

        // 判断每组是否存在空的 datasourceName（在多个节点的情况下算作失败）
        for (Map.Entry<String, List<DatasourceInfoTemplate>> entry : groupMap.entrySet()) {
            List<DatasourceInfoTemplate> group = entry.getValue();
            boolean hasEmptyName = group.stream().anyMatch(item -> !StringUtils.hasText(item.getBusinessName()));

            if (hasEmptyName && group.size() > 1) {
                for (DatasourceInfoTemplate item : group) {
                    errorMap.put(item, "同一业务系统下的数据源存在多个节点，且部分业务系统名称为空");
                }
            } else {
                validGroupMap.put(entry.getKey(), group);
            }
        }

        log.info("数据源导入处理完成：总数={}, 成功组数={}, 失败={}", rawList.size(), validGroupMap.size(), errorMap.size());
    }

    public Map<String, List<DatasourceInfoTemplate>> getValidGroupMap() {
        return validGroupMap;
    }

    public Map<DatasourceInfoTemplate, String> getErrorMap() {
        return errorMap;
    }


}
