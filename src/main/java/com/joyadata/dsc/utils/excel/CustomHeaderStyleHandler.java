package com.joyadata.dsc.utils.excel;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.AbstractVerticalCellStyleStrategy;

public class CustomHeaderStyleHandler extends AbstractVerticalCellStyleStrategy {
    public CustomHeaderStyleHandler() {
        super();
    }

    private static WriteCellStyle createHeaderStyle() {
        WriteCellStyle headerStyle = new WriteCellStyle();
        WriteFont headerFont = new WriteFont();
        headerFont.setBold(true); // 加粗
        headerStyle.setWriteFont(headerFont);
        return headerStyle;
    }
}

