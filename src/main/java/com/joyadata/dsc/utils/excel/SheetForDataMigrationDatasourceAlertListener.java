package com.joyadata.dsc.utils.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.cms.model.Role;
import com.joyadata.cms.model.User;
import com.joyadata.dsc.enums.NoticeWayEnum;
import com.joyadata.dsc.enums.ReceiveControlEnum;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.DatasourceAlertEvent;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.migration.dto.DatasourceAlertTemplate;
import com.joyadata.tms.model.Project;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.sql.Time;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据源告警
 * <AUTHOR>
 */
@Slf4j
public class SheetForDataMigrationDatasourceAlertListener extends AnalysisEventListener<DatasourceAlertTemplate> {

    public DatasourceInfo datasourceInfo;
    //所有告警事件
    public List<DatasourceAlertEvent> datasourceAlertEvents;
    //所有用户
    public List<User> users;
    //所有角色
    public List<Role> roles;
    //所有项目
    public List<Project> projects;

    //写一个构造方法 传数据源id
    public SheetForDataMigrationDatasourceAlertListener(DatasourceInfo datasourceInfo,
                                                        List<DatasourceAlertEvent> datasourceAlertEvents,
                                                        List<User> users,
                                                        List<Role> roles,
                                                        List<Project> projects
                                                        ){
        this.datasourceInfo=datasourceInfo;
        this.datasourceAlertEvents=datasourceAlertEvents;
        this.users=users;
        this.roles=roles;
        this.projects=projects;
    }
    private final List<DatasourceAlertTemplate> rawList = new ArrayList<>();

    // 校验通过的有效数据：分组存储
    private final Map<String, List<DatasourceAlertTemplate>> validGroupMap = new LinkedHashMap<>();

    // 校验失败数据及原因
    private final Map<DatasourceAlertTemplate, String> errorMap = new LinkedHashMap<>();
    private final DatasourceAlert datasourceAlert = new DatasourceAlert();

    @Override
    public void invoke(DatasourceAlertTemplate data, AnalysisContext context) {
        rawList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        //获取所有用户
        //获取所有角色
        //获取所有项目
        //获取所有告警事件
        for (DatasourceAlertTemplate item : rawList) {
            String datasourceName = item.getDatasourceName();
            if (!StringUtils.hasText(datasourceName)) {
                errorMap.put(item, "数据源名称为空");
                continue;
            }
            //告警事件
            String eventName = item.getEventName();
            if (!StringUtils.hasText(eventName)) {
                errorMap.put(item, "告警事件为空");
                continue;
            }
            if(!item.equals(datasourceInfo.getDatasourceName())){
                continue;
            }
            datasourceAlert.setDatasourceInfoId(datasourceInfo.getId());
            //判断告警事件名称是不是在告警事件列表中,eventName 事件是以逗号分隔的
            List<String> eventNameList = StrUtil.split(eventName, StrUtil.COMMA);
            List<DatasourceAlertEvent> roleCollList = datasourceAlertEvents.stream()
                    .filter(role -> eventNameList.stream().anyMatch(name -> role.getEventName().equals(name)))
                    .collect(Collectors.toList());

            if (roleCollList.size() != eventNameList.size()) {
                errorMap.put(item, "告警事件有不满足要求得值");
                continue;
            }
            datasourceAlert.setDatasourceAlertEvents(roleCollList);
            //通知方式
            String noticeWay = item.getNoticeWay();
            if (!StringUtils.hasText(noticeWay)) {
                errorMap.put(item, "通知方式为空");
                continue;
            }
            //判断通知方式是不是在通知方式列表中
            boolean validCode = NoticeWayEnum.isValidCode(noticeWay);
            if (!validCode) {
                errorMap.put(item, "通知方式不存在");
                continue;
            }
            //转换值
            datasourceAlert.setNoticeWay(NoticeWayEnum.getCodeByDescription(noticeWay));
            //如果是http通知方式 设置请求方式、请求地址
            if (NoticeWayEnum.HTTP.getCode().equals(noticeWay)) {
                String requestMethod = item.getRequestMethod();
                if (!StringUtils.hasText(requestMethod)) {
                    errorMap.put(item, "请求方式为空");
                    continue;
                }
                datasourceAlert.setRequestMethod(requestMethod);
                String requestUrl = item.getRequestUrl();
                if (!StringUtils.hasText(requestUrl)) {
                    errorMap.put(item, "请求地址为空");
                    continue;
                }
                datasourceAlert.setRequestUrl(requestUrl);
            }
            //通知范围：user、role、project {个人："",角色:"",项目：""}
            String noticeScope = item.getNoticeType();
            if (!StringUtils.hasText(noticeScope)) {
                errorMap.put(item, "通知范围为空");
                continue;
            }
            //先转为json
            JSONObject json = JSONObject.parseObject(noticeScope);
            List<String> userNames = json.getJSONArray("个人").toJavaList(String.class);
            List<String> roleNames = json.getJSONArray("角色").toJavaList(String.class);
            List<String> projectNames = json.getJSONArray("项目").toJavaList(String.class);
            //若用户名称不为空，则判断用户名称是不是在用户列表中 若在则获取用户id，若重复取第一个
            if (CollUtil.isNotEmpty(userNames)) {
                List<User> userList = users.stream()
                        .filter(role -> userNames.stream().anyMatch(name -> role.getUsername().equals(name)))
                        .collect(Collectors.toList());
                if (userList.size() != userNames.size()) {
                    errorMap.put(item, "用户名称有不满足要求得值");
                    continue;
                }
                //设置用户：将用户id拼成字符串
                datasourceAlert.setUserIds(userList.stream().map(User::getId).collect(Collectors.joining(",")));
                datasourceAlert.setUserNames(userList.stream().map(User::getUsername).collect(Collectors.joining(",")));
            }
            //若角色名称不为空，则判断角色名称是不是在角色列表中 若在则获取角色id，若重复取第一个
            if (CollUtil.isNotEmpty(roleNames)) {
                List<Role> roleList = roles.stream()
                        .filter(role -> roleNames.stream().anyMatch(name -> role.getName().equals(name)))
                        .collect(Collectors.toList());
                if (roleList.size() != roleNames.size()) {
                    errorMap.put(item, "角色名称有不满足要求得值");
                    continue;
                }
                //设置角色：将角色id拼成字符串
                datasourceAlert.setRoleIds(roleList.stream().map(Role::getId).collect(Collectors.joining(",")));
                datasourceAlert.setRoleNames(roleList.stream().map(Role::getName).collect(Collectors.joining(",")));
            }
            //若项目名称不为空，则判断项目名称是不是在项目列表中 若在则获取项目id，若重复取第一个
            if (CollUtil.isNotEmpty(projectNames)) {
                List<Project> projectList = projects.stream()
                        .filter(role -> projectNames.stream().anyMatch(name -> role.getName().equals(name)))
                        .collect(Collectors.toList());
                if (projectList.size() != projectNames.size()) {
                    errorMap.put(item, "项目名称有不满足要求得值");
                }
                //设置项目：将项目id拼成字符串
                datasourceAlert.setProjectIds(projectList.stream().map(Project::getId).collect(Collectors.joining(",")));
                datasourceAlert.setProjectNames(projectList.stream().map(Project::getName).collect(Collectors.joining(",")));
            }
            //频率控制
            String receiveControl =item.getReceiveControl();
            if (!StringUtils.hasText(receiveControl)) {
                errorMap.put(item, "频率控制为空");
                continue;
            }
            //判断频率控制是不是在频率控制列表中
            boolean validDesc = ReceiveControlEnum.isValidCode(receiveControl);
            if (!validDesc) {
                errorMap.put(item, "频率控制不存在");
                continue;
            }
            Integer codeByDescription = ReceiveControlEnum.getCodeByDescription(receiveControl);
            datasourceAlert.setReceiveControl(codeByDescription);
            datasourceAlert.setMsgFormat(item.getMsgFormat());
            //若频率控制为指定时段，则获取指定时段开始时间和结束时间 且将时间转换为Date
            if (ReceiveControlEnum.FIXED_PERIOD.getCode() == codeByDescription) {
                //校验时间格式
                String appointAlarmStartTime = item.getAppointAlarmStartTime();
                //判空
                if (!StringUtils.hasText(appointAlarmStartTime)) {
                    errorMap.put(item, "开始时间不能为空");
                    continue;
                }
                Time effectiveStartDateDate = validateAndParse(appointAlarmStartTime);
                if(Objects.isNull(effectiveStartDateDate)){
                    errorMap.put(item,"开始时间格式错误");
                    continue;
                }
                String appointAlarmEndTime = item.getAppointAlarmEndTime();
                //判空
                if (!StringUtils.hasText(appointAlarmEndTime)) {
                    errorMap.put(item, "结束时间不能为空");
                    continue;
                }
                Time effectiveEndDateDate = validateAndParse(appointAlarmEndTime);
                if(Objects.isNull(effectiveEndDateDate)){
                    errorMap.put(item,"结束时间格式错误");
                    continue;
                }
                datasourceAlert.setAppointAlarmStartTime(effectiveStartDateDate);
                datasourceAlert.setAppointAlarmEndTime(effectiveEndDateDate);
            }
        }
        log.info("数据源导入处理完成：总数={}, 成功组数={}, 失败={}", rawList.size(), validGroupMap.size(), errorMap.size());
    }

    public Map<String, List<DatasourceAlertTemplate>> getValidGroupMap() {
        return validGroupMap;
    }

    public Map<DatasourceAlertTemplate, String> getErrorMap() {
        return errorMap;
    }
    public DatasourceAlert getDatasourceAlert() {
        return datasourceAlert;
    }
    public static Time validateAndParse(String dateStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd  HH:mm:ss");
            // 设置为严格校验模式
            sdf.setLenient(false);
            // 将字符串解析为 Time 对象
            return new Time(sdf.parse(dateStr).getTime());
        } catch (ParseException e) {
            // 解析失败，返回 null 或者记录日志
            log.error("时间格式错误:{}",dateStr);
            return null;
        }
    }

}
