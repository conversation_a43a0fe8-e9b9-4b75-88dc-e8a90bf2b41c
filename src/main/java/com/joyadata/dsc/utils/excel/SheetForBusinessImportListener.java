package com.joyadata.dsc.utils.excel;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.joyadata.cms.model.Dept;
import com.joyadata.cms.model.User;
import com.joyadata.dsc.enums.BusinessEnum;
import com.joyadata.dsc.enums.BusinessImportEnum;
import com.joyadata.dsc.enums.BusinessStatusEnum;
import com.joyadata.dsc.model.business.BusinessSystem;
import com.joyadata.dsc.model.business.BusinessSystemImportErrorVO;
import com.joyadata.dsc.model.business.BusinessSystemImportVO;
import com.joyadata.dsc.service.BusinessSystemService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务系统导入
 */
@Slf4j
@Component
public class SheetForBusinessImportListener extends AnalysisEventListener<BusinessSystemImportVO> {

    private BusinessSystemService businessSystemService;
    public SheetForBusinessImportListener (BusinessSystemService businessSystemService){
        this.businessSystemService=businessSystemService;
    }


    private List<BusinessSystemImportVO> datasourceInfoImportVOS = new ArrayList<>();
    private List<BusinessSystemImportErrorVO> businessSystemImportErrorVOS = new ArrayList<>();


    private List<String> businessNamesExists = new ArrayList<>();

    @SneakyThrows
    @Override
    public void invoke(BusinessSystemImportVO datasourceInfoImportVO, AnalysisContext analysisContext) {
        datasourceInfoImportVOS.add(datasourceInfoImportVO);
    }


    public List<BusinessSystemImportErrorVO> getBusinessSystemImportErrorVOS() {
        return businessSystemImportErrorVOS;
    }

    public List<String> getBusinessNamesExist() {
        return businessNamesExists;
    }


    public List<BusinessSystemImportVO> getDataList() {
        if (datasourceInfoImportVOS.size() > 0) {
             List<BusinessSystemImportVO> datasourceInfoImportVOList = compareValueData(datasourceInfoImportVOS);
            return datasourceInfoImportVOList;
        }
        return datasourceInfoImportVOS;
    }

    private List<BusinessSystemImportVO> compareValueData(List<BusinessSystemImportVO> dataList) {

        List<BusinessSystemImportVO> datasourceInfoImportVOList = new ArrayList<>();


        //获取所有业务系统
//        List<BusinessSystem> datasourceBusinesses = businessSystemService.getQuery().list();

         List<String> businessNames =new ArrayList<>();
         List<String> delBusinessNames =new ArrayList<>();
//        if(CollUtil.isNotEmpty(datasourceBusinesses)){
//            // 获取 delflag 为 0 的元素
//            List<BusinessSystem> filteredBusinesses = datasourceBusinesses.stream()
//                    .filter(business -> !business.getDelFlag() )  // 根据 delflag 值过滤
//                    .collect(Collectors.toList());  // 收集到新的 List

        List<BusinessSystem> filteredBusinesses = businessSystemService.getListByDel(false);
        List<BusinessSystem> delBusinesses = businessSystemService.getListByDel(true);

        // 获取 delflag 为 0 的元素
//            List<BusinessSystem> delBusinesses = datasourceBusinesses.stream()
//                    .filter(business -> business.getDelFlag() )  // 根据 delflag 值过滤
//                    .collect(Collectors.toList());  // 收集到新的 List
            //获取业务名称 生成新只有业务名称得集合List<String>
            if(CollUtil.isNotEmpty(filteredBusinesses)){
                businessNames = filteredBusinesses.stream().map(BusinessSystem::getBusinessName).collect(Collectors.toList());
            }
            if(CollUtil.isNotEmpty(delBusinesses)){
                delBusinessNames = delBusinesses.stream().map(BusinessSystem::getBusinessName).collect(Collectors.toList());
            }

//       }
       //先判断业务系统是否为空
         int n = 1;
        List<String> businessNamesImport =new ArrayList<>();
        for (BusinessSystemImportVO datasourceBusinessImportVO : dataList) {
            n++;
            //判断业务系统是否存在
            //检验业务系统
            String businessName = datasourceBusinessImportVO.getBusinessName();
            StringBuilder errMsg=new StringBuilder();
            if (StringUtils.isEmpty(businessName)) {
                errMsg.append("业务系统名称为空;");
            }
             //业务系统名称，哪些是重复得
               if(businessNames.contains(businessName)){
                errMsg.append("和平台业务系统名称重复;");
                 businessNamesExists.add(businessName);
            }
               //业务系统被删除
            if(delBusinessNames.contains(businessName)){
                errMsg.append("此业务系统被删除,请自行恢复;");
            }
            //新导入得业务系统名称，哪些是重复得
            if (businessNamesImport.contains(businessName)) {
                errMsg.append("文件中业务系统名称重复;");
                businessNamesExists.add(businessName);
            }
            businessNamesImport.add(businessName);


             String departName = datasourceBusinessImportVO.getDepartName();
            String manager = datasourceBusinessImportVO.getManager();
            //校验部门和负责人
            if (StringUtils.isEmpty(departName) ){
                errMsg.append("所属部门为空;");
            }else{
                //todo 部门数据处理
                //校验部门名称是否在部门表内
                 Map<String, Dept> deptMap = businessSystemService.getDeptMap(true);
                if(!deptMap.containsKey(departName)){
                    errMsg.append("所属部门不存在系统内;");
                }else{
                    //部门id
                     Dept dept = deptMap.get(departName);
                    datasourceBusinessImportVO.setDepartName(dept.getId());
                    //获取部门id
                    String deptId = dept.getId();
                    if(StringUtils.isEmpty(manager)){
                        errMsg.append("负责人为空;");
                    }else{
                        //todo 负责人数据处理
                        //校验负责人名称是否在用户表内
                        Map<String, User> userMap = businessSystemService.getUserMap(true);
                         manager=manager+"-"+deptId;
                        if(!userMap.containsKey(manager)){
                            errMsg.append("负责人为空不在系统内;");
                        }else{
                            //负责人id
                            User user = userMap.get(manager);
                            datasourceBusinessImportVO.setManager(user.getId());
                        }
                    }
                }

            }

            String businessStatus = datasourceBusinessImportVO.getBusinessStatus();
            if (StringUtils.isEmpty(businessStatus) ){
                //为空时：默认为在用
                datasourceBusinessImportVO.setBusinessStatus(BusinessStatusEnum.RUNNING.getType());
            }else{
                //校验业务状态值
                //1.判断输入得业务状态是否在规定得数组内
                if (!BusinessStatusEnum.getBusinessStatusList().contains(businessStatus)) {
                    errMsg.append("业务状态不在规定范围内;");
                }else {
                    //2.将业务状态转换为type 重新设置
                    datasourceBusinessImportVO.setBusinessStatus(BusinessStatusEnum.getByChineseName(businessStatus).getType());

                }
           }
            String businessType = datasourceBusinessImportVO.getBusinessType();
            if (StringUtils.isEmpty(businessType) ){
                //为空时：默认为业务系统
                datasourceBusinessImportVO.setBusinessType(BusinessEnum.BUSINESSSYSTEM.getType());
            }else{
                //校验业务类型
                if (!BusinessEnum.getBusinessTypeList().contains(businessType)) {
                    errMsg.append("业务系统不在规定范围内;");
                }else{
                    datasourceBusinessImportVO.setBusinessType(BusinessEnum.getByChineseName(businessType).getType());
                }

            }
            //当msg不为空时，添加到错误集合中
            if (StringUtils.isNotEmpty(errMsg) ) {
                //将BusinessSystemImportVO 转化为 BusinessSystemImportErrorVO
                BusinessSystemImportErrorVO businessSystemImportErrorVO = new BusinessSystemImportErrorVO();
                BeanUtils.copyProperties(datasourceBusinessImportVO, businessSystemImportErrorVO);
                businessSystemImportErrorVO.setErrReason(errMsg.toString());
                businessSystemImportErrorVO.setDepartName(departName);
                businessSystemImportErrorVO.setBusinessStatus(businessStatus);
                businessSystemImportErrorVO.setBusinessType(businessType);
               businessSystemImportErrorVOS.add(businessSystemImportErrorVO);
            }else{
                datasourceInfoImportVOList.add(datasourceBusinessImportVO);
            }
        }

        return datasourceInfoImportVOList;
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("SheetForBusinessImportListener监听器数据解析完毕");
    }

    public List<BusinessSystemImportVO> getData() {
        return datasourceInfoImportVOS;
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        throw exception;
    }

    /**
     * 校验日期格式
     * @param date
     * @return
     */
    public  boolean isValidDate(String date) {
        try {
            String DATE_PATTERN = "yyyy-MM-dd";
            DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);
            LocalDate.parse(date, DATE_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }
}
