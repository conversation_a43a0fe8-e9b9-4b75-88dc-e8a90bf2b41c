package com.joyadata.dsc.utils.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.joyadata.dsc.enums.MetadataCollectionRangeEnum;
import com.joyadata.dsc.enums.MetadataTaskObjectEnum;
import com.joyadata.dsc.enums.MetadataTaskWayEnum;
import com.joyadata.dsc.enums.SwitchStatus;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.metadata.MetadataSelect;
import com.joyadata.dsc.model.metadata.MetadataTask;
import com.joyadata.dsc.model.migration.dto.DatasourceInfoTemplate;
import com.joyadata.dsc.model.migration.dto.MataDataTaskTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 采集任务（仅校验 数据源名称 ）
 * <AUTHOR>
 */
@Slf4j
public class SheetForDataMigrationTaskListener extends AnalysisEventListener<MataDataTaskTemplate> {

    public DatasourceInfo datasourceInfo;
    //数据源名称

    //写一个构造方法 传数据源id
    public SheetForDataMigrationTaskListener(DatasourceInfo datasourceInfo){
        this.datasourceInfo=datasourceInfo;
    }
    private final List<MataDataTaskTemplate> rawList = new ArrayList<>();

    // 校验通过的有效数据：分组存储
    private final Map<String, List<MataDataTaskTemplate>> validGroupMap = new LinkedHashMap<>();

    // 校验失败数据及原因
    private final Map<MataDataTaskTemplate, String> errorMap = new LinkedHashMap<>();
    private final MetadataTask metadataTask = new MetadataTask();

    @Override
    public void invoke(MataDataTaskTemplate data, AnalysisContext context) {
        rawList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 分组 key:  数据源名称
        Map<String, List<MataDataTaskTemplate>> groupMap = new LinkedHashMap<>();

        for (MataDataTaskTemplate item : rawList) {
            String datasourceName = item.getDatasourceName();
            if (!StringUtils.hasText(datasourceName)) {
                errorMap.put(item, "数据源名称为空");
                continue;
            }
            //类型不能为空
            if(!StringUtils.hasText(item.getType())){
                errorMap.put(item, "采集类型为空");
                continue;
            }
            //按数据源名称和类型 进行分组
            String key =  datasourceName.trim();
            groupMap.computeIfAbsent(key, k -> new ArrayList<>()).add(item);
        }

        //判断分组的数据源 是否存在所传入的数据源
        if(!groupMap.containsKey(datasourceInfo.getDatasourceName())){
            MataDataTaskTemplate taskTemplate=new MataDataTaskTemplate();
            taskTemplate.setDatasourceName(datasourceInfo.getDatasourceName());
            errorMap.put(taskTemplate,"不包含此数据源");
            return;
        }
        // 判断每组是否存在空的,即：每组数据源下 采集范围若有值必须保持一致 否则则为失败 ，
        for (Map.Entry<String, List<MataDataTaskTemplate>> entry : groupMap.entrySet()) {
            //1.类型若为file 则验证 文件路径 、是否递归、采集层级数据 ：a 都不能为空 b 递归 只能是0和1 采集层级 只能是大于0的数字
            String datasource = entry.getKey();
            //判断数据源名称和传进来的是否一致
            if(!datasource.equals(datasourceInfo.getDatasourceName())){
                continue;
            }
            List<MataDataTaskTemplate> templates = entry.getValue();
            //每个数据源 创建一个采集任务配置对象

            // === file 类型校验 ===
            for (MataDataTaskTemplate item : templates) {
                if (MetadataTaskObjectEnum.FILE.getType().equalsIgnoreCase(item.getType())) {
                    if (!StringUtils.hasText(item.getFilePath())) {
                        errorMap.put(item,"数据源 [" + datasource + "] 的 file 类型记录 filePath 不能为空");
                       continue;
                    }
                    if (item.getMaxLevel() == null || item.getMaxLevel() <= 0) {
                        errorMap.put(item,"数据源 [" + datasource + "] 的 file 类型记录 maxLevel 不能为空且必须大于 0");
                        continue;
                    }
                    if (item.getIsRecursion() == null || (item.getIsRecursion() != 0 && item.getIsRecursion() != 1)) {
                        errorMap.put(item,"数据源 [" + datasource + "] 的 file 类型记录 isRecursion 只能为 0 或 1");
                        continue;
                    }
                    metadataTask.setIsRecursion(item.getIsRecursion());
                    metadataTask.setIsDefault(SwitchStatus.OFF.getCode());
                    metadataTask.setFilePath(item.getFilePath());
                    metadataTask.setFilePathSpecified(item.getFilePathSpecified());
                    metadataTask.setMaxLevel(item.getMaxLevel());
                    metadataTask.setBlacklist(item.getBlacklist());
                    metadataTask.setWhitelist(item.getWhitelist());
                    metadataTask.setTaskType(SwitchStatus.ON.getCode());
                    metadataTask.setDatasourceInfoId(datasourceInfo.getId());
                    return;
                }
            }

            // === 非 file 类型校验 ===
            List<MataDataTaskTemplate> nonFile = templates.stream()
                    .filter(t -> !MetadataTaskObjectEnum.FILE.getType().equalsIgnoreCase(t.getType()))
                    .collect(Collectors.toList());
            if (!nonFile.isEmpty()) {
                Set<String> scopes = new HashSet<>();

                //选择表
                String selectTable="";
                //采集方式
                String taskWay="";
                //采集对象
                String taskObject="";
                String scope="";
                List<MetadataSelect> metadataSelectList=new ArrayList<>();

                for (MataDataTaskTemplate t : nonFile) {
                     scope = t.getAcquisitionScope();
                    // 空采集范围
                    if (!StringUtils.hasText(scope)) {
                        errorMap.put(t, "数据源 [" + datasource + "] 存在非 file 类型记录 acquisitionScope 为空");
                        continue;
                    }

                    // 非法采集范围
                    if (!MetadataCollectionRangeEnum.isValidDesc(scope)) {
                        errorMap.put(t, "数据源 [" + datasource + "] 存在非法 acquisitionScope：" + scope);
                        continue;
                    }

                    // 合法采集范围收集
                    scopes.add(scope);
                    MetadataSelect metadataSelect =new MetadataSelect();
                    // 检查采集范围所需字段
                    switch (scope) {
                        case "表选择":
                            if (!StringUtils.hasText(t.getDbName())) {
                                errorMap.put(t, "数据源 [" + datasource + "] 表选择 缺少 dbName");
                                continue;
                            }
                            if (!StringUtils.hasText(t.getTableName())) {
                                errorMap.put(t, "数据源 [" + datasource + "] 表选择 缺少 tableName");
                                continue;
                            }
                            metadataSelect.setDbName(t.getDbName());
                            metadataSelect.setSchemaName(t.getSchemaName());
                            metadataSelect.setName(t.getTableName());
                            metadataSelect.setTableName(t.getIndexTableName());
                            metadataSelect.setType(t.getType());
                            metadataSelect.setDatasourceInfoId(datasourceInfo.getId());
                            metadataSelectList.add(metadataSelect);
                            taskWay=t.getTaskWay();
                            break;

                        case "库选择":
                            if (!StringUtils.hasText(t.getDbName())) {
                                errorMap.put(t, "数据源 [" + datasource + "] 库选择 缺少 dbName");
                                continue;
                            }
                            if (!StringUtils.hasText(t.getTaskObject())) {
                                errorMap.put(t, "数据源 [" + datasource + "] 库选择 缺少 taskObject");
                                continue;
                            }
                            metadataSelect.setDbName(t.getDbName());
                            metadataSelect.setSchemaName(t.getSchemaName());
                            metadataSelect.setType(t.getType());
                            metadataSelect.setDatasourceInfoId(datasourceInfo.getId());
                            metadataSelectList.add(metadataSelect);
                            taskWay=t.getTaskWay();
                            taskObject=t.getTaskObject();
                            metadataTask.setBlacklist(t.getBlacklist());
                            metadataTask.setWhitelist(t.getWhitelist());
                            break;
                        case "快速选表":
                            if (!StringUtils.hasText(t.getDbName())) {
                                errorMap.put(t, "数据源 [" + datasource + "] 快速选表 缺少 dbName");
                                continue;
                            }
                            if (!StringUtils.hasText(t.getTableName())) {
                                errorMap.put(t, "数据源 [" + datasource + "] 快速选表 缺少 tableName");
                                continue;
                            }
                            if (!StringUtils.hasText(t.getTaskObject())) {
                                errorMap.put(t, "数据源 [" + datasource + "] 快速选表 缺少 taskObject");
                                continue;
                            }
                            String table=t.getDbName()+"."+t.getTableName();
                            if(StringUtils.hasText(t.getSchemaName())){
                                table=t.getDbName()+"."+t.getSchemaName()+"."+t.getTableName();
                            }
                            selectTable+=table+",";
                            taskWay=t.getTaskWay();
                            taskObject=t.getTaskObject();
                            break;
                    }

                }
                //转换一下采集方式
                if(!StringUtils.hasText(taskWay)){
                    metadataTask.setTaskWay(MetadataTaskWayEnum.ALL.getCode());
                }else {
                    MetadataTaskWayEnum byChineseName = MetadataTaskWayEnum.getByChineseName(taskWay);
                    metadataTask.setTaskWay(byChineseName.getCode());
                }
                //转换一下采集范围
                MetadataCollectionRangeEnum metadataCollectionRangeEnum = MetadataCollectionRangeEnum.fromDesc(scope);
                metadataTask.setAcquisitionScope(metadataCollectionRangeEnum.getCode());
                if(StringUtils.hasText(selectTable)){
                    metadataTask.setSelectTable(selectTable.substring(0,selectTable.lastIndexOf(",")));
                }
                metadataTask.setIsDefault(SwitchStatus.ON.getCode());
                metadataTask.setDatasourceInfoId(datasourceInfo.getId());
                metadataTask.setTaskObject(taskObject);
                metadataTask.setMetadataSelect(metadataSelectList);
                metadataTask.setMetadataTaskRecordId("0");
            }
        }

        log.info("数据源导入处理完成：总数={}, 成功组数={}, 失败={}", rawList.size(), validGroupMap.size(), errorMap.size());
    }

    public Map<String, List<MataDataTaskTemplate>> getValidGroupMap() {
        return validGroupMap;
    }

    public Map<MataDataTaskTemplate, String> getErrorMap() {
        return errorMap;
    }
    public MetadataTask getMataDataTask() {
        return metadataTask;
    }

}
