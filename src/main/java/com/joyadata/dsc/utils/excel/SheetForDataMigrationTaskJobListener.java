package com.joyadata.dsc.utils.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.joyadata.dsc.enums.SwitchStatus;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.metadata.MetadataTask;
import com.joyadata.dsc.model.migration.dto.MataDataTaskJobTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 采集任务（仅校验 数据源名称 ）
 * <AUTHOR>
 */
@Slf4j
public class SheetForDataMigrationTaskJobListener extends AnalysisEventListener<MataDataTaskJobTemplate> {

    public DatasourceInfo datasourceInfo;
    //数据源名称

    //写一个构造方法 传数据源id
    public SheetForDataMigrationTaskJobListener(DatasourceInfo datasourceInfo){
        this.datasourceInfo=datasourceInfo;
    }
    private final List<MataDataTaskJobTemplate> rawList = new ArrayList<>();

    // 校验通过的有效数据：分组存储
    private final Map<String, List<MataDataTaskJobTemplate>> validGroupMap = new LinkedHashMap<>();

    // 校验失败数据及原因
    private final Map<MataDataTaskJobTemplate, String> errorMap = new LinkedHashMap<>();
    private final MetadataTask metadataTask = new MetadataTask();

    @Override
    public void invoke(MataDataTaskJobTemplate data, AnalysisContext context) {
        rawList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 分组 key:  数据源名称
        Map<String, List<MataDataTaskJobTemplate>> groupMap = new LinkedHashMap<>();

        for (MataDataTaskJobTemplate item : rawList) {
            String datasourceName = item.getDatasourceName();
            if (!StringUtils.hasText(datasourceName)) {
                errorMap.put(item, "数据源名称为空");
                continue;
            }
            if (!StringUtils.hasText(item.getIsTiming())) {
                errorMap.put(item, "数据源定时配置为空");
                continue;
            }
            //按数据源名称和类型 进行分组
            String key =  datasourceName.trim();
            groupMap.computeIfAbsent(key, k -> new ArrayList<>()).add(item);
        }

        //判断分组的数据源 是否存在所传入的数据源
        if(!groupMap.containsKey(datasourceInfo.getDatasourceName())){
            MataDataTaskJobTemplate taskTemplate=new MataDataTaskJobTemplate();
            taskTemplate.setDatasourceName(datasourceInfo.getDatasourceName());
            errorMap.put(taskTemplate,"不包含此数据源");
            return;
        }
        // 判断每组是否存在空的,即：每组数据源下 采集范围若有值必须保持一致 否则则为失败 ，
        for (Map.Entry<String, List<MataDataTaskJobTemplate>> entry : groupMap.entrySet()) {
            //1.类型若为file 则验证 文件路径 、是否递归、采集层级数据 ：a 都不能为空 b 递归 只能是0和1 采集层级 只能是大于0的数字
            String datasource = entry.getKey();
            //判断数据源名称和传进来的是否一致
            if(!datasource.equals(datasourceInfo.getDatasourceName())){
                continue;
            }
            List<MataDataTaskJobTemplate> templates = entry.getValue();
            //每个数据源 创建一个采集任务配置对象
            //若是关闭则直接返回
            metadataTask.setDatasourceInfoId(datasourceInfo.getId());
            metadataTask.setIsDefault(SwitchStatus.ON.getCode());
            for (MataDataTaskJobTemplate template : templates) {
                String isTiming = template.getIsTiming();
                if(SwitchStatus.OFF.getDescription().equalsIgnoreCase(isTiming)){
                    metadataTask.setIsTiming(SwitchStatus.OFF.getCode());
                    return;
                }
                //如果是开启 则校验数据
                //生效时间
                if(SwitchStatus.ON.getDescription().equalsIgnoreCase(isTiming)){
                    String effectiveStartDate = template.getEffectiveStartDate();
                    if(!StringUtils.hasText(effectiveStartDate)){
                        errorMap.put(template,"生效开始时间为空");
                        continue;
                    }
                    Date effectiveStartDateDate = validateAndParse(effectiveStartDate);
                    if(Objects.isNull(effectiveStartDateDate)){
                        errorMap.put(template,"生效开始时间格式错误");
                        continue;
                    }
                    String effectiveEndDate = template.getEffectiveEndDate();
                    if(!StringUtils.hasText(effectiveEndDate)){
                        errorMap.put(template,"生效结束时间为空");
                        continue;
                    }
                    metadataTask.setEffectiveStartDate(effectiveStartDateDate);
                    Date effectiveEndDateDate = validateAndParse(effectiveEndDate);
                    if(Objects.isNull(effectiveEndDateDate)){
                        errorMap.put(template,"生效结束时间格式错误");
                        continue;
                    }
                    metadataTask.setEffectiveEndDate(effectiveEndDateDate);

                    if(metadataTask.getEffectiveStartDate().after(metadataTask.getEffectiveEndDate())){
                        errorMap.put(template,"生效时间错误:开始时间在结束时间之后");
                        continue;
                    }
                    //频率类型
                    String cronType = template.getCronType();
                    if(!StringUtils.hasText(cronType)){
                        errorMap.put(template,"频率类型为空");
                        continue;
                    }
                    if("推荐".equalsIgnoreCase(cronType)){
                        metadataTask.setCronType(SwitchStatus.ON.getCode());
                    }
                    if("自定义".equalsIgnoreCase(cronType)){
                        metadataTask.setCronType(SwitchStatus.OFF.getCode());
                    }
                    //获取corn
                    String executionFrequency = template.getExecutionFrequency();
                    if(!StringUtils.hasText(executionFrequency)){
                        errorMap.put(template,"定时配置为空");
                        continue;
                    }
                    metadataTask.setExecutionFrequency(executionFrequency);
                    metadataTask.setIsTiming(SwitchStatus.ON.getCode());
                    metadataTask.setIsDefault(SwitchStatus.ON.getCode());
                    metadataTask.setCronType(SwitchStatus.ON.getCode());
                    return;
                }
                errorMap.put(template,"开启定时得值不在允许得范围内");
            }
        }

        log.info("数据源导入处理完成：总数={}, 成功组数={}, 失败={}", rawList.size(), validGroupMap.size(), errorMap.size());
    }
    public static Date validateAndParse(String dateStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd  HH:mm:ss");
            // 设置为严格校验模式
            sdf.setLenient(false);
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            // 解析失败，返回 null 或者记录日志
            log.error("时间格式错误:{}",dateStr);
            return null;
        }
    }
    public Map<String, List<MataDataTaskJobTemplate>> getValidGroupMap() {
        return validGroupMap;
    }

    public Map<MataDataTaskJobTemplate, String> getErrorMap() {
        return errorMap;
    }
    public MetadataTask getMataDataTask() {
        return metadataTask;
    }

}
