package com.joyadata.dsc.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.joyadata.dsc.enums.PublicConstants;
import com.joyadata.dsc.model.datasoure.*;
import com.joyadata.dsc.model.datasoure.dto.ReplaceDatasourceDTO;
import com.joyadata.dsc.utils.jdbc.JdbcUrlBuilderUtil;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.file.model.JoyadataAppFile;
import com.joyadata.service.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据源工具
 */
@Component
public class DatasourceUtil {

    @Autowired
    private BaseServiceImpl baseServiceImpl;

    private static JoyaFeignService<JoyadataAppFile> joyadataAppFile = FeignFactory.make(JoyadataAppFile.class);

    /**
     * 根据数据源信息id获取数据源信息
     * @param datasourceInfoId 数据源信息id
     * @return datasourceX中所使用的数据源信息
     */
    public DatasourceDTO getDatasourceDTO(String datasourceInfoId) {
        DatasourceDTO result = null;
        DatasourceInfo datasourceInfo = (DatasourceInfo) baseServiceImpl.getService(DatasourceInfo.class).getQuery().eq("id", datasourceInfoId).withs("datasourceDriverName", "datasourceTypeName", "pluginName").lazys("datasourceNodeList", "datasourceFieldData").one();
        if (datasourceInfo == null) {
            throw new AppErrorException("数据源不存在");
        }
        DatasourceDriver datasourceDriver = (DatasourceDriver) baseServiceImpl.getService(DatasourceDriver.class).getQuery().eq("id", datasourceInfo.getDatasourceDriverId()).one();
        // 如果是手动新增的，需要把驱动版本设置为引用的模版的驱动版本信息
        if (!datasourceDriver.getIsDefault()) {
            DatasourceDriver datasourceDriverTemplate = (DatasourceDriver) baseServiceImpl.getService(DatasourceDriver.class).getQuery().eq("id", datasourceDriver.getDatasourceDriverId()).one();
            datasourceInfo.setDbVersion(datasourceDriverTemplate.getDriverVersion());
        }
        // DatasourceType表没有维护数据库版本的code值
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceInfo.getDatasourceTypeName(), datasourceInfo.getDbVersion());
        datasourceInfo.setDataTypeCode(typeEnum.getVal());
        result = getDatasourceDTO(datasourceInfo, datasourceInfo.getDatasourceNodeList());
        return result;
    }

    public static DatasourceDTO getDatasourceDTO(DatasourceInfo datasourceInfo, List<DatasourceNode> datasourceNodeList) {
        DatasourceDTO result = null;
        DatasourceNode datasourceNode = obtainEffectiveNode(datasourceInfo.getDatasourceNodeSerialNumber(), CollUtil.isEmpty(datasourceNodeList) ? datasourceInfo.getDatasourceNodeList() : datasourceNodeList);
        JSONObject dataJson = processDataJson(datasourceInfo, datasourceNode);
        result = DatasourceDTO.builder()
                .dataJsonMap(dataJson)
                .dataJson(dataJson.toJSONString())
                .dataType(datasourceInfo.getDatasourceTypeName())
                .dataTypeCode(datasourceInfo.getDataTypeCode())
                .pluginName(datasourceInfo.getPluginName())
                .dataName(datasourceInfo.getDatasourceName())
                .dbName(datasourceNode.getDbName())
                .schema(datasourceNode.getSchema())
                .datasourceId(datasourceInfo.getId())
                .dataVersion(datasourceInfo.getDbVersion())
                .manager(datasourceInfo.getPersonCharge())
                .build();
        return result;
    }

    /**
     * 构建kerberos
     * @param dataJson 数据源信息
     * @param result 数据源信息构建物
     */
    private void buildKerberos(JSONObject dataJson, DatasourceDTO result) {
        if (dataJson.getString(DatasourceInfo.DataJSONKeys.OPEN_KERBEROS) != null && DatasourceInfo.DataJSONKeys.OPEN_KERBEROS_TRUE.equals(dataJson.getString(DatasourceInfo.DataJSONKeys.OPEN_KERBEROS))) {

        }
    }

    /**
     * 替换数据
     * @param datasourceDTO 数据源信息
     * @param replaceDatasourceDTO 替换数据
     * @return 处理后的数据
     */
    public static DatasourceDTO getDatasourceDTO(DatasourceDTO datasourceDTO, ReplaceDatasourceDTO replaceDatasourceDTO) {
        DatasourceDTO result = datasourceDTO;
        if (datasourceDTO == null || replaceDatasourceDTO == null) {
            return result;
        }
        JSONObject dataJsonMap = result.getDataJsonMap();
        if (StringUtils.isNotEmpty(replaceDatasourceDTO.getDbName())) {
            dataJsonMap.put(DatasourceInfo.DataJSONKeys.JDBC_URL, JdbcUrlBuilderUtil.replaceDatabaseInJdbcUrl(dataJsonMap.getString(DatasourceInfo.DataJSONKeys.JDBC_URL), replaceDatasourceDTO.getDbName()));
            result.setDbName(replaceDatasourceDTO.getDbName());
        }
        if (StringUtils.isNotEmpty(replaceDatasourceDTO.getSchemaName())) {
            dataJsonMap.put(DatasourceInfo.DataJSONKeys.SCHEMA, replaceDatasourceDTO.getSchemaName());
            result.setSchema(replaceDatasourceDTO.getSchemaName());
        }
        result.setDataJson(dataJsonMap.toJSONString());
        return result;
    }

    /**
     * 替换数据源信息-数据库
     * @param datasourceDTO 数据源信息
     * @param dbName 数据库
     * @return 处理后的数据
     */
    public static DatasourceDTO replaceDatasourceDTODbName(DatasourceDTO datasourceDTO, String dbName) {
        ReplaceDatasourceDTO replaceDatasourceDTO = ReplaceDatasourceDTO.builder().dbName(dbName).build();
        return getDatasourceDTO(datasourceDTO, replaceDatasourceDTO);
    }

    /**
     * 替换数据源信息-数据库
     * @param datasourceDTO 数据源信息
     * @param dbName 数据库
     * @param schema 模式
     * @return 处理后的数据
     */
    public static DatasourceDTO replaceDatasourceDTODbName(DatasourceDTO datasourceDTO, String dbName, String schema) {
        ReplaceDatasourceDTO replaceDatasourceDTO = ReplaceDatasourceDTO.builder().dbName(dbName).schemaName(schema).build();
        return getDatasourceDTO(datasourceDTO, replaceDatasourceDTO);
    }

    /**
     * 获取生效节点
     * @param datasourceNodeSerialNumber 生效节点唯一值
     * @param datasourceNodeList 节点列表
     * @return 生效节点
     */
    public static DatasourceNode obtainEffectiveNode(String datasourceNodeSerialNumber, List<DatasourceNode> datasourceNodeList) {
        verifyEffectiveNode(datasourceNodeSerialNumber, datasourceNodeList);
        for (DatasourceNode datasourceNode : datasourceNodeList) {
            if (datasourceNodeSerialNumber.equals(datasourceNode.getSerialNumber())) {
                datasourceNode.openGetJdbcUrl();
                return datasourceNode;
            }
        }
        throw new AppErrorException("数据源生效节点未查询到");
    }

    private static void verifyEffectiveNode(String datasourceNodeSerialNumber, List<DatasourceNode> datasourceNodeList) {
        if (StringUtils.isEmpty(datasourceNodeSerialNumber)) {
            throw new AppErrorException("数据源生效节点不存在");
        }
        if (CollUtil.isEmpty(datasourceNodeList)) {
            throw new AppErrorException("数据源节点不存在");
        }
    }

    /**
     * TODO 开启kerberos认证处理文件等信息待完善
     * 处理数据源信息
     * @param datasourceInfo 数据源信息
     * @param datasourceNode 数据源节点信息
     * @return 处理后的数据
     */
    private static JSONObject processDataJson(DatasourceInfo datasourceInfo, DatasourceNode datasourceNode) {
        JSONObject result = new JSONObject();
        for (DatasourceFieldData datasourceFieldData : datasourceNode.getDatasourceFieldData()) {
            result.put(datasourceFieldData.getLabel(), datasourceFieldData.getValue());
        }
        result.put(DatasourceInfo.DataJSONKeys.DATA_NAME, datasourceInfo.getDatasourceName());
        result.put(DatasourceInfo.DataJSONKeys.BUSINESS_UUID, datasourceInfo.getBusinessSystemId());
        result.put(DatasourceInfo.DataJSONKeys.DATA_TYPE, datasourceInfo.getDatasourceTypeName());
        result.put(DatasourceInfo.DataJSONKeys.DATA_TYPE_CODE, datasourceInfo.getDataTypeCode());
        result.put(DatasourceInfo.DataJSONKeys.DRIVER_CLASS_NAME, datasourceInfo.getDatasourceDriverName());
        result.put(DatasourceInfo.DataJSONKeys.DATA_VERSION, datasourceInfo.getDbVersion());
        result.put(DatasourceInfo.DataJSONKeys.JDBC_URL, datasourceNode.getJdbcUrl());
        return result;
    }
}
