package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.vo.ProjectVO;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.interfaces.IService;
import com.joyadata.model.BaseBean;
import com.joyadata.service.BaseServiceImpl;
import com.joyadata.tms.model.Project;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName ProjectService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/12 14:23
 * @Version 1.0
 **/
@Service
public class ProjectService {
    private JoyaFeignService<Project> projectJoyaFeignService = FeignFactory.make(Project.class);
    @Autowired
    private BaseServiceImpl baseServiceImpl;

    public Integer getProjectTotal(String keywords, String datasourceInfoId, Boolean checked) {
        String projectIds = getProjectIds(datasourceInfoId);
        IQueryWrapper<Project> projectIQueryWrapper = buildProjectQueryWrapper(keywords, projectIds, checked);
        return projectIQueryWrapper.total();
    }

    public List<ProjectVO> getProjects(String keywords, String datasourceInfoId, Boolean checked, Integer page, Integer pager) {
        String projectIds = getProjectIds(datasourceInfoId);
        IQueryWrapper<Project> projectIQueryWrapper = buildProjectQueryWrapper(keywords, projectIds, checked);
        if (null != page && null != pager) {
            projectIQueryWrapper.page(page, pager);
        }

        List<Project> projects = projectIQueryWrapper.list();
        return convertToProjectVOList(projects, projectIds);
    }

    private IQueryWrapper<Project> buildProjectQueryWrapper(String keywords, String projectIds, Boolean checked) {
        IQueryWrapper<Project> projectIQueryWrapper = projectJoyaFeignService.getQuery()
                .sortbyDesc("createTime");

        if (null != checked) {
            if (checked) {
                queryParam(projectIQueryWrapper, "id", projectIds, "IN");
            } else {
                queryParam(projectIQueryWrapper, "id", projectIds, "NOTIN");
            }
        }
        queryParam(projectIQueryWrapper, "name", keywords, "SEARCHBY");
        return projectIQueryWrapper;
    }

    private String getProjectIds(String datasourceInfoId) {
        String projectIds = "未知";
        IService<DatasourceAlert> datasourceAlertIService = baseServiceImpl.getService(DatasourceAlert.class);
        DatasourceAlert datasourceAlert = datasourceAlertIService.getQuery()
                .eq("datasourceInfoId", datasourceInfoId)
                .one();

        if (null != datasourceAlert) {
            projectIds = Optional.ofNullable(datasourceAlert.getProjectIds()).orElse("未知");
        }
        return projectIds;
    }

    private List<ProjectVO> convertToProjectVOList(List<Project> projects, String projectIds) {
        List<ProjectVO> projectVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(projects)) {
            List<String> projectIdList = StrUtil.split(projectIds, ",");
            for (Project project : projects) {
                ProjectVO projectVO = new ProjectVO();
                BeanUtils.copyProperties(project, projectVO);
                projectVO.setChecked(projectIdList.contains(project.getId()));
                projectVOS.add(projectVO);
            }
        }
        return projectVOS;
    }

    private void queryParam(IQueryWrapper<? extends BaseBean> query, String paramName, String paramValue, String symbol) {
        if ("EQ".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.eq(paramName, paramValue);
            }
        } else if ("SEARCHBY".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.searchby(paramName, paramValue);
            }
        } else if ("SETIN".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.setin(paramName, paramValue);
            }
        } else if ("LIKE".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.like(paramName, paramValue);
            }
        } else if ("IN".equals(symbol)) {
            List<String> ids = null;
            if (StrUtil.isNotBlank(paramValue)) {
                ids = StrUtil.split(paramValue, ",");
                query.in(paramName, ids);
            }
        } else if ("NOTIN".equals(symbol)) {
            List<String> ids = null;
            if (StrUtil.isNotBlank(paramValue)) {
                ids = StrUtil.split(paramValue, ",");
                query.notIn(paramName, ids);
            }
        }
    }
}
