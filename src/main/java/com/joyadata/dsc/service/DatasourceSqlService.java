package com.joyadata.dsc.service;


import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.statement.SQLDDLStatement;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.google.common.collect.Lists;
import com.joyadata.dsc.constants.Constants;
import com.joyadata.dsc.enums.DatasourceTypeDatabase;
import com.joyadata.dsc.enums.DbTypeEnum;
import com.joyadata.dsc.enums.MetadataCollectionStatusEnum;
import com.joyadata.dsc.model.datasoure.DatasourceSql;
import com.joyadata.dsc.model.datasoure.DatasourceSqlExec;
import com.joyadata.dsc.model.datasoure.dto.DatasourceSqlDTO;
import com.joyadata.dsc.utils.DatasourceUtil;
import com.joyadata.exception.AppWarningException;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DatasourceSqlService extends BaseService<DatasourceSql> {
    @Autowired
    DatasourceUtil datasourceUtil;

    /**
     * 重写删除方法
     */
    @Override
    public Integer delete(String id, DatasourceSql bean) {
        Integer delete = super.delete(id);
        if(delete>0){
            //删除执行记录
            EqCondition deleteTask = new EqCondition("sqlId",id);
            Integer integer = getService(DatasourceSqlExec.class).deleteBy(deleteTask);
        }
        return delete;
    }

    /**
     * 预览数据
     */
    public List<Map<String, Object>>  preview(DatasourceSqlDTO datasourceSqlDTO) {
        //获取数据源信息
        DatasourceDTO datasourceDTO = datasourceUtil.getDatasourceDTO(datasourceSqlDTO.getDatasourceInfoId());
        if(datasourceDTO==null){
            throw new AppWarningException("数据源不存在"+datasourceSqlDTO.getDatasourceInfoId());
        }
//        //替换jdbcurl 方法
        datasourceDTO = DatasourceUtil.replaceDatasourceDTODbName(datasourceDTO, datasourceSqlDTO.getDbName(),datasourceSqlDTO.getSchemaName());
        //判断是否执行执行计划
        String sql = datasourceSqlDTO.getSql();
        if(datasourceSqlDTO.getIsExecutionPlan()){
            sql=String.format(Constants.EXECUTION_PLAN_SQL_TEMPLATE,datasourceSqlDTO.getSql());
        }
        List<Map<String, Object>> result = Lists.newArrayList();
        //新建执行记录对象
        DatasourceSqlExec datasourceSqlExec =new DatasourceSqlExec();
        datasourceSqlExec.setSqlId(datasourceSqlDTO.getSqlId());
        datasourceSqlExec.setSql(sql);
        datasourceSqlExec.setDatasourceInfoId(datasourceSqlDTO.getDatasourceInfoId());
        datasourceSqlExec.setDbName(datasourceSqlDTO.getDbName());
        datasourceSqlExec.setSchemaName(datasourceSqlDTO.getSchemaName());
        //开始执行时间
        long l = System.currentTimeMillis();
        //执行状态 默认成功
        int status = MetadataCollectionStatusEnum.SUCCESS.getCode();
        //捕获异常执行
        try {
            //sql 去掉换行符 去掉 前后空格
            datasourceDTO.setSql(sql);
            //分页
            datasourceDTO.setPreviewNum(datasourceSqlDTO.getPreviewNum());
            log.info("执行sql:{}",sql);
            log.info("开始执行datasourceDTO:{}",datasourceDTO);
            result = DatasourceUtils.executeQuery(datasourceDTO);
        }catch (Exception e){
            status = MetadataCollectionStatusEnum.FAILURE.getCode();
            datasourceSqlExec.setSqlExecLog(e.getMessage());
        }
        datasourceSqlExec.setSqlStatus(status);
        //结束时间
        long l1 = System.currentTimeMillis();
        //计算时长并转为ms
        datasourceSqlExec.setSqlExecTime(String.valueOf((l1-l)/1000));
        //非执行计划保存
        if(!datasourceSqlDTO.getIsExecutionPlan()){
            //保存执行记录
            getService(DatasourceSqlExec.class).add(datasourceSqlExec);
        }

        return result;
    }


    /**
     * 校验sql 是不是ddl
     */
    public  Boolean parseSqlDDL(String sql, String dbType)  {
        if ("clickhouse".equalsIgnoreCase(dbType)) {
            if (sql.startsWith("optimize")) {
                return true;
            }
        }
        try{
            List<SQLStatement> stmts = SQLUtils.parseStatements(sql, DbTypeEnum.getDbTypeByName(dbType));
            for (SQLStatement stmt : stmts) {
                Boolean result = stmt instanceof SQLDDLStatement;
                if (result) {
                    return true;
                }
            }
        }catch (Exception e){
            return true;
        }
        return false;
    }



}