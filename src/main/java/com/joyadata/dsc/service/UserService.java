package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.joyadata.cms.model.User;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.vo.UserVO;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.interfaces.IService;
import com.joyadata.model.BaseBean;
import com.joyadata.service.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName UserService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/12 10:40
 * @Version 1.0
 **/
@Service
public class UserService {
    private JoyaFeignService<User> userJoyaFeignService = FeignFactory.make(User.class);
    @Autowired
    private BaseServiceImpl baseServiceImpl;

    public Integer getUserTotal(String deptId, String keywords, String datasourceInfoId, Boolean checked) {
        String userIds = getUserIds(datasourceInfoId);
        IQueryWrapper<User> userIQueryWrapper = buildUserQueryWrapper(deptId, keywords, userIds, checked);
        return userIQueryWrapper.total();
    }

    public List<UserVO> getUsers(String deptId, String keywords, String datasourceInfoId, Boolean checked, Integer page, Integer pager) {
        String userIds = getUserIds(datasourceInfoId);
        IQueryWrapper<User> userIQueryWrapper = buildUserQueryWrapper(deptId, keywords, userIds, checked);
        //userIQueryWrapper.withs("deptId", "deptName", "fullDeptName", "fullDeptIds");
        if (null != page && null != pager) {
            userIQueryWrapper.page(page, pager);
        }

        List<User> users = userIQueryWrapper.list();

        return convertToUserVOList(users, userIds);
    }

    private IQueryWrapper<User> buildUserQueryWrapper(String deptId, String keywords, String userIds, Boolean checked) {
        IQueryWrapper<User> userIQueryWrapper = userJoyaFeignService.getQuery()
                .withs("deptId", "deptName", "fullDeptName", "fullDeptIds")
                //.setin("fullDeptIds", deptId)
                //.searchby("username,nickname", keywords)
                //.in("id", userIds)
                ;
        if (null != checked) {
            if (checked) {
                queryParam(userIQueryWrapper, "id", userIds, "IN");
            } else {
                queryParam(userIQueryWrapper, "id", userIds, "NOTIN");
            }
        }
        queryParam(userIQueryWrapper, "fullDeptIds", deptId, "SETIN");
        queryParam(userIQueryWrapper, "username,nickname", keywords, "SEARCHBY");
        return userIQueryWrapper;
    }

    private String getUserIds(String datasourceInfoId) {
        String userIds = "未知";
        IService<DatasourceAlert> datasourceAlertIService = baseServiceImpl.getService(DatasourceAlert.class);
        DatasourceAlert datasourceAlert = datasourceAlertIService.getQuery()
                .eq("datasourceInfoId", datasourceInfoId)
                .one();
        if (null != datasourceAlert) {
            userIds = Optional.ofNullable(datasourceAlert.getUserIds()).orElse("未知");
        }
        return userIds;
    }

    private List<UserVO> convertToUserVOList(List<User> users, String userIds) {
        List<UserVO> userVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(users)) {
            List<String> userIdList = StrUtil.split(userIds, ",");
            for (User user : users) {
                UserVO userVO = new UserVO();
                BeanUtils.copyProperties(user, userVO);
                if (userIdList.contains(user.getId())) {
                    userVO.setChecked(true);
                } else {
                    userVO.setChecked(false);
                }
                userVOS.add(userVO);
            }
        }
        return userVOS;
    }

    private void queryParam(IQueryWrapper<? extends BaseBean> query, String paramName, String paramValue, String symbol) {
        if ("EQ".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.eq(paramName, paramValue);
            }
        } else if ("SEARCHBY".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.searchby(paramName, paramValue);
            }
        } else if ("SETIN".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.setin(paramName, paramValue);
            }
        } else if ("LIKE".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.like(paramName, paramValue);
            }
        } else if ("IN".equals(symbol)) {
            List<String> ids = null;
            if (StrUtil.isNotBlank(paramValue)) {
                ids = StrUtil.split(paramValue, ",");
                query.in(paramName, ids);
            }
        } else if ("NOTIN".equals(symbol)) {
            List<String> ids = null;
            if (StrUtil.isNotBlank(paramValue)) {
                ids = StrUtil.split(paramValue, ",");
                query.notIn(paramName, ids);
            }
        }
    }

    //public List<Dept> getDeptTree(String deptId, String keywords, String datasourceInfoId, Boolean checked) {
    //    List<UserVO> users = getUsers();
    //    Set<String> deptIds = users.stream().flatMap(user -> StrUtil.split(user.getFullDeptIds(), ",").stream())
    //            .collect(Collectors.toSet());
    //    List<Dept> depts = deptJoyaFeignService.getQuery().in("id", deptIds).list();
    //}
}
