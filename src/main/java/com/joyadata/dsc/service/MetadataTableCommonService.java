package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.joyadata.dsc.enums.MetadataTaskObjectEnum;
import com.joyadata.dsc.model.metadata.MetadataColumnCommon;
import com.joyadata.dsc.model.metadata.MetadataTableCommon;
import com.joyadata.exception.AppErrorException;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: sh
 */
@Service
@Slf4j
public class MetadataTableCommonService extends BaseService<MetadataTableCommon> {


    /**
     * 批量修改
     * @param metadataTableCommons
     * @return
     */
    public void batchUpdate(List<MetadataTableCommon> metadataTableCommons)   {

        metadataTableCommons.forEach(table -> {
            table.setDelFlag(true);
            getService(MetadataTableCommon.class).update(table);
        });
    }

    public List<MetadataTableCommon> getTablesByDatasourceinfoId(String datasourceinfoId,boolean delFlag)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        List<MetadataTableCommon> metadataTables = getService(MetadataTableCommon.class)
                .getQuery()
                .eq("datasourceInfoId", datasourceinfoId)
                .eq("delFlag",  delFlag)
                .list();
        return metadataTables;
    }

    public List<MetadataTableCommon> getTableByDatasourceinfoId(MetadataTableCommon table,Integer page,Integer pager,List<String> tableNames)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        IQueryWrapper<MetadataTableCommon> query = getService(MetadataTableCommon.class).getQuery();
        query.eq("datasourceInfoId", table.getDatasourceInfoId());
        if(StringUtils.isNotEmpty(table.getDbName())){
            query.eq("dbName", table.getDbName());
        }
        if(StringUtils.isNotEmpty(table.getSchemaName())){
            query.eq("schemaName", table.getSchemaName());
        }
        if(StringUtils.isNotEmpty(table.getName())){
            query.like("name", table.getName());
        }
        if(StringUtils.isNotEmpty(table.getType())){
            query.eq("type", table.getType());
        }
        if(CollUtil.isNotEmpty(tableNames)){
            query.in("name",tableNames);
        }
        List<MetadataTableCommon> dbNames = query.page(page,pager).sortbyAsc("name").list();
        if(Objects.isNull(dbNames)){
            return new ArrayList<>();
        }
        return dbNames;
    }

    public Integer getTableTotalByDatasourceinfoId(MetadataTableCommon table,List<String> tableNames)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        IQueryWrapper<MetadataTableCommon> query = getService(MetadataTableCommon.class).getQuery();
        query.eq("datasourceInfoId", table.getDatasourceInfoId());
        if(StringUtils.isNotEmpty(table.getDbName())){
            query.eq("dbName", table.getDbName());
        }
        if(StringUtils.isNotEmpty(table.getSchemaName())){
            query.eq("schemaName", table.getSchemaName());
        }
        if(StringUtils.isNotEmpty(table.getName())){
            query.like("name", table.getName());
        }
        if(StringUtils.isNotEmpty(table.getType())){
            query.eq("type", table.getType());
        }
        if(CollUtil.isNotEmpty(tableNames)){
            query.in("name",tableNames);
        }
        List<MetadataTableCommon> dbNames = query.list();
        if(Objects.isNull(dbNames)){
            return 0;
        }
        return dbNames.size();
    }


    public List<MetadataTableCommon> getDbNameByDatasourceinfoId(String datasourceId,String dbName)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        IQueryWrapper<MetadataTableCommon> query = getService(MetadataTableCommon.class).getQuery();
        query.eq("datasourceInfoId", datasourceId)
                .groupBy("dbName");
        if(StringUtils.isNotEmpty(dbName)){
            query.like("dbName", dbName);
        }
        List<MetadataTableCommon> dbNames = query.sortbyAsc("dbName").list();
        //dbNames 不为null或者空时，去重
        if (dbNames!=null&&dbNames.size()>0){
            //去重
            dbNames =  dbNames.stream()
                    .collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MetadataTableCommon::getDbName))))
                    .stream()
                    .collect(Collectors.toList());
        }
        if(Objects.isNull(dbNames)){
            return new ArrayList<>();
        }
        return dbNames;
    }
    //根据数据源id，查询其下所有表得schema分组，并去重
    public List<MetadataTableCommon> getSchemaNameByDatasourceinfoId(String datasourceId,String dbName) {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称

        IQueryWrapper<MetadataTableCommon> query = getService(MetadataTableCommon.class).getQuery();
        query.eq("datasourceInfoId", datasourceId)
                .groupBy("schemaName");
        if(StringUtils.isNotEmpty(dbName)){
            query.like("schemaName", dbName);
        }
        List<MetadataTableCommon> schemaNames = query.sortbyAsc("schemaName").list();
        //schemaNames 不为null或者空时，去重
        if (schemaNames!=null&&schemaNames.size()>0){
            //去重
            schemaNames =  schemaNames.stream()
                    .collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MetadataTableCommon::getSchemaName))))
                    .stream()
                    .collect(Collectors.toList());
        }
        if(Objects.isNull(schemaNames)){
            return new ArrayList<>();
        }
        return schemaNames;
    }


    //快速选表使用
    public List<String> getTablesExist(MetadataTableCommon table,List<String> tableNames)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        IQueryWrapper<MetadataTableCommon> query = getService(MetadataTableCommon.class).getQuery();
        query.eq("datasourceInfoId", table.getDatasourceInfoId());
        if(StringUtils.isNotEmpty(table.getDbName())){
            query.eq("dbName", table.getDbName());
        }
        if(StringUtils.isNotEmpty(table.getSchemaName())){
            query.eq("schemaName", table.getSchemaName());
        }
        if(StringUtils.isNotEmpty(table.getName())){
            query.like("name", table.getName());
        }
        if(StringUtils.isNotEmpty(table.getType())){
            query.eq("type", table.getType());
        }
        if(CollUtil.isNotEmpty(tableNames)){
            query.in("name",tableNames);
        }
        List<String> tableName = query.listValue("name",String.class);
        if(Objects.isNull(tableName)){
            return new ArrayList<>();
        }
        return tableName;
    }

    /**
     * 根据tableID获取索引信息
     */
    public List<MetadataTableCommon> getIndexByTableId(String tableId,Integer page, Integer pager) {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        IQueryWrapper<MetadataTableCommon> query = getService(MetadataTableCommon.class).getQuery();
        query.eq("indexTableId", tableId);
        query.eq("type", MetadataTaskObjectEnum.INDEX.getType());
        query.eq("delFlag", Boolean.FALSE);
        if(page != null && pager != null){
            query.page(page, pager);
        }
        List<MetadataTableCommon> indexs = query.list();
        //获取表得字段
        if (CollUtil.isNotEmpty(indexs)) {
            //indexs 提取id  生成ids
            List<String> ids = indexs.stream().map(MetadataTableCommon::getId).collect(Collectors.toList());
            List<MetadataColumnCommon> col = getService(MetadataColumnCommon.class).getQuery()
                    .in("metadataTableCommonId", ids)
                    .eq("delFlag", Boolean.FALSE)
                    .list();
            //若不为空 则按 metadataTableCommonId 分组 metadataTableCommonId 为key
            if (CollUtil.isNotEmpty(col)) {
                Map<String, List<MetadataColumnCommon>> map = col.stream().collect(Collectors.groupingBy(MetadataColumnCommon::getMetadataTableCommonId));
                //遍历indexs
                for (MetadataTableCommon index : indexs) {
                    List<MetadataColumnCommon> metadataColumnCommons = map.get(index.getId());
                    if (CollUtil.isNotEmpty(metadataColumnCommons)) {
                       //将metadataColumnCommons得字段名拼接为字符串
                        String metadataColu = metadataColumnCommons.stream().map(MetadataColumnCommon::getColumnName).collect(Collectors.joining(","));
                        index.setIndexColNames(metadataColu);
                    }
                }
            }
        }
        return indexs;
    }

    /**
     * 根据MetadataTableCommon的id生成查询语句
     * @param metadataTableCommonId 元数据表ID
     * @return 生成的查询语句
     */
    public String generateQuerySql(String metadataTableCommonId) {
        // 1. 获取元数据表信息
        MetadataTableCommon tableCommon = getById(metadataTableCommonId);
        if (tableCommon == null) {
            throw new AppErrorException("未找到对应的元数据表信息");
        }

        // 2. 判断类型是否为表或视图
        if (!MetadataTaskObjectEnum.TABLE.getType().equals(tableCommon.getType()) &&
                !MetadataTaskObjectEnum.VIEW.getType().equals(tableCommon.getType())) {
            throw new AppErrorException("只支持表或视图类型生成查询语句");
        }

        // 3. 获取表的所有列信息
        List<MetadataColumnCommon> columns = getService(MetadataColumnCommon.class)
                .getQuery()
                .eq("metadataTableCommonId", metadataTableCommonId)
                .list();

        if (columns.isEmpty()) {
            throw new AppErrorException("未找到表的列信息");
        }

        // 4. 拼接查询语句
        StringBuilder sqlBuilder = new StringBuilder("SELECT ");

        // 拼接列名
        for (int i = 0; i < columns.size(); i++) {
            MetadataColumnCommon column = columns.get(i);
            sqlBuilder.append(column.getColumnName());

            if (i < columns.size() - 1) {
                sqlBuilder.append(", ");
            }
        }

        // 拼接表名
        sqlBuilder.append(" FROM ");

        // 如果有schema，添加schema
        if (StringUtils.isNotEmpty(tableCommon.getSchemaName())) {
            sqlBuilder.append(tableCommon.getSchemaName())
                    .append(tableCommon.getName());
        } else {
            sqlBuilder.append(tableCommon.getName());
        }

        return sqlBuilder.toString();
    }

}
