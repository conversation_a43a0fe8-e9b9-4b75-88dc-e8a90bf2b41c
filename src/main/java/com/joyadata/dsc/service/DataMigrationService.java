package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.cms.model.Role;
import com.joyadata.cms.model.User;
import com.joyadata.dsc.enums.PublicConstants;
import com.joyadata.dsc.enums.SwitchStatus;
import com.joyadata.dsc.feign.CmsFeignService;
import com.joyadata.dsc.model.business.BusinessSystem;
import com.joyadata.dsc.model.datasoure.*;
import com.joyadata.dsc.model.datasoure.dto.DatasourceInfoSaveDTO;
import com.joyadata.dsc.model.metadata.MetadataTask;
import com.joyadata.dsc.model.migration.DataMigration;
import com.joyadata.dsc.model.migration.DataMigrationDetail;
import com.joyadata.dsc.model.migration.dto.*;
import com.joyadata.dsc.model.migration.vo.BusinessSystemImportResult;
import com.joyadata.dsc.model.migration.vo.DatasourceImportResult;
import com.joyadata.dsc.model.migration.vo.ExportDataResultVO;
import com.joyadata.dsc.utils.IdUtils;
import com.joyadata.dsc.utils.excel.*;
import com.joyadata.dsc.utils.jdbc.JdbcUrlBuilderUtil;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.file.model.JoyadataAppFile;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.InCondition;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.service.BaseService;
import com.joyadata.tms.model.Product;
import com.joyadata.tms.model.Project;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
@Slf4j
@Service
public class DataMigrationService extends BaseService<DataMigration> {

    @Autowired
    private BusinessSystemService businessSystemService;

    @Autowired
    private DatasourceInfoService datasourceInfoService;

    @Autowired
    private MetadataTaskService metadataTaskService;
    private final JoyaFeignService<User> userJoyaFeignService = FeignFactory.make(User.class);

    @Autowired

    private DatasourceAlertService datasourceAlertService;

    @Autowired
    private CmsFeignService cmsFeignService;

    /**
     * 上传下载文件工具
     */
    private JoyaFeignService<JoyadataAppFile> joyadataAppFile = FeignFactory.make(JoyadataAppFile.class);
    /**
     * 下载导出模板
     */
    public void downloadTemplate(HttpServletResponse response) {


        try {
            // 设置响应头
            // 初始化HTTP响应，确保文件下载时的响应头正确设置
            response.reset();
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/vnd.ms-excel");

            // 创建 EasyExcel 写入器
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .autoCloseStream(Boolean.FALSE)
                    .build();

            // 写入 Sheet 1：业务系统
            WriteSheet businessSheet = EasyExcel.writerSheet("业务系统")
                    .head(BusinessSystemTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            excelWriter.write(Collections.emptyList(), businessSheet);

            // 写入 Sheet 2：数据源登记
            WriteSheet datasourceSheet = EasyExcel.writerSheet("数据源登记")
                    .head(DatasourceInfoTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            excelWriter.write(Collections.emptyList(), datasourceSheet);


            // Sheet3：采集配置
            WriteSheet mataDataSheet = EasyExcel.writerSheet("采集配置")
                    .head(MataDataTaskTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            excelWriter.write(Collections.emptyList(), mataDataSheet);

            // Sheet4：采集配置
            WriteSheet mataDataSheetJob = EasyExcel.writerSheet("采集定时配置")
                    .head(MataDataTaskJobTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            excelWriter.write(Collections.emptyList(), mataDataSheetJob);

            // Sheet5：数据源告警
            WriteSheet datasourceAlertSheet = EasyExcel.writerSheet("数据源告警")
                    .head(DatasourceAlertTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            excelWriter.write(Collections.emptyList(), datasourceAlertSheet);

            excelWriter.finish();
        } catch (Exception e) {
            log.error("数据迁移模板下载失败：{}", e.getMessage());
        }
    }

    /**
     * 导出所选数据源相关信息
     */
    public void exportDatasourceInfo(DataMigrationDTO dataMigrationDTO, HttpServletResponse response) {
        //创建一个数据源迁移对象
        DataMigration dataMigration = new DataMigration();
        dataMigration.setName(dataMigrationDTO.getDataMigrationName());
        dataMigration.setType("export");
        dataMigration.setStartTime(new Date());
        //设置id
        String dataMigrationId = IdUtils.simpleUUID();
        dataMigration.setId(dataMigrationId);
        if(StringUtils.isNotEmpty(dataMigrationDTO.getId())){
            dataMigration.setId(dataMigrationDTO.getId());
        }
        //迁移任务状态
        dataMigration.setStatus(SwitchStatus.ON.getCode());
        ExecutorService executorService = Executors.newFixedThreadPool(5);

        try {
            //获取用户
            Map<String, User> userMap = businessSystemService.getUserMap(false);

            List<Callable<ExportDataResultVO>> tasks = new ArrayList<>();
            List<String> datasourceIds = dataMigrationDTO.getDatasourceIds();
            for (String datasourceId : datasourceIds) {
                Callable<ExportDataResultVO> task = () -> {
                    ExportDataResultVO result = new ExportDataResultVO();
                    int status = SwitchStatus.OFF.getCode();
                    DataMigrationDetail dataMigrationDetail = new DataMigrationDetail();
                    //数据源模板结合
                    List<DatasourceInfoTemplate> datasourceInfoTemplates = new ArrayList<>();
                    try {
                        //获取数据源基本信息
                        DatasourceInfo datasourceInfo = datasourceInfoService.getDatasourceForDataMigration(datasourceId);
                        //获取数据源业务系统
                        String businessSystemId = datasourceInfo.getBusinessSystemId();
                        //获取业务系统
                        BusinessSystemTemplate businessSystemExportTemplate = businessSystemService.getBusinessSystemExportTemplate(businessSystemId, datasourceInfo.getBusinessName());
                        result.setSystemTemplate(businessSystemExportTemplate);
                        //获取数据源节点信息
                        List<DatasourceNode> datasourceNodeList = datasourceInfo.getDatasourceNodeList();
                        //生效节点
                        String datasourceNodeSerialNumber = datasourceInfo.getDatasourceNodeSerialNumber();
                        DatasourceNode effectiveNode = datasourceNodeList.stream()
                                .filter(node -> StringUtils.equals(node.getSerialNumber(), datasourceNodeSerialNumber)
                                        && StringUtils.equals(node.getDatasourceInfoId(), datasourceId))
                                .findFirst()
                                .orElse(null);
                        //节点名称
                        String datasourceNodeName = effectiveNode.getNodeName();
                        if (CollUtil.isNotEmpty(datasourceNodeList)) {
                            for (DatasourceNode datasourceNode : datasourceNodeList) {
                                DatasourceInfoTemplate datasourceInfoTemplate = new DatasourceInfoTemplate();
                                //业务系统
                                datasourceInfoTemplate.setBusinessName(datasourceInfo.getBusinessName());
                                //数据源名称
                                datasourceInfoTemplate.setDatasourceName(datasourceInfo.getDatasourceName());
                                //数据源类型
                                datasourceInfoTemplate.setDatasourceTypeName(datasourceInfo.getDatasourceTypeName());
                                //驱动版本
                                datasourceInfoTemplate.setDriverVersion(datasourceInfo.getDbVersion());
                                // 获取节点属性值
                                Map<String, String> fieldDataMap = datasourceNode.getDatasourceFieldData().stream()
                                        .collect(Collectors.toMap(DatasourceFieldData::getLabel, DatasourceFieldData::getValue));
                                //库名
                                datasourceInfoTemplate.setDbName(fieldDataMap.get(DatasourceFormField.PropsKeys.DB_NAME));
                                //模式
                                datasourceInfoTemplate.setSchema(fieldDataMap.get(DatasourceFormField.PropsKeys.SCHEMA));
                                //用户名
                                datasourceInfoTemplate.setUsername(fieldDataMap.get(DatasourceFormField.PropsKeys.USER_NAME));
                                //密码
                                datasourceInfoTemplate.setPassword(fieldDataMap.get(DatasourceFormField.PropsKeys.PASSWORD));
                                //服务器ip
                                String hostPort = fieldDataMap.get(DatasourceFormField.PropsKeys.HOST_PORT);
                                //如果有多个ip，则用英文逗号隔开
                                if (StringUtils.isNotEmpty(hostPort)) {
                                    StringBuilder host = new StringBuilder();
                                    for (JdbcUrlBuilderUtil.HostPort hostPort1 : datasourceInfo.getHostPortList()) {
                                        host.append(hostPort1.getHost()).append(":");
                                        host.append(hostPort1.getPort()).append(",");
                                    }
                                    datasourceInfoTemplate.setHostPort(host.toString().substring(0, host.length() - 1));
                                }
                                //备注
                                datasourceInfoTemplate.setRemark(fieldDataMap.get(DatasourceFormField.PropsKeys.REMARK));
                                //生效节点
                                datasourceInfoTemplate.setEffectiveNode(datasourceNodeName);
                                //jdbcUrl
                                datasourceInfoTemplate.setJdbcUrl(fieldDataMap.get(DatasourceFormField.PropsKeys.JDBC));
                                //高级参数
                                datasourceInfoTemplate.setAdvancedConfig(fieldDataMap.get(DatasourceFormField.PropsKeys.ADVANCED));
                                //节点名称
                                datasourceInfoTemplate.setNodeName(datasourceNode.getNodeName());
                                //数据源id
                                datasourceInfoTemplate.setDatasourceId(datasourceId);
                                //todo 负责人
                                String personCharge = datasourceInfo.getPersonCharge();
                                if (userMap.containsKey(personCharge)) {
                                    User user = userMap.get(personCharge);
                                    datasourceInfoTemplate.setOwner(user.getNickname() + "(" + user.getUsername() + ")");
                                } else {
                                    datasourceInfoTemplate.setOwner(personCharge);
                                }
                                datasourceInfoTemplates.add(datasourceInfoTemplate);

                            }
                        }

                        //todo 数据源采集配置、授权、告警等
                        //获取采集配置
                        List<MataDataTaskTemplate> mataDataTaskTemplates = metadataTaskService.getDefaultTask(datasourceId,datasourceInfo.getDatasourceName());
                        result.setMataDataTaskTemplates(mataDataTaskTemplates);
                        //采集定时配置
                        MataDataTaskJobTemplate taskJob = metadataTaskService.getTaskJob(datasourceId, datasourceInfo.getDatasourceName());
                        result.setMataDataTaskJobTemplate(taskJob);
                        //数据源告警
                        DatasourceAlertTemplate datasourceAlertTemplate = datasourceAlertService.getDatasourceAlertEvents(datasourceId,datasourceInfo.getDatasourceName());
                        result.setDatasourceAlertTemplate(datasourceAlertTemplate);

                        status = SwitchStatus.ON.getCode();
                        result.setDatasourceInfoTemplates(datasourceInfoTemplates);
                        result.setStatus(status);
                        //数据源迁移详情
                        dataMigrationDetail.setDatasourceId(datasourceId);
                        dataMigrationDetail.setDatasourceName(datasourceInfo.getDatasourceName());
                        dataMigrationDetail.setBusinessName(businessSystemExportTemplate.getBusinessName());
                        dataMigrationDetail.setBusinessId(businessSystemId);
                        dataMigrationDetail.setStatus(status);
                    } catch (Exception e) {
                        dataMigrationDetail.setStatus(SwitchStatus.OFF.getCode());
                        dataMigrationDetail.setErrorMsg(e.getMessage());
                        log.error("数据源信息{}导出失败：{}", datasourceId, e.getMessage());
                    }
                    result.setStatus(status);
                    result.setDataMigrationDetail(dataMigrationDetail);
                    return result;
                };
                tasks.add(task);
            }
            // 提交任务列表，并获取Future列表
            List<Future<ExportDataResultVO>> futures = new ArrayList<>();
            futures = executorService.invokeAll(tasks);
            //创建业务系统模板集合
            List<BusinessSystemTemplate> systemTemplateList = new ArrayList<>();
            //数据源登记模板集合
            List<DatasourceInfoTemplate> datasourceInfoTemplateList = new ArrayList<>();
            //数据源迁移详情集合
            List<DataMigrationDetail> dataMigrationDetailList = new ArrayList<>();
            //采集配置集合
            List<MataDataTaskTemplate> mataDataTaskTemplateList = new ArrayList<>();
            //采集定时配置集合
            List<MataDataTaskJobTemplate> mataDataTaskJobTemplateList=new ArrayList<>();
            //数据源告警集合
            List<DatasourceAlertTemplate> datasourceAlertTemplateList=new ArrayList<>();

            //成功数
            int successCount = 0;
            int failCount = 0;
            // 处理任务执行结果
            for (Future<ExportDataResultVO> future : futures) {
                // 获取任务执行结果，get方法会阻塞直到任务完成
                ExportDataResultVO exportDataResultVO = future.get();
                if (Objects.nonNull(exportDataResultVO)) {
                    BusinessSystemTemplate systemTemplate = exportDataResultVO.getSystemTemplate();
                    if (Objects.nonNull(systemTemplate) && !systemTemplateList.contains(systemTemplate)) {
                        systemTemplateList.add(systemTemplate);
                    }
                    //数据源信息
                    List<DatasourceInfoTemplate> datasourceInfoTemplates = exportDataResultVO.getDatasourceInfoTemplates();
                    if (CollUtil.isNotEmpty(datasourceInfoTemplates)) {
                        datasourceInfoTemplateList.addAll(datasourceInfoTemplates);
                    }
                    //采集配置
                    List<MataDataTaskTemplate> mataDataTaskTemplates = exportDataResultVO.getMataDataTaskTemplates();
                    if (CollUtil.isNotEmpty(mataDataTaskTemplates)) {
                        mataDataTaskTemplateList.addAll(mataDataTaskTemplates);
                    }
                    //采集定时配置
                    MataDataTaskJobTemplate mataDataTaskJobTemplate = exportDataResultVO.getMataDataTaskJobTemplate();
                    if(Objects.nonNull(mataDataTaskJobTemplate)){
                        mataDataTaskJobTemplateList.add(mataDataTaskJobTemplate);
                    }
                    //数据源告警
                    DatasourceAlertTemplate datasourceAlertTemplate = exportDataResultVO.getDatasourceAlertTemplate();
                    if(Objects.nonNull(datasourceAlertTemplate)){
                        datasourceAlertTemplateList.add(datasourceAlertTemplate);
                    }
                    //状态
                    Integer status = exportDataResultVO.getStatus();
                    if (status == SwitchStatus.ON.getCode()) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                    //数据源迁移详情
                    DataMigrationDetail dataMigrationDetail = exportDataResultVO.getDataMigrationDetail();
                    if (Objects.nonNull(dataMigrationDetail) && !dataMigrationDetailList.contains(dataMigrationDetail)) {
                        dataMigrationDetail.setDataMigrationId(dataMigration.getId());
                        dataMigrationDetailList.add(dataMigrationDetail);
                    }
                }
            }
            //记录迁移详情
            if (CollUtil.isNotEmpty(dataMigrationDetailList)) {
                getService(DataMigrationDetail.class).add(dataMigrationDetailList);
            }

            // 设置响应头
            // 初始化HTTP响应，确保文件下载时的响应头正确设置
            response.reset();
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/vnd.ms-excel");

            ExcelWriter writer = EasyExcel.write(response.getOutputStream())
                    .autoCloseStream(Boolean.FALSE)
                    .build();

            // Sheet1：业务系统
            WriteSheet businessSheet = EasyExcel.writerSheet("业务系统")
                    .head(BusinessSystemTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            writer.write(systemTemplateList, businessSheet);

            // Sheet2：数据源登记
            WriteSheet datasourceSheet = EasyExcel.writerSheet("数据源登记")
                    .head(DatasourceInfoTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            writer.write(datasourceInfoTemplateList, datasourceSheet);


            // Sheet3：采集配置
            WriteSheet mataDataSheet = EasyExcel.writerSheet("采集配置")
                    .head(MataDataTaskTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            writer.write(mataDataTaskTemplateList, mataDataSheet);


            // Sheet4：采集配置
            WriteSheet mataDataSheetJob = EasyExcel.writerSheet("采集定时配置")
                    .head(MataDataTaskJobTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            writer.write(mataDataTaskJobTemplateList, mataDataSheetJob);
            // Sheet5：数据源告警
            WriteSheet datasourceAlertSheet = EasyExcel.writerSheet("数据源告警")
                    .head(DatasourceAlertTemplate.class)
                    .registerWriteHandler(new CustomHeaderStyleHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            writer.write(datasourceAlertTemplateList, datasourceAlertSheet);

            writer.close();

            dataMigration.setSuccessCount(successCount);
            dataMigration.setFailCount(failCount);

        } catch (Exception e) {
            dataMigration.setStatus(SwitchStatus.OFF.getCode());
            log.error("数据源迁移导出失败：{}", e.getMessage());
        } finally {
            // 关闭线程池
            executorService.shutdown();
            //保存数据源迁移信息
            dataMigration.setEndTime(new Date());
            getService(DataMigration.class).add(dataMigration);
        }

    }

    /**
     * 下载文件
     *
     */
    public void downloadFile(String fileId) {
       joyadataAppFile.dowload(fileId, PublicConstants.MODULE);
    }

    /**
     * 数据迁移：导入
     */
    public void importDatasourceInfo(DataMigration dataMigration, MultipartFile file) {
        //先上传文件
        //文件路径
        String filePath = "";
        try{
            JoyadataAppFile dsc = joyadataAppFile.upload(file, PublicConstants.MODULE, file.getOriginalFilename());
            filePath=dsc.getId();
        }catch (Exception e){
            log.error("上传文件失败{}",  e.getMessage());
        }

        //先保存数据迁移信息
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        try {
            DataMigration dataMigration1 = initDataMigrationRecord(dataMigration);
            dataMigration1.setFilePath(filePath);
            DataMigration add = add(dataMigration1);
            //读取文件
            //业务系统导入
            BusinessSystemImportResult businessSystemImportResult =importBusinessSheet(file,  add, dataMigration.getSkipOrCover());
            if(Objects.isNull(businessSystemImportResult)){
                log.error("业务系统导入失败");
                add.setStatus(SwitchStatus.OFF.getCode());
                add.setEndTime(new Date());
                getService(DataMigration.class).update(add);
                return;
            }
            //获取addList
            List<BusinessSystem> addedList = businessSystemImportResult.getAddedList();


            //错误map
            Map<String, String> errBusinessSystemMap = businessSystemImportResult.getErrBusinessSystemMap();
            /**
             * =============================================数据源登记===========================================================
             */

            //数据源
            SheetForDataMigrationDatasourceListener listener = new SheetForDataMigrationDatasourceListener();
            EasyExcel.read(file.getInputStream(),
                    DatasourceInfoTemplate.class,
                    listener).sheet(1).head(DatasourceInfoTemplate.class).doRead();
            //获取导入失败得数据源
            Map<DatasourceInfoTemplate, String> errorMap = listener.getErrorMap();
            if (CollUtil.isNotEmpty(errorMap)) {
                //创建数据源迁移详情集合
                List<DataMigrationDetail> dataMigrationDetailList = new ArrayList<>();
                for (Map.Entry<DatasourceInfoTemplate, String> entry : errorMap.entrySet()) {
                    DatasourceInfoTemplate key = entry.getKey();
                    String value = entry.getValue();
                    DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), "", "", key.getBusinessName(), "",
                            SwitchStatus.OFF.getCode(), value, "datasource");
                    dataMigrationDetailList.add(dataMigrationDetail);
                }
                getService(DataMigrationDetail.class).add(dataMigrationDetailList);
            }
            //失败数据源数据量
            int failCount = errorMap.size();
            //获取数据需要导入的数据源
            Map<String, List<DatasourceInfoTemplate>> validGroupMap = listener.getValidGroupMap();

            if (CollUtil.isNotEmpty(validGroupMap)) {
                //平台存在得业务系统
                List<BusinessSystem> existList = businessSystemService.getListByDel(false);
                //获取业务名称 生成新只有业务名称得集合List<String>
                Map<String, BusinessSystem> qianfan = new HashMap<>();
                if (CollUtil.isNotEmpty(existList)) {
                    existList.forEach(businessSystem -> {
                        qianfan.put(businessSystem.getBusinessName(), businessSystem);
                    });
                }
                Map<String, BusinessSystem> addedMap = new HashMap<>();
                if (CollUtil.isNotEmpty(addedList)) {
                    //addedList  转成 map  key  业务系统名称 value 业务系统对象
                    addedList.forEach(businessSystem -> {
                        addedMap.put(businessSystem.getBusinessName(), businessSystem);
                    });
                }

                //数据源类型
                Map<String, DatasourceType> datasourceTypeMap = getService(DatasourceType.class).getQuery()
                        .eq("delFlag", Boolean.FALSE)
                        .lazys("datasourceDrivers")
                        .list()
                        .stream()
                        .collect(Collectors.toMap(DatasourceType::getDataType, type -> type));
                //驱动
                Map<String, DatasourceDriver> datasourceDriverMap = new HashMap<>();
                datasourceTypeMap.forEach((typeName, type) -> {
                    if (CollUtil.isNotEmpty(type.getDatasourceDrivers())) {
                        for (DatasourceDriver datasourceDriver : type.getDatasourceDrivers()) {
                            datasourceDriverMap.put(type.getDataType() + datasourceDriver.getDriverVersion(), datasourceDriver);
                        }
                    }
                });

                //获取用户
                //用户
                List<User> users = getUsers();
                Map<String, User> userMap = users
                        .stream()
                        .collect(Collectors.toMap(
                                user -> user.getNickname() + "(" + user.getUsername() + ")",
                                Function.identity()
                        ));
                //数据源告警事件
                List<DatasourceAlertEvent> datasourceAlertEvents = getDatasourceAlertEvents();
                //角色
                List<Role> roles = getRoles();
                //项目
                List<Project> projects = getProjects();

                List<Callable<Integer>> tasks = new ArrayList<>();
                //这里每个数据源用多线程得方式处理 反回数据源导入状态 以及失败原因
                for (Map.Entry<String, List<DatasourceInfoTemplate>> entry : validGroupMap.entrySet()) {
                    Callable<Integer> task = () -> {
                        //定义数据源导入状态
                        int status = SwitchStatus.ON.getCode();
                        try {
                            //key 业务系统名称：数据源名称
                            String key = entry.getKey();
                            //节点
                            List<DatasourceInfoTemplate> value = entry.getValue();
                            //在key中获取业务系统名称
                            String businessName = key.split("::")[0];
                            //获取数据源名称
                            String datasourceName = key.split("::")[1];
                            //1.判断业务系统名称 是否存在  在失败的业务系统里面
                            if (errBusinessSystemMap.containsKey(businessName)) {
                                //获取失败原因
                                String errMsg = errBusinessSystemMap.get(businessName);
                                //创建数据迁移详情
                                DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), datasourceName, "", businessName, "",
                                        SwitchStatus.OFF.getCode(), errMsg, "datasource");
                                //直接保存
                                getService(DataMigrationDetail.class).add(dataMigrationDetail);
                                return SwitchStatus.OFF.getCode();
                            }
                            //2.判断业务系统名称 存在在新增或者更新业务系统里面
                            if (CollUtil.isNotEmpty(addedList)) {
                                //addedList  转成 map  key  业务系统名称 value 业务系统对象
                                BusinessSystem businessSystem = addedMap.get(businessName);
                                if (Objects.isNull(businessSystem)) {
                                    if (!qianfan.containsKey(businessName)) {
                                        //创建数据迁移详情
                                        DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), datasourceName, "", businessName, "",
                                                SwitchStatus.OFF.getCode(), "业务系统不存在", "datasource");
                                        //直接保存
                                        getService(DataMigrationDetail.class).add(dataMigrationDetail);
                                        return SwitchStatus.OFF.getCode();
                                    }

                                }
                            }
                            //3.校验数据源名称是否重复
//                            DatasourceInfo info = getService(DatasourceInfo.class).getQuery()
//                                    .eq("datasourceName", datasourceName).one();
                            DatasourceInfo info = datasourceInfoService.getDatasourceForDataMigrationByName(datasourceName);
                            //判断是不是在删除状态
                            if (Objects.nonNull(info)) {
                                //该数据源是否删除
                                if (info.getDelFlag().equals(Boolean.TRUE)) {
                                    //创建数据迁移详情
                                    DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), datasourceName, info.getId(), businessName, "",
                                            SwitchStatus.OFF.getCode(), "数据源名称已存在", "datasource");
                                    //直接保存
                                    getService(DataMigrationDetail.class).add(dataMigrationDetail);
                                    return SwitchStatus.OFF.getCode();
                                }
                            }

                            //当前业务系统
                            BusinessSystem businessSystem = qianfan.get(businessName);
                            //4.数据源没有重复

                            //校验数据获取新增还是修改
                            DatasourceImportResult datasourceImportResult = this.validateImportData(value, datasourceTypeMap, datasourceDriverMap, businessSystem, userMap, info, dataMigration.getSkipOrCover());
                            if (SwitchStatus.OFF.getCode() == datasourceImportResult.getStatus()) {
                                DatasourceInfoTemplate importDatum = datasourceImportResult.getImportDatum();
                                DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), importDatum.getDatasourceName(), "",
                                        importDatum.getBusinessName(), businessSystem.getId(),
                                        datasourceImportResult.getStatus(), datasourceImportResult.getErrMsg(), "datasource");
                                getService(DataMigrationDetail.class).add(dataMigrationDetail);
                                return SwitchStatus.OFF.getCode();
                            }
                            //获取type
                            String type = datasourceImportResult.getType();
                            DatasourceInfo datasourceInfo = datasourceImportResult.getDatasourceInfo();
                            //采集配置
                            if ("skip".equals(type)) {
                                DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), datasourceInfo.getDatasourceName(), datasourceInfo.getId(),
                                        businessSystem.getBusinessName(), businessSystem.getId(),
                                        datasourceImportResult.getStatus(), datasourceImportResult.getErrMsg(), "datasource");
                                getService(DataMigrationDetail.class).add(dataMigrationDetail);
                                return SwitchStatus.ON.getCode();
                            }
                            MetadataTask metadataTask = readMetaDataSheet(file,datasourceInfo);
                            //采集定时配置
                            MetadataTask metadataTaskCorn = readMetaDataSheetForCron(file, datasourceInfo);
                            //数据源告警
                            DatasourceAlert datasourceAlert = readDatasourceAlertSheet(file, datasourceInfo,datasourceAlertEvents, users, roles, projects);
                            if ("add".equals(type)) {
                                //保存数据源
                                // 保存数据源信息
                                DatasourceInfoSaveDTO saveDTO = new DatasourceInfoSaveDTO();
                                saveDTO.setDatasourceInfo(datasourceInfo);
                                saveDTO.setDatasourceNode(datasourceInfo.getDatasourceNodeList());
                                datasourceInfoService.saveDatasourceInfo(saveDTO);
                                DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), datasourceInfo.getDatasourceName(), datasourceInfo.getId(),
                                        businessSystem.getBusinessName(), businessSystem.getId(),
                                        datasourceImportResult.getStatus(), datasourceImportResult.getErrMsg(), "datasource");
                                getService(DataMigrationDetail.class).add(dataMigrationDetail);
                                //采集配置
                                if(Objects.nonNull(metadataTask)){
                                    if(Objects.nonNull(metadataTaskCorn)){
                                        metadataTask.setIsTiming(metadataTaskCorn.getIsTiming());
                                        metadataTask.setCronType(metadataTaskCorn.getCronType());
                                        metadataTask.setEffectiveStartDate(metadataTaskCorn.getEffectiveStartDate());
                                        metadataTask.setEffectiveEndDate(metadataTaskCorn.getEffectiveEndDate());
                                        metadataTask.setExecutionFrequency(metadataTaskCorn.getExecutionFrequency());
                                    }
                                    metadataTaskService.addDetail(metadataTask);
                                }
                                //告警保存
                                if(Objects.nonNull(datasourceAlert)){
                                    //保存 捕获异常
                                    try{
                                        datasourceAlertService.save(datasourceAlert);
                                    }catch (Exception e){
                                        log.error("保存数据源告警异常",e);
                                    }

                                }
                                return SwitchStatus.ON.getCode();
                            }
                            //覆盖
                            if ("update".equals(type)) {
                                //todo 更新数据源
                                //1.先删除 节点和属性值
                                EqCondition deleteTask = new EqCondition("datasourceInfoId", datasourceInfo.getId());
                                List<WhereCondition> whereConditions=new ArrayList<>();
                                whereConditions.add(deleteTask);
                                getService(DatasourceNode.class).deleteBy(whereConditions);
                                //删除属性值
                                //获取现在节点
                                List<DatasourceNode> datasourceNodeList = datasourceInfo.getDatasourceNodeList();
                                for (DatasourceNode datasourceNode : datasourceNodeList) {
                                    //删除属性值
                                    EqCondition deleteField = new EqCondition("datasourceNodeId", datasourceNode.getId());
                                    List<WhereCondition> whereConditionsField=new ArrayList<>();
                                    whereConditionsField.add(deleteField);
                                    getService(DatasourceFieldData.class).deleteBy(whereConditions);
                                }
                                //在保存新的节点和属性值
                                //获取新的节点和属性值
                                List<DatasourceNode> node = datasourceImportResult.getNode();
                                getService(DatasourceNode.class).add(node);
                                DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), datasourceInfo.getDatasourceName(), datasourceInfo.getId(),
                                        businessSystem.getBusinessName(), businessSystem.getId(),
                                        datasourceImportResult.getStatus(), datasourceImportResult.getErrMsg(), "datasource");
                                getService(DataMigrationDetail.class).add(dataMigrationDetail);
                                //采集配置
                                if(Objects.nonNull(metadataTask)){
                                    if(Objects.nonNull(metadataTaskCorn)){
                                        metadataTask.setIsTiming(metadataTaskCorn.getIsTiming());
                                        metadataTask.setCronType(metadataTaskCorn.getCronType());
                                        metadataTask.setEffectiveStartDate(metadataTaskCorn.getEffectiveStartDate());
                                        metadataTask.setEffectiveEndDate(metadataTaskCorn.getEffectiveEndDate());
                                        metadataTask.setExecutionFrequency(metadataTaskCorn.getExecutionFrequency());
                                    }
                                    metadataTaskService.updateByDefault(datasourceInfo.getId(),metadataTask);
                                }
                                //告警更新
                                if(Objects.nonNull(datasourceAlert)){
                                    //保存 捕获异常
                                    try{
                                        datasourceAlertService.modify(datasourceAlert);
                                    }catch (Exception e){
                                        log.error("更新数据源告警异常",e);
                                    }

                                }
                                return SwitchStatus.ON.getCode();
                            }
                        } catch (Exception e) {
                            log.error("数据源信息导入失败：{}", e.getMessage());
                            status = SwitchStatus.OFF.getCode();
                            e.printStackTrace();
                        }
                        return status;
                    };
                    tasks.add(task);

                }
                // 提交任务列表，并获取Future列表
                List<Future<Integer>> futures = new ArrayList<>();
                futures = executorService.invokeAll(tasks);

                //成功数
                int successCount = 0;
                //失败数
                
                for (Future<Integer> future : futures) {
                    try {
                        Integer status = future.get();
                        if (status == SwitchStatus.ON.getCode()) {
                            successCount++;
                        } else {
                            failCount++;
                        }
                    } catch (InterruptedException | ExecutionException e) {
                        log.error("数据源信息导入失败：{}", e.getMessage());
                    }
                    
                }
                try  {
                    //获取迁移详情得业务系统:统计业务系统导入的数据源数
                    List<DataMigrationDetail> dataMigrationId = getService(DataMigrationDetail.class).getQuery().eq("dataMigrationId", dataMigration.getId()).list();
                    if(CollUtil.isNotEmpty(dataMigrationId)){
                        //将dataMigrationId按type 分组
                        Map<String, List<DataMigrationDetail>> groupByType = dataMigrationId.stream().collect(Collectors.groupingBy(DataMigrationDetail::getType));
                        //获取business
                        if(groupByType.containsKey("business")){
                            List<DataMigrationDetail> business = groupByType.get("business");
                            //获取datasource
                            if(groupByType.containsKey("datasource")){
                                List<DataMigrationDetail> datasource = groupByType.get("datasource");
                                //将datasource 按busineesId 分组
                                Map<String, List<DataMigrationDetail>> groupByBusinessId = datasource.stream().collect(Collectors.groupingBy(DataMigrationDetail::getBusinessId));
                                business.forEach(businessData -> {
                                    if(groupByBusinessId.containsKey(businessData.getBusinessId())){
                                        List<DataMigrationDetail> datasources = groupByBusinessId.get(businessData.getBusinessId());
                                        //统计datasources status为0 且成功数 ，1为失败数 分别有多少个
                                        int success = (int) datasources.stream().filter(dataMigrationDetail -> dataMigrationDetail.getStatus() == 0).count();
                                        int fail = (int) datasources.stream().filter(dataMigrationDetail -> dataMigrationDetail.getStatus() == 1).count();
                                        //修改业务系统数据
                                        businessData.setSuccessCount(success);
                                        businessData.setFailCount(fail);
                                        getService(DataMigrationDetail.class).update(businessData.getId(), businessData);
                                    }

                                });
                            }
                        }
                    }

                } catch (Exception e) {
                    log.error("数据迁移导入，统计业务系统导入数据源数异常：{}", e.getMessage());
                }
                //修改任务状态和结束时间
                DataMigration update = new DataMigration();
                update.setId(dataMigration.getId());
                update.setEndTime(new Date());
                //成功数
                update.setSuccessCount(successCount);
                update.setFailCount(failCount);
                //状态
                update.setStatus(SwitchStatus.ON.getCode());
                update(dataMigration.getId(), update);
            }
            log.info("数据源信息导入完成：{}", dataMigration.getName());
        } catch (Exception e) {
            log.error("数据源信息导入失败：{}", e.getMessage());
            //修改任务状态和结束时间
            DataMigration update = new DataMigration();
            update.setId(dataMigration.getId());
            update.setEndTime(new Date());
            //状态
            update.setStatus(SwitchStatus.OFF.getCode());
            update(dataMigration.getId(), update);
        }finally {
            executorService.shutdown();
        }
    }

    /**
     * 校验导入数据
     */
    private DatasourceImportResult validateImportData(List<DatasourceInfoTemplate> importData,
                                                      Map<String, DatasourceType> datasourceTypeMap,
                                                      Map<String, DatasourceDriver> datasourceDriverMap,
                                                      BusinessSystem businessSystem,
                                                      Map<String, User> userMap,
                                                      DatasourceInfo datasourceInfo,
                                                      Integer skipOrUpdate) {
        DatasourceImportResult result = new DatasourceImportResult();

        try {
            DatasourceInfo datasourceInfoNew = new DatasourceInfo();
            //node集合
            List<DatasourceNode> nodeList = new ArrayList<>();
            //生效节点名称定义
            String effectiveNodeName = "";
            String datasourceid = IdUtils.simpleUUID();
            int i = 0;
            for (DatasourceInfoTemplate importDatum : importData) {
                if (StringUtils.isBlank(importDatum.getDatasourceTypeName())) {
                    result.setStatus(SwitchStatus.OFF.getCode());
                    result.setErrMsg("数据库类型不能为空");
                    result.setImportDatum(importDatum);
                    return result;
                }
                if (StringUtils.isBlank(importDatum.getDriverVersion())) {
                    result.setStatus(SwitchStatus.OFF.getCode());
                    result.setErrMsg("驱动版本不能为空");
                    result.setImportDatum(importDatum);

                    return result;
                }
                // 校验数据库类型是否存在
                if (!datasourceTypeMap.containsKey(importDatum.getDatasourceTypeName())) {
                    result.setStatus(SwitchStatus.OFF.getCode());
                    result.setErrMsg("数据库类型不存在：" + importDatum.getDatasourceTypeName());
                    result.setImportDatum(importDatum);
                    return result;
                }
                // 校验负责人是否存在
                if (!userMap.containsKey(importDatum.getOwner())) {
                    result.setStatus(SwitchStatus.OFF.getCode());
                    result.setErrMsg("负责人不存在：" + importDatum.getOwner());
                    result.setImportDatum(importDatum);
                    return result;

                }
                if (StringUtils.isBlank(importDatum.getHostPort()) && StringUtils.isBlank(importDatum.getJdbcUrl())) {
                    result.setStatus(SwitchStatus.OFF.getCode());
                    result.setErrMsg("jdbcUrl 和 ip 端口 不能同时为空");
                    result.setImportDatum(importDatum);
                    return result;
                }
                //节点名不能为空
                if (StringUtils.isBlank(importDatum.getNodeName())) {
                    result.setStatus(SwitchStatus.OFF.getCode());
                    result.setErrMsg("节点名不能为空");
                    result.setImportDatum(importDatum);
                    return result;
                }
                //生效节点不能为空
                if (StringUtils.isBlank(importDatum.getEffectiveNode())) {
                    result.setStatus(SwitchStatus.OFF.getCode());
                    result.setErrMsg("生效节点不能为空");
                    result.setImportDatum(importDatum);
                    return result;
                }

                effectiveNodeName = importDatum.getEffectiveNode();
                //构建数据源信息
                // 创建数据源信息
                datasourceInfoNew.setDatasourceName(importDatum.getDatasourceName());
                datasourceInfoNew.setPersonCharge(userMap.get(importDatum.getOwner()).getId());
                datasourceInfoNew.setBusinessSystemId(businessSystem.getId());
                datasourceInfoNew.setDatasourceTypeId(datasourceTypeMap.get(importDatum.getDatasourceTypeName()).getId());
                datasourceInfoNew.setDatasourceDriverId(datasourceDriverMap.get(importDatum.getDatasourceTypeName() + importDatum.getDriverVersion()).getId());
                datasourceInfoNew.setDbVersion(importDatum.getDriverVersion());
                datasourceInfoNew.setAuthFlag(DatasourceInfo.AuthFlagStatus.UNAUTHORIZED);
                //设置一个id
                datasourceInfoNew.setId(datasourceid);
                //创建节点信息
                DatasourceNode node = new DatasourceNode();
                node.setNodeName(importDatum.getNodeName());
                String serialNumber = "tab" + i;
                node.setSerialNumber(serialNumber);
                node.setJdbcUrl(importDatum.getJdbcUrl());
                //节点id
                String nodeId = IdUtils.simpleUUID();
                node.setId(nodeId);

                // 创建节点字段数据
                List<DatasourceFieldData> fieldDataList = new ArrayList<>();
                fieldDataList.add(datasourceInfoService.createFieldData(DatasourceFormField.PropsKeys.CONFIG_TYPE, datasourceInfoService.isHostOrJdbcString(importDatum.getJdbcUrl())));
                if (datasourceInfoService.isHostOrJdbcBoolean(importDatum.getJdbcUrl())) {
                    fieldDataList.add(datasourceInfoService.createFieldData(DatasourceFormField.PropsKeys.JDBC, importDatum.getJdbcUrl()));
                } else {
                    List<JdbcUrlBuilderUtil.HostPort> hostPortList = new ArrayList<>();
                    String serverAddress = importDatum.getHostPort();
                    if (serverAddress.contains(",")) {
                        for (String s : serverAddress.split(",")) {
                            String[] hostPortSplit = s.split(":");
                            JdbcUrlBuilderUtil.HostPort hostPort = new JdbcUrlBuilderUtil.HostPort(hostPortSplit[0], Integer.parseInt(hostPortSplit[1]));
                            hostPortList.add(hostPort);
                        }
                    }else{
                        String[] hostPortSplit = serverAddress.split(":");
                        JdbcUrlBuilderUtil.HostPort hostPort = new JdbcUrlBuilderUtil.HostPort(hostPortSplit[0], Integer.parseInt(hostPortSplit[1]));
                        hostPortList.add(hostPort);
                    }
                    datasourceInfoNew.setServerAddress(serverAddress);
                    fieldDataList.add(datasourceInfoService.createFieldData(DatasourceFormField.PropsKeys.HOST_PORT, JSONObject.toJSONString(hostPortList)));
                }
                fieldDataList.add(datasourceInfoService.createFieldData(DatasourceFormField.PropsKeys.DB_NAME, importDatum.getDbName()));
                fieldDataList.add(datasourceInfoService.createFieldData(DatasourceFormField.PropsKeys.SCHEMA, importDatum.getSchema()));
                fieldDataList.add(datasourceInfoService.createFieldData(DatasourceFormField.PropsKeys.USER_NAME, importDatum.getUsername()));
                fieldDataList.add(datasourceInfoService.createFieldData(DatasourceFormField.PropsKeys.PASSWORD, importDatum.getPassword()));
                fieldDataList.add(datasourceInfoService.createFieldData(DatasourceFormField.PropsKeys.REMARK, importDatum.getRemark()));
                node.setDatasourceFieldData(fieldDataList);
                nodeList.add(node);

                //数据源生效节点设置
                if(importDatum.getNodeName().equalsIgnoreCase(effectiveNodeName)){
                    effectiveNodeName=serialNumber;
                    datasourceInfoNew.setDatasourceNodeSerialNumber(effectiveNodeName);
                }
                i++;
            }
            datasourceInfoNew.setDatasourceNodeList(nodeList);
            datasourceInfoNew.setDelFlag(Boolean.FALSE);
            //数据源dataTypeCode
            //新增
            result.setStatus(SwitchStatus.ON.getCode());
            result.setDatasourceInfo(datasourceInfoNew);
            result.setNode(nodeList);
            if (Objects.isNull(datasourceInfo)) {
                result.setType("add");
                return result;
            }
            //如果是跳过
            if (SwitchStatus.ON.getCode() == skipOrUpdate) {
                result.setStatus(SwitchStatus.ON.getCode());
                result.setErrMsg("跳过");
                result.setType("skip");
                result.setDatasourceInfo(datasourceInfoNew);
                return result;
            }

            //覆盖
            result.setType("update");
            datasourceInfoNew.setId(datasourceInfo.getId());
            datasourceInfoNew.setDbid(datasourceInfo.getDbid());
            result.setDatasourceInfo(datasourceInfoNew);
            result.setNode(nodeList);
            return result;
        } catch (Exception e) {
            log.error("数据源信息导入失败：{}", e.getMessage());
            result.setStatus(SwitchStatus.OFF.getCode());
            result.setErrMsg(e.getMessage());
        }

        return result;
    }

    /**
     * 业务系统导入
     */
    private BusinessSystemImportResult importBusinessSheet(MultipartFile file,DataMigration add,Integer skipOrUpdate)  {
        boolean  result = true;

        BusinessSystemImportResult businessSystemImportResult= new BusinessSystemImportResult();
        try {
            businessSystemImportResult = readBusinessSheet(file);
        }catch (Exception e){
            //读取文件失败，直接失败
            add.setStatus(SwitchStatus.OFF.getCode());
            update(add.getId(),add);
            return businessSystemImportResult;
        }
        try {
            //获取业务系统导入失败得数据
            Map<String, String> errBusinessSystemMap = businessSystemImportResult.getErrBusinessSystemMap();
            if (CollUtil.isNotEmpty(errBusinessSystemMap)) {
                //创建数据源迁移详情集合
                List<DataMigrationDetail> dataMigrationDetailList = new ArrayList<>();
                for (Map.Entry<String, String> entry : errBusinessSystemMap.entrySet()) {
                    //业务系统名称
                    String businessName = entry.getKey();
                    String errMsg = entry.getValue();
                    DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), "", "", businessName, "",
                            SwitchStatus.OFF.getCode(), errMsg, "business");
                    dataMigrationDetailList.add(dataMigrationDetail);
                }
                getService(DataMigrationDetail.class).add(dataMigrationDetailList);
                add.setBusinessFailCount(dataMigrationDetailList.size());
            }
            //新增业务系统
            List<BusinessSystem> addedList = businessSystemImportResult.getAddedList();
            if (CollUtil.isNotEmpty(addedList)) {
                getService(BusinessSystem.class).add(addedList);
                add.setBusinessSuccessCount(addedList.size());
            }
            //更新业务系统
            //先判断业务此任务是跳过还是覆盖
            List<BusinessSystem> updatedList = businessSystemImportResult.getUpdatedList();
            if (SwitchStatus.OFF.getCode() == skipOrUpdate) {
                //覆盖
                if (CollUtil.isNotEmpty(updatedList)) {
                    for (BusinessSystem businessSystem : updatedList) {
                        getService(BusinessSystem.class).update(businessSystem);
                    }
                    addedList.addAll(updatedList);
                }
            }
            List<DataMigrationDetail> dataMigrationDetailListB = new ArrayList<>();
            //业务系统成功记录数据迁移详情
            for (BusinessSystem businessSystem : addedList) {
                DataMigrationDetail dataMigrationDetail = buildDataMigrationDetail(add.getId(), "", "", businessSystem.getBusinessName(), businessSystem.getId(),
                        SwitchStatus.ON.getCode(), "", "business");
                dataMigrationDetailListB.add(dataMigrationDetail);
            }
            if (CollUtil.isNotEmpty(dataMigrationDetailListB)) {
                getService(DataMigrationDetail.class).add(dataMigrationDetailListB);
            }
            add.setBusinessSuccessCount(updatedList.size() + addedList.size());
            getService(DataMigration.class).update(add);
            return businessSystemImportResult;
        }catch (Exception e){
            log.error("数据迁移业务系统信息导入失败：{}", e.getMessage());
            result = false;
            add.setStatus(SwitchStatus.OFF.getCode());
//            dataMigration.setErrMsg(e.getMessage());
            update(add.getId(),add);

        }

        return new BusinessSystemImportResult();

    }

    /**
     * 构建迁移详情对象
     */
    private DataMigrationDetail buildDataMigrationDetail(String dataMigrationId,
                                                         String datasourceName,
                                                         String datasourceId,
                                                         String businessName,
                                                         String businessId,
                                                         int status,
                                                         String errorMsg,
                                                         String type) {
        DataMigrationDetail dataMigrationDetail = new DataMigrationDetail();
        dataMigrationDetail.setDataMigrationId(dataMigrationId);
        dataMigrationDetail.setDatasourceName(datasourceName);
        dataMigrationDetail.setBusinessName(businessName);
        dataMigrationDetail.setStatus(status);
        dataMigrationDetail.setErrorMsg(errorMsg);
        dataMigrationDetail.setDatasourceId(datasourceId);
        dataMigrationDetail.setBusinessId(businessId);
        dataMigrationDetail.setType(type);
        return dataMigrationDetail;
    }


    /**
     * 数据迁移：读取文件第一个sheet 业务系统
     */
    private BusinessSystemImportResult readBusinessSheet(MultipartFile file) throws Exception {

        SheetForDataMigrationBusinessListener listener = new SheetForDataMigrationBusinessListener(businessSystemService);
        EasyExcel.read(file.getInputStream(),
                BusinessSystemTemplate.class,
                listener).sheet(0).head(BusinessSystemTemplate.class).doRead();
        // 使用 EasyExcel 读取数据时传入 listener
        BusinessSystemImportResult result = listener.getImportResult();
        return result;
    }

    /**
     * 数据迁移读取第三个sheet 采集配置
     */
    private MetadataTask readMetaDataSheet(MultipartFile file,DatasourceInfo datasourceInfo)  {
       try{
           SheetForDataMigrationTaskListener listener = new SheetForDataMigrationTaskListener(datasourceInfo);
           EasyExcel.read(file.getInputStream(),
                   MataDataTaskTemplate.class,
                   listener).sheet(2).head(MataDataTaskTemplate.class).doRead();
           MetadataTask mataDataTask = listener.getMataDataTask();
           Map<MataDataTaskTemplate, String> errorMap = listener.getErrorMap();
           if (Objects.nonNull(errorMap)) {
               for (MataDataTaskTemplate mataDataTaskJobTemplate : errorMap.keySet()) {
                   // 数据源名称
                   String datasourceName = mataDataTaskJobTemplate.getDatasourceName();
                   if(datasourceName != null && datasourceName.equals(datasourceInfo.getDatasourceName())){
                       return null;
                   }
               }
           }
           return mataDataTask;
       }catch (Exception e){
           log.error("数据迁移-数据源{}采集配置息导入失败：{}",datasourceInfo.getDatasourceName(), e.getMessage());
       }
       return null;
    }

    /**
     * 数据迁移：读取文件第4个sheet 采集定时配置
     */
    private MetadataTask readMetaDataSheetForCron(MultipartFile file,DatasourceInfo datasourceInfo)  {
        try{
            SheetForDataMigrationTaskJobListener listener = new SheetForDataMigrationTaskJobListener(datasourceInfo);
            EasyExcel.read(file.getInputStream(),
                    MataDataTaskJobTemplate.class,
                    listener).sheet(3).head(MataDataTaskJobTemplate.class).doRead();
            MetadataTask mataDataTask = listener.getMataDataTask();
            Map<MataDataTaskJobTemplate, String> errorMap = listener.getErrorMap();
            if (Objects.nonNull(errorMap)) {
                for (MataDataTaskJobTemplate mataDataTaskJobTemplate : errorMap.keySet()) {
                   // 数据源名称
                    String datasourceName = mataDataTaskJobTemplate.getDatasourceName();
                    if(datasourceName != null && datasourceName.equals(datasourceInfo.getDatasourceName())){
                        return null;
                    }
                }
            }
            return mataDataTask;
        }catch (Exception e){
            log.error("数据迁移-数据源{}采集定时配置息导入失败：{}",datasourceInfo.getDatasourceName(), e.getMessage());
        }
        return null;
    }

    private DatasourceAlert readDatasourceAlertSheet(MultipartFile file,
                                                     DatasourceInfo datasourceInfo,
                                                     List<DatasourceAlertEvent> datasourceAlertEvents,
                                                     List<User> users,
                                                     List<Role> roles,
                                                     List<Project> projects
    )  {
        try{
            SheetForDataMigrationDatasourceAlertListener listener = new SheetForDataMigrationDatasourceAlertListener(datasourceInfo,
                    datasourceAlertEvents,users,roles,projects);
            EasyExcel.read(file.getInputStream(),
                    DatasourceAlertTemplate.class,
                    listener).sheet(4).head(DatasourceAlertTemplate.class).doRead();
            DatasourceAlert datasourceAlert = listener.getDatasourceAlert();
            Map<DatasourceAlertTemplate, String> errorMap = listener.getErrorMap();
            if (Objects.nonNull(errorMap)) {
                for (DatasourceAlertTemplate datasourceAlertTemplate : errorMap.keySet()) {
                    // 数据源名称
                    String datasourceName = datasourceAlertTemplate.getDatasourceName();
                    if (datasourceName != null && datasourceName.equals(datasourceInfo.getDatasourceName())) {
                        return null;
                    }
                }
            }
            return datasourceAlert;
        }catch (Exception e){
            log.error("数据迁移-数据源{}数据源告警信息导入失败：{}",datasourceInfo.getDatasourceName(), e.getMessage());
        }
        return null;
    }


    private DataMigration initDataMigrationRecord(DataMigration dto) {
        DataMigration dataMigration = new DataMigration();
        String id = dto.getId();
        if (StringUtils.isEmpty(id)) {
           id= IdUtils.simpleUUID();
        }
        dataMigration.setId(id);
        dataMigration.setName(dto.getName());
        dataMigration.setStartTime(new Date());
        dataMigration.setType("import");
        dataMigration.setSkipOrCover(dto.getSkipOrCover());
        dataMigration.setStatus(SwitchStatus.ON.getCode());
        return dataMigration;
    }

    /**
     * 数据迁移删除
     */
    public List<DataMigration> batchDelete(List<String> ids) {
        List<DataMigration> dataMigrations = new ArrayList<>();
        if (CollUtil.isNotEmpty(ids)) {
            WhereCondition whereConditions = new InCondition("id", ids);
            Integer integer = super.deleteBy(whereConditions);
            if (integer > 0) {
              //删除迁移记录
                WhereCondition condition = new InCondition("dataMigrationId", ids);
                getService(DataMigrationDetail.class).deleteBy(condition);
            }
        }
        return dataMigrations;
    }

    /**
     * 查询所有告警事件
     */
    public List<DatasourceAlertEvent> getDatasourceAlertEvents() {
        return getService(DatasourceAlertEvent.class).getQuery().list();
    }

    /**
     * 获取所有用户
     */
    public List<User> getUsers() {
        List<User> userList = cmsFeignService.getAllUsers();
        return userList;
    }

    /**
     * 获取所有角色
     */
    public List<Role> getRoles() {
        List<Role> roleList = cmsFeignService.getAllRoles();
        return roleList;
    }

    /**
     * 获取所有项目
     */
    public List<Project> getProjects() {
        List<Project> projectList = cmsFeignService.getAllProjects();
        return projectList;
    }



}
