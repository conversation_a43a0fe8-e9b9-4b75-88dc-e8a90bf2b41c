package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.joyadata.cms.model.Role;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.vo.RoleVO;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.interfaces.IService;
import com.joyadata.model.BaseBean;
import com.joyadata.service.BaseServiceImpl;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName RoleService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/12 13:37
 * @Version 1.0
 **/
@Service
public class RoleService {

    private JoyaFeignService<Role> roleJoyaFeignService = FeignFactory.make(Role.class);
    @Autowired
    private BaseServiceImpl baseServiceImpl;

    public Integer getRoleTotal(String keywords, String datasourceInfoId, Boolean checked) {
        String roleIds = getRoleIds(datasourceInfoId);
        IQueryWrapper<Role> roleIQueryWrapper = getRoleIQueryWrapper(keywords, checked, roleIds, roleJoyaFeignService.getQuery());
        return roleIQueryWrapper.total();
    }

    @NotNull
    private String getRoleIds(String datasourceInfoId) {
        String roleIds = "未知";
        IService<DatasourceAlert> datasourceAlertIService = baseServiceImpl.getService(DatasourceAlert.class);
        DatasourceAlert datasourceAlert = datasourceAlertIService.getQuery()
                .eq("datasourceInfoId", datasourceInfoId)
                .one();
        if (null != datasourceAlert) {
            roleIds = Optional.ofNullable(datasourceAlert.getRoleIds()).orElse("未知");
        }
        return roleIds;
    }

    public List<RoleVO> getRoles(String keywords, String datasourceInfoId, Boolean checked, Integer page, Integer pager) {
        String roleIds = getRoleIds(datasourceInfoId);
        IQueryWrapper<Role> roleIQueryWrapper = getRoleIQueryWrapper(keywords, checked, roleIds, roleJoyaFeignService.getQuery()
                //.searchby("username,nickname", keywords)
                //.in("id", roleIds)
                .sortbyDesc("createTime"));
        if (null != page && null != pager) {
            roleIQueryWrapper.page(page, pager);
        }

        List<Role> roles = roleIQueryWrapper.list();
        return convertToRoleVOList(roleIds, roles);
    }

    private List<RoleVO> convertToRoleVOList(String roleIds, List<Role> roles) {
        List<RoleVO> roleVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(roles)) {
            List<String> roleIdList = StrUtil.split(roleIds, ",");
            for (Role role : roles) {
                RoleVO roleVO = new RoleVO();
                BeanUtils.copyProperties(role, roleVO);
                if (roleIdList.contains(role.getId())) {
                    roleVO.setChecked(true);
                } else {
                    roleVO.setChecked(false);
                }
                roleVOS.add(roleVO);
            }
        }
        return roleVOS;
    }

    private IQueryWrapper<Role> getRoleIQueryWrapper(String keywords, Boolean checked, String roleIds, IQueryWrapper<Role> iQueryWrapper) {
        IQueryWrapper<Role> roleIQueryWrapper = iQueryWrapper;
        //.page(page, pager);
        if (null != checked) {
            if (checked) {
                queryParam(roleIQueryWrapper, "id", roleIds, "IN");
            } else {
                queryParam(roleIQueryWrapper, "id", roleIds, "NOTIN");
            }
        }
        queryParam(roleIQueryWrapper, "name", keywords, "SEARCHBY");
        return roleIQueryWrapper;
    }

    private void queryParam(IQueryWrapper<? extends BaseBean> query, String paramName, String paramValue, String symbol) {
        if ("EQ".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.eq(paramName, paramValue);
            }
        } else if ("SEARCHBY".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.searchby(paramName, paramValue);
            }
        } else if ("SETIN".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.setin(paramName, paramValue);
            }
        } else if ("LIKE".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.like(paramName, paramValue);
            }
        } else if ("IN".equals(symbol)) {
            List<String> ids = null;
            if (StrUtil.isNotBlank(paramValue)) {
                ids = StrUtil.split(paramValue, ",");
                query.in(paramName, ids);
            }
        } else if ("NOTIN".equals(symbol)) {
            List<String> ids = null;
            if (StrUtil.isNotBlank(paramValue)) {
                ids = StrUtil.split(paramValue, ",");
                query.notIn(paramName, ids);
            }
        }
    }
}
