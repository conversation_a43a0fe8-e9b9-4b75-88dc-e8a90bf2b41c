package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import com.joyadata.dsc.constants.Constants;
import com.joyadata.dsc.model.datasoure.DatasourceType;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.service.BaseService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11
 */
@Service
public class DatasourceTypeService extends BaseService<DatasourceType> {

    @Override
    public List<DatasourceType> getList(List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        if (CollUtil.isNotEmpty(conditionGroupList)) {
            for (ConditionGroup group : conditionGroupList) {
                List<WhereCondition> conditions = group.getConditions();
                if (CollUtil.isNotEmpty(conditions)) {
                    for (WhereCondition condition : conditions) {
                        // 如果查询全部  删除查询条件 查询全部数据
                        if ("datasource_classify_id".equals(condition.getFiled())) {
                            if (Constants.ALL_DATATYPE_ID.equals(condition.getValue())) {
                                conditions.remove(condition);
                            }
                        }
                        break;
                    }
                }
            }
        }
        return this.getQuery().andConditionGroupList(conditionGroupList).orConditionGroupList(orConditionGroupList).addGroupByCondition(groupByCondition).addQueryFilter(queryFilter).page(page, pager).list();
    }
}
