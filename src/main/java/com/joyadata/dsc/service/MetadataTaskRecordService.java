package com.joyadata.dsc.service;


import cn.hutool.core.collection.CollUtil;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.joyadata.dsc.collector.MetadataCollector;
import com.joyadata.dsc.collector.MetadataCollectorFactory;
import com.joyadata.dsc.enums.MetadataTaskObjectEnum;
import com.joyadata.dsc.model.metadata.*;
import com.joyadata.dsc.model.metadata.vo.MetadataTaskRecordResultVO;
import com.joyadata.exception.AppErrorException;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 采集记录
 * <AUTHOR>
 */
@Service
@Slf4j
public class MetadataTaskRecordService extends BaseService<MetadataTaskRecord> {

    @Autowired
    MetadataTableCommonDetailService metadataTableCommonDetailService;
    @Autowired
    MetadataColumnCommonDetailService metadataColumnCommonDetailService;

    @Override
    public Integer delete(String id, MetadataTaskRecord bean) {
        Integer delete = super.delete(id);
        if(delete>0){
            //1.删除表记录
            metadataTableCommonDetailService.deleteByTaskRecordId(id);
            //2.删除字段记录
            metadataColumnCommonDetailService.deleteByTaskRecordId(id);
            //3.删除配置记录
            EqCondition deleteTask = new EqCondition("metadataTaskRecordId",id);
            List<WhereCondition> whereConditions=new ArrayList<>();
            whereConditions.add(deleteTask);
            getService(MetadataTask.class).deleteBy(whereConditions);
            //4.删除选择表记录
            EqCondition deleteSelect = new EqCondition("metadataTaskRecordId",id);
            List<WhereCondition> whereConditionsSelect=new ArrayList<>();
            whereConditionsSelect.add(deleteSelect);
            getService(MetadataSelect.class).deleteBy(whereConditionsSelect);
        }
        return delete;
    }
    /*
    * 批量删除
     */
    public Integer deleteBatch(List<String> ids) {
        for (String id : ids) {
            Integer delete = super.delete(id);
            if(delete>0){
                //1.删除表记录
                metadataTableCommonDetailService.deleteByTaskRecordId(id);
                //2.删除字段记录
                metadataColumnCommonDetailService.deleteByTaskRecordId(id);
                //3.删除配置记录
                EqCondition deleteTask = new EqCondition("metadataTaskRecordId",id);
                List<WhereCondition> whereConditions=new ArrayList<>();
                whereConditions.add(deleteTask);
                getService(MetadataTask.class).deleteBy(whereConditions);
                //4.删除选择表记录
                EqCondition deleteSelect = new EqCondition("metadataTaskRecordId",id);
                List<WhereCondition> whereConditionsSelect=new ArrayList<>();
                whereConditionsSelect.add(deleteSelect);
                getService(MetadataSelect.class).deleteBy(whereConditionsSelect);
            }
        }
        return 0;
    }

    //分dbName统计此次库采集的表数量、视图数量、字段数量，以及表总数、视图总数、字段总数
    public List<MetadataTaskRecordResultVO> getResultByTaskRecordId(String id,String opt){
        //获取记录
        List<MetadataTaskRecordResultVO> metadataTaskRecordResultList=new ArrayList<>();
        MetadataTaskRecord metadataTaskRecord = getById(id);
        if(metadataTaskRecord==null){
            return metadataTaskRecordResultList;
        }
        //获取数据源类型
        String dataSourceType = metadataTaskRecord.getDatasourceType();
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(dataSourceType.toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        if (Objects.isNull(collector)) {
            throw new AppErrorException("数据源类型暂不支持统计结果" + dataSourceType);
        }
        try {
            metadataTaskRecordResultList = collector.getResultByTaskRecordId(id,opt);
        }catch (Exception e){
            log.error("采集记录统计结果失败:{}",e.getMessage());
        }
        return metadataTaskRecordResultList;

    }
}
