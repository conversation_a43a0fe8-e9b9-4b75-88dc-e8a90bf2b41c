package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.cms.model.User;
import com.joyadata.dsc.model.record.RecordsOperation;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.model.sql.ConditionGroup;
import com.joyadata.model.sql.GroupByCondition;
import com.joyadata.model.sql.LeftJoinCondition;
import com.joyadata.model.sql.QueryFilter;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class RecordsOperationService extends BaseService<RecordsOperation> {

    private JoyaFeignService<User> userJoyaFeignService = FeignFactory.make(User.class);

    @Override
    public List<RecordsOperation> getList(List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        List<RecordsOperation> result = super.getList(conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
        setOperator(result);
        return result;
    }

    @Override
    public List<JSONObject> getList(String[] sortby, Integer page, Integer pager, Set<String> filters, List<LeftJoinCondition> joinConditionList, List<ConditionGroup> andConditionGroup, List<ConditionGroup> orConditionGroup, List<ConditionGroup> havingConditionGroup, Map<String, String> aliasMap, List<String> groupby, List<String> aggfun, List<String> groupAlias, List<String> groupSortby, String[] lazys, String[] unlazys, String[] withs, String[] unwiths, String[] ignores, String cascade, Boolean ignorePermissions) {
        List<JSONObject> result = super.getList(sortby, page, pager, filters, joinConditionList, andConditionGroup, orConditionGroup, havingConditionGroup, aliasMap, groupby, aggfun, groupAlias, groupSortby, lazys, unlazys, withs, unwiths, ignores, cascade, ignorePermissions);
        setOperatorJSON(result);
        return result;
    }

    /**
     * 设置操作人
     * @param list 操作记录集合
     */
    private void setOperator(List<RecordsOperation> list){
        if (CollUtil.isNotEmpty(list)) {
            Map<String, String> userMap = new HashMap<>();
            for (RecordsOperation recordsOperations : list) {
                if (StringUtils.isNotBlank(recordsOperations.getCreateBy())) {
                    if (userMap.containsKey(recordsOperations.getCreateBy())) {
                        recordsOperations.setOperator(userMap.get(recordsOperations.getCreateBy()));
                    } else {
                        try {
                            User user = userJoyaFeignService.getById(recordsOperations.getCreateBy());
                            if (user != null) {
                                recordsOperations.setOperator(user.getNickname());
                                userMap.put(recordsOperations.getCreateBy(), user.getNickname());
                            } else {
                                userMap.put(recordsOperations.getCreateBy(), "");
                            }
                        } catch (Exception e) {
                            log.error("获取用户失败，{}", e.getMessage());
                        }
                    }
                }
            }
        }
    }

    private void setOperatorJSON(List<JSONObject> list){
        if (CollUtil.isNotEmpty(list)) {
            Map<String, String> userMap = new HashMap<>();
            for (JSONObject recordsOperations : list) {
                String createBy = recordsOperations.getString("createBy");
                if (StringUtils.isNotBlank(createBy)) {
                    if (userMap.containsKey(createBy)) {
                        recordsOperations.put("operator", userMap.get(createBy));
                    } else {
                        try {
                            User user = userJoyaFeignService.getById(createBy);
                            if (user != null) {
                                recordsOperations.put("operator", user.getNickname());
                                userMap.put(createBy, user.getNickname());
                            } else {
                                userMap.put(createBy, "");
                            }
                        } catch (Exception e) {
                            log.error("获取用户失败，{}", e.getMessage());
                        }
                    }
                }
            }
        }
    }
}