package com.joyadata.dsc.service;

import com.joyadata.dsc.model.metadata.MetadataTaskStep;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 采集任务步骤服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class MetadataTaskStepService extends BaseService<MetadataTaskStep> {


    /**
     * 保存采集任务步骤，参数为 排序 步骤名称 步骤状态 步骤错误信息 步骤开始时间 步骤结束时间
     *
     */
    public MetadataTaskStep save(String id,Integer pos, String stepName,String stepContent,String metadataTaskRecordId, Integer stepStatus, String stepErrorMsg, Date stepStartTime, Date stepEndTime) {
        MetadataTaskStep metadataTaskStep = new MetadataTaskStep();
        metadataTaskStep.setPos(pos);
        metadataTaskStep.setId(id);
        metadataTaskStep.setStepName(stepName);
        metadataTaskStep.setStepStatus(stepStatus);
        metadataTaskStep.setStepErrorMsg(stepErrorMsg);
        metadataTaskStep.setStepStartTime(stepStartTime);
        metadataTaskStep.setStepEndTime(stepEndTime);
        metadataTaskStep.setMetadataTaskRecordId(metadataTaskRecordId);
        metadataTaskStep.setStepContent(stepContent);
        return super.add(metadataTaskStep);
    }

    public Integer update(String id,Integer stepStatus, String stepErrorMsg, Date stepEndTime) {
        MetadataTaskStep metadataTaskStep = new MetadataTaskStep();
        metadataTaskStep.setId(id);
        metadataTaskStep.setStepStatus(stepStatus);
        metadataTaskStep.setStepErrorMsg(stepErrorMsg);
        metadataTaskStep.setStepEndTime(stepEndTime);

        Integer update = super.update(metadataTaskStep);
        return update;
    }
}
