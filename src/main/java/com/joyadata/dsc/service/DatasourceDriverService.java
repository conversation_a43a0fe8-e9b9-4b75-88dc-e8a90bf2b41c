package com.joyadata.dsc.service;

import cn.hutool.core.util.StrUtil;
import com.joyadata.dsc.constants.Constants;
import com.joyadata.dsc.enums.PublicConstants;
import com.joyadata.dsc.model.datasoure.ClassifyTree;
import com.joyadata.dsc.model.datasoure.DatasourceClassify;
import com.joyadata.dsc.model.datasoure.DatasourceDriver;
import com.joyadata.dsc.model.datasoure.DatasourceType;
import com.joyadata.dsc.properties.AppProperties;
import com.joyadata.dsc.properties.DatabaseProperties;
import com.joyadata.dsc.utils.FileUtil;
import com.joyadata.dsc.utils.tree.TreeUtil;
import com.joyadata.exception.AppWarningException;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.file.model.AppFile;
import com.joyadata.file.model.JoyadataAppFile;
import com.joyadata.model.web.Response;
import com.joyadata.service.BaseService;
import com.joyadata.util.GeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.FileSystemUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
@Slf4j
@Service
public class DatasourceDriverService extends BaseService<DatasourceDriver> {

    @Autowired
    private DatabaseProperties databaseProperties;
    @Autowired
    private AppProperties appProperties;

    private JoyaFeignService<JoyadataAppFile> fileJoyaFeignService = FeignFactory.make(JoyadataAppFile.class);

    public List<ClassifyTree> tree() {
        List<ClassifyTree> trees = new ArrayList<>();
        // 查询一级目录列表
        List<DatasourceClassify> datasourceClassifies = getDatasourceClassifies();
        // 查询二级目录列表
        List<DatasourceType> datasourceTypes = getDatasourceTypes();

        Map<String, Long> datasourceTypeMap = getDatasourceTypeCountMap();
        // 一级目录转成ClassifyTree结构
        for (DatasourceClassify datasourceClassify : Optional.ofNullable(datasourceClassifies).orElse(new ArrayList<>())) {
            ClassifyTree classifyTree = getClassifyTree(datasourceClassify.getId(), Constants.TREE_PID_ID, 0, datasourceClassify.getClassifyName());
            classifyTree.setClassifyCode(datasourceClassify.getClassifyCode());
            trees.add(classifyTree);
        }
        // 二级目录转成ClassifyTree结构
        for (DatasourceType datasourceType : Optional.ofNullable(datasourceTypes).orElse(new ArrayList<>())) {
            ClassifyTree classifyTree = getClassifyTree("datasourceType" + datasourceType.getId(), datasourceType.getDatasourceClassifyId(), 1, datasourceType.getDataType());
            // 根据分组Map设置驱动数量
            classifyTree.setCount(Optional.ofNullable(datasourceTypeMap.get(datasourceType.getId())).orElse(0L).intValue());
            trees.add(classifyTree);
        }
        // 构建目录树结构
        List<ClassifyTree> classifyTrees = TreeUtil.classifyTreeList(trees, Constants.TREE_PID_ID);
        // 计算父级节点驱动数量
        for (ClassifyTree classifyTree : classifyTrees) {
            TreeUtil.calculateParentCounts(classifyTree);
        }

        // 还原原本的id
        for (ClassifyTree tree : trees) {
            if (tree.getId().contains("datasourceType")) {
                tree.setId(tree.getId().replace("datasourceType", ""));
            }
        }
        return classifyTrees;
    }

    @NotNull
    private ClassifyTree getClassifyTree(String id, String treePidId, int i, String classifyName) {
        ClassifyTree classifyTree = new ClassifyTree();
        classifyTree.setId(id);
        classifyTree.setPid(treePidId);
        classifyTree.setLevel(i);
        classifyTree.setName(classifyName);
        classifyTree.setCount(0);
        return classifyTree;
    }

    // 获取一级目录列表
    private List<DatasourceClassify> getDatasourceClassifies() {
        return getService(DatasourceClassify.class)
                .getQuery()
                .eq("delFlag", Boolean.FALSE)
                .sortbyDesc("pos")
                .list();
    }

    // 获取二级目录列表
    private List<DatasourceType> getDatasourceTypes() {
        return getService(DatasourceType.class)
                .getQuery()
                .eq("delFlag", Boolean.FALSE)
                .sortbyDesc("pos")
                .list();
    }

    // 获取驱动数量统计
    private Map<String, Long> getDatasourceTypeCountMap() {
        return Optional.ofNullable(getQuery().list()).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy(DatasourceDriver::getDatasourceTypeId, Collectors.counting()));
    }

    /**
     * 新增驱动
     * @param datasourceDriver
     */
    @Transactional(rollbackFor = Exception.class)
    public DatasourceDriver save(DatasourceDriver datasourceDriver) {
        DatasourceType datasourceType = getService(DatasourceType.class).getById(datasourceDriver.getDatasourceTypeId());
        if (null == datasourceType) {
            throw new AppWarningException(Response.Code.Warning, "数据源类型不存在");
        }
        List<AppFile> appFiles = datasourceDriver.getAppFiles();
        if (null == appFiles) {
            throw new AppWarningException(Response.Code.Warning, "没有检测到上传驱动");
        }

        List<DatasourceDriver> drivers = getQuery().eq("datasourceTypeId", datasourceDriver.getDatasourceTypeId()).eq("driverVersion", datasourceDriver.getDriverVersion()).list();
        if (!CollectionUtils.isEmpty(drivers)) {
            throw new AppWarningException(Response.Code.Warning, "同一数据库类型的驱动版本不能重复");
        }

        String dirName = datasourceType.getDataType() + datasourceDriver.getDriverVersion();
        String fileIds = appFiles.stream().map(AppFile::getId).collect(Collectors.joining(";"));
        String fileNames = appFiles.stream().map(AppFile::getName).collect(Collectors.joining(";"));
        datasourceDriver.setDriverFileIds(fileIds);
        datasourceDriver.setDriverJarNames(fileNames);
        datasourceDriver.setId(GeneratorUtil.genCode());
        datasourceDriver.setDirName(dirName);

        // 1. 创建新驱动文件夹并拷贝模版
        mkdirAndCopyTemplate(datasourceDriver);
        // 2. 新增驱动版本
        return add(datasourceDriver.getId(), datasourceDriver);
    }

    private void mkdirAndCopyTemplate(DatasourceDriver datasourceDriver) {
        // 查询模版数据
        DatasourceDriver driverTemplate = getById(datasourceDriver.getDatasourceDriverId());
        if (null == datasourceDriver.getDatasourceDriverId() || null == driverTemplate) {
            throw new AppWarningException(Response.Code.Warning, "模版不存在");
        }
        // 驱动排序设置
        datasourceDriver.setPos(driverTemplate.getPos() + 1);
        String targetFilePath = databaseProperties.getPluginBasePath() + databaseProperties.getPluginPath() + File.separator + datasourceDriver.getDirName();
        String sourceFilePath = databaseProperties.getPluginBasePath() + databaseProperties.getPluginPath() + File.separator + driverTemplate.getDirName();
        if ("local".equals(appProperties.getActive())) {
            targetFilePath = databaseProperties.getLocalPluginPath() + File.separator + datasourceDriver.getDirName();
            sourceFilePath = databaseProperties.getLocalPluginPath() + File.separator + driverTemplate.getDirName();
        }
        // 下载上传上来的文件
        List<AppFile> appFiles = datasourceDriver.getAppFiles();

        // 1. 创建文件夹
        FileUtil.createDirectory(Paths.get(targetFilePath));
        // 设置要排除的文件（例如，排除所有.txt和config.json文件）
        Set<String> excludeFiles = new HashSet<>(StrUtil.split(driverTemplate.getDriverJarNames(), ";"));
        // 2. 拷贝到目标文件夹
        try {
            FileUtil.copyFilesToDirectory(Paths.get(sourceFilePath), Paths.get(targetFilePath), excludeFiles);
            // 上传的jar拷贝到目标文件夹
            for (AppFile appFile : appFiles) {
                JoyadataAppFile joyadataAppFile = fileJoyaFeignService.dowload(appFile.getId(), PublicConstants.MODULE);
                FileUtil.writeToFile(joyadataAppFile.getData(), targetFilePath + File.separator + appFile.getOriginalFilename());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new AppWarningException(Response.Code.Warning, "模版驱动拷贝失败");
        }

    }

    public Integer del(String id) {
        // 只能删除非系统默认的数据源，新增数据源以 驱动类型+版本作为文件夹名称
        DatasourceDriver datasourceDriver = getQuery()
                //.lazys("datasourceType")
                .eq("id", id)
                .eq("isDefault", 0)
                .one();
        if (null == datasourceDriver) {
            return 0;
        }
        String dirName = datasourceDriver.getDirName();
        String filePath = databaseProperties.getPluginBasePath() + databaseProperties.getPluginPath() + File.separator + dirName;
        if ("local".equals(appProperties.getActive())) {
            filePath = databaseProperties.getLocalPluginPath() + File.separator + dirName;
        }
        // 1. 删除驱动文件夹
        boolean isDel = FileSystemUtils.deleteRecursively(new File(filePath));
        // 2. 删除驱动数据
        return delete(datasourceDriver.getId(), datasourceDriver);
    }

    public Integer modify(DatasourceDriver datasourceDriver) {
        DatasourceType datasourceType = getService(DatasourceType.class).getById(datasourceDriver.getDatasourceTypeId());
        if (null == datasourceType) {
            throw new AppWarningException(Response.Code.Warning, "数据源类型不存在");
        }
        List<AppFile> appFiles = datasourceDriver.getAppFiles();
        if (null == appFiles) {
            throw new AppWarningException(Response.Code.Warning, "没有检测到上传驱动");
        }

        String fileIds = appFiles.stream().map(AppFile::getId).collect(Collectors.joining(";"));
        String fileNames = appFiles.stream().map(AppFile::getName).collect(Collectors.joining(";"));
        datasourceDriver.setDriverFileIds(fileIds);
        datasourceDriver.setDriverJarNames(fileNames);

        DatasourceDriver datasourceDriverOri = getById(datasourceDriver.getId());
        if (!datasourceDriverOri.getDriverVersion().equalsIgnoreCase(datasourceDriver.getDriverVersion())) {
            List<DatasourceDriver> drivers = getQuery().eq("datasourceTypeId", datasourceDriver.getDatasourceTypeId()).eq("driverVersion", datasourceDriver.getDriverVersion()).list();
            if (!CollectionUtils.isEmpty(drivers)) {
                throw new AppWarningException(Response.Code.Warning, "同一数据库类型的驱动版本不能重复");
            }
        }

        // 比对驱动是否发生变化
        List<String> oriDriverFileIds = StrUtil.split(datasourceDriverOri.getDriverFileIds(), ";");
        List<String> newDriverFileIds = StrUtil.split(datasourceDriver.getDriverFileIds(), ";");
        List<String> oriDriverJarNames = StrUtil.split(datasourceDriverOri.getDriverJarNames(), ";");
        if (!areListsEqual(newDriverFileIds, oriDriverFileIds)) {
            String targetFilePath = databaseProperties.getPluginBasePath() + databaseProperties.getPluginPath() + File.separator + datasourceDriver.getDirName();
            if ("local".equals(appProperties.getActive())) {
                targetFilePath = databaseProperties.getLocalPluginPath() + File.separator + datasourceDriver.getDirName();
            }
            // 删除旧驱动
            for (String oriDriverJarName : oriDriverJarNames) {
                FileSystemUtils.deleteRecursively(new File(targetFilePath + File.separator + oriDriverJarName));
            }

            // 添加新驱动
            try {
                for (AppFile appFile : appFiles) {
                    JoyadataAppFile joyadataAppFile = fileJoyaFeignService.dowload(appFile.getId(), PublicConstants.MODULE);
                    FileUtil.writeToFile(joyadataAppFile.getData(), targetFilePath + File.separator + appFile.getName());
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new AppWarningException(Response.Code.Warning, "上传驱动拷贝失败");
            }
        }
        return update(datasourceDriver.getId(), datasourceDriver);
    }

    public static boolean areListsEqual(List<String> list1, List<String> list2) {
        // 将 List 转换为 Set，这样可以忽略顺序和重复元素
        Set<String> set1 = new HashSet<>(list1);
        Set<String> set2 = new HashSet<>(list2);

        // 比较两个 Set 是否相等
        return set1.equals(set2);
    }
}
