package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.joyadata.cms.model.ProductEvent;
import com.joyadata.cms.model.SysDict;
import com.joyadata.csc.model.AlarmRecord;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.DatasourceAlertStrategy;
import com.joyadata.dsc.model.datasoure.vo.AlarmRecordVO;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.interfaces.IService;
import com.joyadata.model.BaseBean;
import com.joyadata.service.BaseServiceImpl;
import com.joyadata.tms.model.Product;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DatasourceAlertRecordService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/14 11:18
 * @Version 1.0
 **/
@Service
public class DatasourceAlertRecordService {

    private JoyaFeignService<AlarmRecord> alarmRecordJoyaFeignService = FeignFactory.make(AlarmRecord.class);
    private JoyaFeignService<SysDict> sysDictJoyaFeignService = FeignFactory.make(SysDict.class);
    @Autowired
    private BaseServiceImpl baseServiceImpl;

    public Integer getAlertRecordTotal(String keywords, String datasourceInfoId, String alarmStrategyId, String eventCode, String alertLevel) {
        String alarmStrategyIds = StrUtil.blankToDefault(alarmStrategyId, getAlarmStrategyIds(datasourceInfoId));
        IQueryWrapper<AlarmRecord> alarmRecordIQueryWrapper = buildAlarmRecordQueryWrapper(keywords, alarmStrategyIds, eventCode, alertLevel);
        return alarmRecordIQueryWrapper.total();
    }

    public List<AlarmRecordVO> getAlarmRecordList(String keywords, String datasourceInfoId, String alarmStrategyId, String eventCode, String alertLevel, Integer page, Integer pager) {
        String alarmStrategyIds = StrUtil.blankToDefault(alarmStrategyId, getAlarmStrategyIds(datasourceInfoId));
        IQueryWrapper<AlarmRecord> alarmRecordIQueryWrapper = buildAlarmRecordQueryWrapper(keywords, alarmStrategyIds, eventCode, alertLevel);
        alarmRecordIQueryWrapper.sortbyDesc("createTime");
        if (null != page && null != pager) {
            alarmRecordIQueryWrapper.page(page, pager);
        }

        List<AlarmRecord> alarmRecords = alarmRecordIQueryWrapper.list();
        List<AlarmRecordVO> voList = convertToAlarmRecordVOList(alarmRecords);
        return voList;
    }

    @NotNull
    private List<AlarmRecordVO> convertToAlarmRecordVOList(List<AlarmRecord> alarmRecords) {
        List<AlarmRecordVO> voList = new ArrayList<>();
        if (CollUtil.isNotEmpty(alarmRecords)) {
            Map<String, DatasourceAlert> strategyMap = getStrategyMap();

            Map<String, String> sysDicMap = getDicMap();

            Map<String, String> productEventMap = getEventMap();

            for (AlarmRecord alarmRecord : alarmRecords) {
                AlarmRecordVO vo = new AlarmRecordVO();
                BeanUtils.copyProperties(alarmRecord, vo);
                DatasourceAlert datasourceAlert = Optional.ofNullable(strategyMap.get(alarmRecord.getAlarmStrategyId())).orElse(new DatasourceAlert());
                vo.setDatasourceInfoId(datasourceAlert.getDatasourceInfoId());
                vo.setDatasourceInfoName(datasourceAlert.getDatasourceInfoName());
                vo.setEventName(productEventMap.get(alarmRecord.getEventCode()));
                vo.setNoticeWay(datasourceAlert.getNoticeWay());
                vo.setNoticeWayName(sysDicMap.get(datasourceAlert.getNoticeWay()));
                vo.setProjects(datasourceAlert.getProjects());
                vo.setUsers(datasourceAlert.getUsers());
                vo.setRoles(datasourceAlert.getRoles());
                voList.add(vo);
            }
        }
        return voList;
    }

    @NotNull
    private Map<String, String> getEventMap() {
        Map<String, String> productEventMap = new HashMap<>();
        Product product = ((DatasourceAlertService) baseServiceImpl.getService(DatasourceAlert.class)).getProduct("sourceMetadata");
        if (null != product) {
            List<ProductEvent> productEvents = ((DatasourceAlertService) baseServiceImpl.getService(DatasourceAlert.class)).getProductEvent(product.getId());
            if (CollUtil.isNotEmpty(productEvents)) {
                productEventMap = productEvents.stream().collect(Collectors.toMap(ProductEvent::getCode, ProductEvent::getName));
            }
        }
        return productEventMap;
    }

    @NotNull
    private Map<String, String> getDicMap() {
        Map<String, String> sysDicMap = new HashMap<>();
        List<SysDict> sysDicts = sysDictJoyaFeignService.getQuery()
                .withs("threshold_code")
                .eq("threshold_code", "noticeWay")
                .eq("status", true)
                .sortby("pos")
                .list();
        if (CollUtil.isNotEmpty(sysDicts)) {
            sysDicMap = sysDicts.stream().collect(Collectors.toMap(SysDict::getValue, SysDict::getLabel, (v1, v2) -> v1, HashMap::new));
        }
        return sysDicMap;
    }

    @NotNull
    private Map<String, DatasourceAlert> getStrategyMap() {
        Map<String, DatasourceAlert> strategyIdMap = new HashMap<>();
        IService<DatasourceAlert> datasourceAlertIService = baseServiceImpl.getService(DatasourceAlert.class);
        List<DatasourceAlert> datasourceAlerts = datasourceAlertIService.getQuery()
                .lazys("datasourceAlertEvents", "datasourceAlertStrategies")
                .withs("datasourceInfoName")
                .list();
        if (CollUtil.isNotEmpty(datasourceAlerts)) {
            for (DatasourceAlert datasourceAlert : datasourceAlerts) {
                List<DatasourceAlertStrategy> datasourceAlertStrategies = datasourceAlert.getDatasourceAlertStrategies();
                if (CollUtil.isNotEmpty(datasourceAlertStrategies)) {
                    for (DatasourceAlertStrategy datasourceAlertStrategy : datasourceAlertStrategies) {
                        strategyIdMap.put(datasourceAlertStrategy.getAlarmStrategyId(), datasourceAlert);
                    }
                }
            }
        }
        return strategyIdMap;
    }

    public IQueryWrapper<AlarmRecord> buildAlarmRecordQueryWrapper(String keywords, String alarmStrategyIds, String eventCode, String alertLevel) {
        IQueryWrapper<AlarmRecord> query = alarmRecordJoyaFeignService.getQuery();
        queryParam(query, "eventCode", eventCode, "EQ");
        queryParam(query, "alarmLevel", alertLevel, "EQ");
        queryParam(query, "title,alarmStrategyName", keywords, "SEARCHBY");
        queryParam(query, "alarmStrategyId", alarmStrategyIds, "IN");
        return query;
    }

    private String getAlarmStrategyIds(String datasourceInfoId) {
        IService<DatasourceAlertStrategy> service = baseServiceImpl.getService(DatasourceAlertStrategy.class);
        IQueryWrapper<DatasourceAlertStrategy> query = service.getQuery();
        String alarmStrategyIds = "未知";
        String datasourceAlertId = "未知";
        IService<DatasourceAlert> datasourceAlertIService = baseServiceImpl.getService(DatasourceAlert.class);
        if (StrUtil.isNotBlank(datasourceInfoId)) {
            DatasourceAlert datasourceAlert = datasourceAlertIService.getQuery().eq("datasourceInfoId", datasourceInfoId).one();
            if (null != datasourceAlert) {
                datasourceAlertId = datasourceAlert.getId();
            }
            queryParam(query, "datasourceAlertId", datasourceAlertId, "EQ");
        }

        List<DatasourceAlertStrategy> list = query.list();
        if (CollUtil.isNotEmpty(list)) {
            alarmStrategyIds = list.stream().map(DatasourceAlertStrategy::getAlarmStrategyId).collect(Collectors.joining(","));
        }
        return alarmStrategyIds;
    }

    private void queryParam(IQueryWrapper<? extends BaseBean> query, String paramName, String paramValue, String symbol) {
        if ("EQ".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.eq(paramName, paramValue);
            }
        } else if ("SEARCHBY".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.searchby(paramName, paramValue);
            }
        } else if ("SETIN".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.setin(paramName, paramValue);
            }
        } else if ("LIKE".equals(symbol)) {
            if (StrUtil.isNotBlank(paramValue)) {
                query.like(paramName, paramValue);
            }
        } else if ("IN".equals(symbol)) {
            List<String> ids = null;
            if (StrUtil.isNotBlank(paramValue)) {
                ids = StrUtil.split(paramValue, ",");
                query.in(paramName, ids);
            }
        } else if ("NOTIN".equals(symbol)) {
            List<String> ids = null;
            if (StrUtil.isNotBlank(paramValue)) {
                ids = StrUtil.split(paramValue, ",");
                query.notIn(paramName, ids);
            }
        }
    }
}
