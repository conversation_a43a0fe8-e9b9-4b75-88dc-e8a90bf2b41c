package com.joyadata.dsc.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.joyadata.cms.model.User;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.service.BaseServiceImpl;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 初始化数据
 */
@Slf4j
@Service
public class InitDatabaseService {
    /**
     * 注释行
     * -- 用户表
     */
    private static final Pattern EXPLAIN_PATTERN = Pattern.compile("--.*");
    /**
     * 空白行
     */
    private static final Pattern BLANK_PATTERN = Pattern.compile("\\s+$");
    //private static final Pattern BLANK_PATTERN = Pattern.compile("[\\r\\n]+$");
    /**
     * 回车换行
     */
    private static final Pattern ENTER_PATTERN = Pattern.compile("[\\r\\n|\\r|\\n]+");
    private static final Pattern INSERT_UPDATE_DELETE_PATTERN = Pattern.compile("^(insert|update|delete).*$", Pattern.CASE_INSENSITIVE);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private BaseServiceImpl baseServiceImpl;

    private JoyaFeignService<User> userJoyaFeignService = FeignFactory.make(User.class);

    public Map<String, Object> init(String tenantCode, Boolean write) {
        String workDir = baseServiceImpl.getWorkDir();
        String initSqlPath = workDir + "/config/sql/initDefault.sql";
        Map<String, Object> map = new HashMap<>();
        List<String> list = new ArrayList<>();
        map.put("flag", false);
        map.put("data", list);
        String systemToken = AuthUtil.getSystemToken(tenantCode);
        ThreadLocalUserUtil.setCode("token", systemToken);
        User user = userJoyaFeignService.getQuery()
                .eq("tenant_code", tenantCode)
                .eq("username", tenantCode + "_manager")
                .one();
        Optional.ofNullable(user).orElseThrow(() -> new AppErrorException("租户信息不存在！"));
        try {
            String initSql = FileUtil.readUtf8String(initSqlPath);
            Matcher explainMatcher = EXPLAIN_PATTERN.matcher(initSql);
            String initSql2 = explainMatcher.replaceAll("");
            List<String> sqlList = StrUtil.split(initSql2, ");");
            sqlList.forEach(sql -> {
                String trimSql = StrUtil.trim(sql) + ")";
                Matcher enterMatch = ENTER_PATTERN.matcher(trimSql);
                String replace = enterMatch.replaceAll(" ");
                Matcher sqlMatch = INSERT_UPDATE_DELETE_PATTERN.matcher(replace);
                if (sqlMatch.matches()) {
                    String sqlStr = sqlMatch.group();
                    String finalSql = sqlStr.replaceAll("'dsg", "'" + tenantCode)
                            .replaceAll("'xxx'", "'" + user.getId() + "'")
                            .replaceAll("'系统管理员'", "'" + user.getNickname() + "'");
                    log.info("初始化SQL: " + finalSql);
                    list.add(finalSql);
                    if (write) {
                        jdbcTemplate.execute(finalSql);
                        log.info("写入数据库");
                    }
                }
            });
            map.put("flag", true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return map;
    }
}
