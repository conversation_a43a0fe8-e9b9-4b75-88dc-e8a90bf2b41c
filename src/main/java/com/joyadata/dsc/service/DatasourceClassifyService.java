package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import com.joyadata.dsc.constants.Constants;
import com.joyadata.dsc.model.datasoure.ClassifyTree;
import com.joyadata.dsc.model.datasoure.DatasourceClassify;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.DatasourceType;
import com.joyadata.service.BaseService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据源图标列表
 */
@Service
public class DatasourceClassifyService extends BaseService<DatasourceClassify> {

    public List<ClassifyTree> tree() {
        List<ClassifyTree> trees = new ArrayList<>();
        List<ClassifyTree> types = new ArrayList<>();
        List<DatasourceClassify> datasourceClassifies = getQuery()
                .eq("delFlag", Boolean.FALSE)
                .ne("id", "999999")
                .withs("datasourceInfoSize","datasourceInfoSuccessSize")
                .list();
        List<DatasourceType> datasourceTypes = getService(DatasourceType.class)
                .getQuery()
                .list();
        List<DatasourceInfo> datasourceInfos = getService(DatasourceInfo.class)
                .getQuery()
                .list();
        for (DatasourceType datasourceType : datasourceTypes) {
            ClassifyTree classifyTree = new ClassifyTree();
            classifyTree.setId(datasourceType.getId());
            classifyTree.setPid(datasourceType.getDatasourceClassifyId());
            classifyTree.setName(datasourceType.getDataType());
            classifyTree.setCount(datasourceInfos != null ? (int) datasourceInfos.stream().filter(obj -> obj.getDatasourceTypeId().equals(datasourceType.getId())).count() : 0);
            classifyTree.setSuccessCount(datasourceInfos != null ? (int) datasourceInfos.stream().filter(obj -> obj.getDatasourceTypeId().equals(datasourceType.getId()) && obj.getStatus() == null ? obj.getStatus() == 1 : false).count() : 0);
            types.add(classifyTree);
        }
        if (CollUtil.isNotEmpty(datasourceClassifies)) {
            for (DatasourceClassify datasourceClassify : datasourceClassifies) {
                datasourceClassify.setDatasourceInfoSize(datasourceInfos != null ? (int) datasourceInfos.stream().filter(obj -> obj.getDatasourceClassifyId().equals(datasourceClassify.getId())).count() : 0);
                datasourceClassify.setDatasourceInfoSuccessSize(datasourceInfos != null ? (int) datasourceInfos.stream().filter(obj -> obj.getDatasourceClassifyId().equals(datasourceClassify.getId()) && obj.getStatus() == null ? obj.getStatus() == 1 : false).count() : 0);
                ClassifyTree classifyTree = getClassifyTree(datasourceClassify);
                trees.add(classifyTree);
            }
        }
        for (ClassifyTree tree : trees) {
            List<ClassifyTree> typeList = types.stream()
                    .filter(obj -> obj.getPid().equals(tree.getId())).sorted(new Comparator<ClassifyTree>() {
                        @Override
                        public int compare(ClassifyTree o1, ClassifyTree o2) {
                            return Character.compare(o1.getName().charAt(0), o2.getName().charAt(0));
                        }
                    }).collect(Collectors.toList());
            tree.setChildren(typeList);
        }
        return trees;
    }

    private static @NotNull ClassifyTree getClassifyTree(DatasourceClassify datasourceClassify) {
        ClassifyTree classifyTree = new ClassifyTree();
        classifyTree.setId(datasourceClassify.getId());
        classifyTree.setPid(Constants.TREE_PID_ID);
        classifyTree.setLevel(0);
        classifyTree.setName(datasourceClassify.getClassifyName());
        classifyTree.setClassifyCode(datasourceClassify.getClassifyCode());
        classifyTree.setCount(Optional.ofNullable(datasourceClassify.getDatasourceInfoSize()).orElse(0));
        classifyTree.setSuccessCount(Optional.ofNullable(datasourceClassify.getDatasourceInfoSuccessSize()).orElse(0));
        return classifyTree;
    }

    /**
     * 获取带图标信息的分类树
     * @return 带图标信息的分类树
     */
    public List<ClassifyTree> treeWithIcons() {
        List<ClassifyTree> trees = new ArrayList<>();
        List<ClassifyTree> types = new ArrayList<>();
        
        // 获取所有分类
        List<DatasourceClassify> datasourceClassifies = getQuery()
                .eq("delFlag", Boolean.FALSE)
                .withs("datasourceInfoSize","datasourceInfoSuccessSize")
                .list();
                
        // 获取所有类型（包含图标信息）
        List<DatasourceType> datasourceTypes = getService(DatasourceType.class)
                .getQuery()
                .eq("delFlag", Boolean.FALSE)
                .list();
                
        // 构建类型树节点
        for (DatasourceType datasourceType : datasourceTypes) {
            ClassifyTree classifyTree = new ClassifyTree();
            classifyTree.setId(datasourceType.getId());
            classifyTree.setPid(datasourceType.getDatasourceClassifyId());
            classifyTree.setName(datasourceType.getDataType());
            classifyTree.setCount(Optional.ofNullable(datasourceType.getDatasourceInfoSize()).orElse(0));
            classifyTree.setSuccessCount(Optional.ofNullable(datasourceType.getDatasourceInfoSuccessSize()).orElse(0));
            // 设置图标URL
            classifyTree.setIcon(datasourceType.getImgUrl());
            // 设置数据类型代码
            classifyTree.setDataTypeCode(datasourceType.getDataTypeCode());
            types.add(classifyTree);
        }
        
        // 构建分类树节点
        if (CollUtil.isNotEmpty(datasourceClassifies)) {
            for (DatasourceClassify datasourceClassify : datasourceClassifies) {
                ClassifyTree classifyTree = getClassifyTree(datasourceClassify);
                trees.add(classifyTree);
            }
        }
        
        // 构建树结构
        for (ClassifyTree tree : trees) {
            List<ClassifyTree> typeList = types.stream()
                    .filter(obj -> obj.getPid().equals(tree.getId())).sorted(Comparator.comparing(ClassifyTree::getName)).collect(Collectors.toList());
            // 按名称排序
            tree.setChildren(typeList);
        }
        
        return trees;
    }
}
