package com.joyadata.dsc.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.dsc.collector.MetadataCollector;
import com.joyadata.dsc.collector.MetadataCollectorFactory;
import com.joyadata.dsc.collector.util.MetadataCollectionManger;
import com.joyadata.dsc.enums.*;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.metadata.MetadataSelect;
import com.joyadata.dsc.model.metadata.MetadataTableCommonDetail;
import com.joyadata.dsc.model.metadata.MetadataTask;
import com.joyadata.dsc.model.metadata.MetadataTaskRecord;
import com.joyadata.dsc.model.metadata.dto.MetadataDbDTO;
import com.joyadata.dsc.model.metadata.dto.MetadataTaskDTO;
import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import com.joyadata.dsc.model.migration.dto.MataDataTaskTemplate;
import com.joyadata.dsc.utils.DatasourceUtil;
import com.joyadata.dsc.utils.IdUtils;
import com.joyadata.dsc.utils.MD5Util;
import com.joyadata.exception.AppErrorException;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.joyadata.feign.enums.RpcMethodName.add;

/**
 * MetadataTaskService类用于处理元数据任务的相关操作
 * 它继承自BaseService类，为MetadataTask实体提供数据访问和业务逻辑操作的封装
 */
@Service
@Slf4j
public class MetadataTaskService extends BaseService<MetadataTask> {
    // 用于存储每个采集记录的任务 key：采集记录id value：Future对象
    private final Map<String, MetadataTaskDTO> tasks = new ConcurrentHashMap<>();

    @Autowired
    MetadataTaskRecordService metadataTaskRecordService;
    @Autowired
    DatasourceUtil datasourceUtil;


    @Override
    public MetadataTask add(String id, MetadataTask bean) {
        if (CollUtil.isNotEmpty(bean.getMetadataSelect())) {
            for (MetadataSelect metadataSelectTable : bean.getMetadataSelect()) {
                metadataSelectTable.setMetadataTaskId(bean.getId());
                metadataSelectTable.setDatasourceInfoId(bean.getDatasourceInfoId());
                if (StringUtils.isNotEmpty(bean.getMetadataTaskRecordId())) {
                    metadataSelectTable.setMetadataTaskRecordId(bean.getMetadataTaskRecordId());
                } else {
                    metadataSelectTable.setMetadataTaskRecordId("0");
                }

            }
        }
        //判断是否默认配置
        if (SwitchStatus.ON.getCode() == bean.getIsDefault()) {
            bean.setMetadataTaskRecordId("0");
        }
        MetadataTask add = super.add(id, bean);
        return add;
    }

    public MetadataTask addDetail(MetadataTask bean) {

        //判断是否默认配置
        if (SwitchStatus.ON.getCode() == bean.getIsDefault()) {
            bean.setMetadataTaskRecordId("0");
        }
        MetadataTask add1 = this.add(bean);
        if (CollUtil.isNotEmpty(bean.getMetadataSelect())) {
            for (MetadataSelect metadataSelectTable : bean.getMetadataSelect()) {
                metadataSelectTable.setMetadataTaskId(add1.getId());
                metadataSelectTable.setDatasourceInfoId(bean.getDatasourceInfoId());
                if (StringUtils.isNotEmpty(bean.getMetadataTaskRecordId())) {
                    metadataSelectTable.setMetadataTaskRecordId(bean.getMetadataTaskRecordId());
                } else {
                    metadataSelectTable.setMetadataTaskRecordId("0");
                }

            }
        }
        if (Objects.nonNull(add1)) {
            List<MetadataSelect> metadataSelect = bean.getMetadataSelect();
            //保存MetadataSelects
            if (CollUtil.isNotEmpty(metadataSelect)) {
                getService(MetadataSelect.class).add(bean.getMetadataSelect());
            }
        }
        return add1;
    }


    /**
     * 根据数据源id获取采集配置
     *
     * @param datasourceId
     * @return
     */
    public MetadataTask getInfoByDsId(String datasourceId) {
        MetadataTask one = getService(MetadataTask.class)
                .getQuery()
                .eq("datasourceInfoId", datasourceId)
                .eq("isDefault", 0)
                .eq("metadataTaskRecordId", "0")
                .lazys("metadataSelect")
                .one();
        return one;
    }

    /**
     * 根据数据源id获取采集配置
     *
     * @param metadataTaskRecordId
     * @return
     */
    public MetadataTask getInfoByRecordId(String metadataTaskRecordId) {
        MetadataTask one = getService(MetadataTask.class)
                .getQuery()
                .eq("metadataTaskRecordId", metadataTaskRecordId)
                .lazys("metadataSelect")
                .one();
        return one;
    }

    @Override
    public Integer update(String id, MetadataTask bean) {
        // 修改主表
        //判断是否默认配置
        if (SwitchStatus.ON.getCode() == bean.getIsDefault()) {
            bean.setMetadataTaskRecordId("0");
        }
        Integer update = super.updateBy(new EqCondition("id", id), bean, true);
        // 删除之前的配置信息 并且MetadataTaskRecordId为0的数据
        EqCondition delete = new EqCondition("metadataTaskId", bean.getId());
        List<WhereCondition> whereConditions = new ArrayList<>();
        whereConditions.add(delete);
        getService(MetadataSelect.class).deleteBy(whereConditions);
        if (CollUtil.isNotEmpty(bean.getMetadataSelect())) {
            for (MetadataSelect metadataSelectTable : bean.getMetadataSelect()) {
                metadataSelectTable.setMetadataTaskId(bean.getId());
                metadataSelectTable.setDatasourceInfoId(bean.getDatasourceInfoId());
                if (StringUtils.isNotEmpty(bean.getMetadataTaskRecordId())) {
                    metadataSelectTable.setMetadataTaskRecordId(bean.getMetadataTaskRecordId());
                } else {
                    metadataSelectTable.setMetadataTaskRecordId("0");
                }
            }
            // 保存配置信息
            getService(MetadataSelect.class).add(bean.getMetadataSelect());
        }
        return update;
    }

    /**
     * 根据数据源id 和defalt 项修改任务配置
     *
     */
    public Integer updateByDefault(String datasourceId, MetadataTask bean) {
        List<WhereCondition> whereConditions=new ArrayList<>();
        whereConditions.add(new EqCondition("datasourceInfoId", datasourceId));
        whereConditions.add(new EqCondition("isDefault", SwitchStatus.ON.getCode()));
        whereConditions.add(new EqCondition("metadataTaskRecordId", "0"));

        Integer integer = getService(MetadataTask.class)
                .updateBy(whereConditions, bean, true);
        // 删除之前的配置信息 并且MetadataTaskRecordId为0的数据
        EqCondition delete = new EqCondition("metadataTaskId", bean.getId());
        List<WhereCondition> conditions = new ArrayList<>();
        whereConditions.add(delete);
        getService(MetadataSelect.class).deleteBy(conditions);
        if (CollUtil.isNotEmpty(bean.getMetadataSelect())) {
            for (MetadataSelect metadataSelectTable : bean.getMetadataSelect()) {
                metadataSelectTable.setMetadataTaskId(bean.getId());
                metadataSelectTable.setDatasourceInfoId(bean.getDatasourceInfoId());
                if (StringUtils.isNotEmpty(bean.getMetadataTaskRecordId())) {
                    metadataSelectTable.setMetadataTaskRecordId(bean.getMetadataTaskRecordId());
                } else {
                    metadataSelectTable.setMetadataTaskRecordId("0");
                }
            }
            // 保存配置信息
            getService(MetadataSelect.class).add(bean.getMetadataSelect());
        }
        return integer;
    }

    /**
     * 获取DatasourceDTO datasourceDTO 对象
     */
    public DatasourceDTO getDatasourceDTO(String datasourceId) {
        DatasourceDTO datasourceDTO = datasourceUtil.getDatasourceDTO(datasourceId);
        return datasourceDTO;
    }

    /**
     * 获取指定数据源信息ID下的所有数据库
     *
     * @param datasourceId 数据源信息的ID，用于查询特定的数据源
     * @return 返回一个MetadataNodeVO对象列表，包含了数据源中的所有数据库信息
     */
    public List<MetadataNodeVO> getAllDbsNode(String datasourceId) {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        //todo  根据数据源id获取数据源配置
        DatasourceDTO datasourceDTO = getDatasourceDTO(datasourceId);
        //判断数据源对象是否为null
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", datasourceId);
        }
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceDTO.getDataType().toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        if (Objects.isNull(collector)) {
            throw new AppErrorException("数据源类型暂不支持获取库表" + datasourceDTO.getDataType());
        }
        // 使用元数据收集器获取所有数据库，并返回结果
        //捕获异常
        List<MetadataNodeVO> metadataNodeVOS = new ArrayList<>();
        try {
            metadataNodeVOS = collector.getAllDbsNode(datasourceId, datasourceDTO);
        } catch (Exception e) {
            log.error("获取库表失败:{}", e.getMessage());
        }
        return metadataNodeVOS;
    }

    /**
     * 获取文件指定path下得路径
     */
    public List<MetadataNodeVO> getAllFileNode(String datasourceId, String path) {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        //todo  根据数据源id获取数据源配置
        DatasourceDTO datasourceDTO = getDatasourceDTO(datasourceId);
        //判断数据源对象是否为null
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", datasourceId);
        }

        List<MetadataNodeVO> metadataNodeVOS = new ArrayList<>();
        List<String> allPaths = new ArrayList<>();
        String path1 = datasourceDTO.getPath();
        if (StringUtils.isNotEmpty(path1)) {
            allPaths.add(path1);
        } else {
            if (StringUtils.isEmpty(path)) {
                datasourceDTO.setPath("/");
                allPaths = (DatasourceUtils.getAllDbs(datasourceDTO));
            } else {
                datasourceDTO.setPath(path);
                allPaths = DatasourceUtils.getAllDbs(datasourceDTO);
            }
        }
        for (String allDb : allPaths) {
            MetadataNodeVO metadataCalalogVO = new MetadataNodeVO();
            metadataCalalogVO.setDatasourceInfoId(datasourceId);
            metadataCalalogVO.setName(allDb);
            metadataCalalogVO.setCnName(MetadataTaskObjectEnum.FOLDER.getChineseName());
            String uuid = MD5Util.resourceToMD5(datasourceId, allDb, "", allDb, MetadataTaskObjectEnum.FOLDER.getType(), null);
            metadataCalalogVO.setUuid(uuid);
            metadataCalalogVO.setType(MetadataTaskObjectEnum.FOLDER.getType());
            metadataCalalogVO.setLevel("1");
            metadataNodeVOS.add(metadataCalalogVO);
        }
        return metadataNodeVOS;
    }

    /**
     * 获取指定数据源信息ID下的指定库下得所有模式
     *
     * @param datasourceId 数据源信息的ID，用于查询特定的数据源
     * @return 返回一个MetadataNodeVO对象列表，包含了数据源中的所有数据库信息
     */
    public List<MetadataNodeVO> getAllSchemaNode(String datasourceId, String dbName) {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        //todo  根据数据源id获取数据源配置
        DatasourceDTO datasourceDTO = getDatasourceDTO(datasourceId);
        //判断数据源对象是否为null
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", datasourceId);
        }
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceDTO.getDataType().toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        // 使用元数据收集器获取所有数据库，并返回结果
        datasourceDTO.setDbName(dbName);
        List<MetadataNodeVO> metadataNodeVOS = collector.getAllSchemaNode(datasourceId, datasourceDTO);
        return metadataNodeVOS;
    }

    /**
     * 获取表或者视图
     *
     * @param dto
     * @return
     */
    public List<MetadataNodeVO> getTableNodesByDb(MetadataDbDTO dto) {
        //todo  根据数据源id获取数据源配置
        DatasourceDTO datasourceDTO = getDatasourceDTO(dto.getDatasourceInfoId());
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", dto.getDatasourceInfoId());
        }
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceDTO.getDataType().toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        // 使用元数据收集器获取所有数据库，并返回结果
        List<MetadataNodeVO> nodesByDb = collector.getTableNodesByDb(dto.getDatasourceInfoId(), dto, datasourceDTO);
        return nodesByDb;
    }

    public List<MetadataNodeVO> getCommonNode(String datasourceinfoId) {
        DatasourceDTO datasourceDTO = null;
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", datasourceinfoId);
        }
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceDTO.getDataType().toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        // 使用元数据收集器获取所有数据库，并返回结果
        List<MetadataNodeVO> commonNode = collector.getCommonNode(IdUtils.simpleUUID(), "1", "", "");
        return commonNode;
    }

    /**
     * 获取索引
     *
     * @param dto
     * @return
     */
    public List<MetadataNodeVO> getIndexNodesByDb(MetadataDbDTO dto) {
        //todo  根据数据源id获取数据源配置
        DatasourceDTO datasourceDTO = getDatasourceDTO(dto.getDatasourceInfoId());
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", dto.getDatasourceInfoId());
        }
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceDTO.getDataType().toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        // 使用元数据收集器获取所有数据库，并返回结果
        List<MetadataNodeVO> nodesByDb = collector.getIndexNodesByDb(dto.getDatasourceInfoId(), dto, datasourceDTO);
        return nodesByDb;
    }

    /**
     * 获取函数或者存储过程
     *
     * @param dto
     * @return
     */
    public List<MetadataNodeVO> getFunctionNodesByDb(MetadataDbDTO dto) {
        //todo  根据数据源id获取数据源配置
        DatasourceDTO datasourceDTO = getDatasourceDTO(dto.getDatasourceInfoId());
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", dto.getDatasourceInfoId());
        }
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceDTO.getDataType().toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        // 使用元数据收集器获取所有数据库，并返回结果
        List<MetadataNodeVO> nodesByDb = collector.getFunctionNodesByDb(dto.getDatasourceInfoId(), dto, datasourceDTO);
        return nodesByDb;
    }


    //启动任务
    public void startTask(MetadataTask task) {
        //判断该任务是否存在运行中的采集记录
        EqCondition metadataTaskId = new EqCondition("metadataTaskId", task.getId());
        EqCondition datasourceInfoId = new EqCondition("datasourceInfoId", task.getDatasourceInfoId());
        EqCondition status = new EqCondition("status", MetadataCollectionStatusEnum.RUNNING.getCode());
        List<WhereCondition> conditions = new ArrayList<>();
        conditions.add(metadataTaskId);
        conditions.add(status);
        conditions.add(datasourceInfoId);
        List<MetadataTaskRecord> list = metadataTaskRecordService.getList(conditions);

        if (CollUtil.isNotEmpty(list)) {
            throw new AppErrorException("此任务正在运行中");
        }
        //todo  根据数据源id获取数据源配置
        DatasourceDTO datasourceDTO = getDatasourceDTO(task.getDatasourceInfoId());
        // 获取数据源类型名称，用于后续确定数据源的类型
        String datasourceTypeName = datasourceDTO.getDataType();
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceTypeName.toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        //如果collector为空 则不支持此数据源类型
        if (collector == null) {
            throw new AppErrorException("数据源类型暂不支持采集" + datasourceTypeName);
        }
        //创建任务记录对象
        String metadataTaskRecordId = createMetadataTaskRecord(task, datasourceTypeName);
        MetadataTaskDTO taskDTO = new MetadataTaskDTO(metadataTaskRecordId);
        tasks.put(metadataTaskRecordId, taskDTO);
        MetadataCollectionManger metadataCollectionManger = new MetadataCollectionManger(collector, task.getDatasourceInfoId(), task, metadataTaskRecordId, metadataTaskRecordService, taskDTO);
        //执行任务
        metadataCollectionManger.runTask();


    }

    //创建采集记录对象，并保存记录
    private String createMetadataTaskRecord(MetadataTask task, String datasourceTypeName) {
        MetadataTaskRecord metadataTaskRecord = new MetadataTaskRecord();
        //采集记录id
        String metadataTaskRecordId = IdUtils.simpleUUID();
        String metadataTaskId = IdUtils.simpleUUID();
        metadataTaskRecord.setId(metadataTaskRecordId);
        metadataTaskRecord.setMetadataTaskId(task.getId());
        metadataTaskRecord.setStatus(MetadataCollectionStatusEnum.RUNNING.getCode());
        metadataTaskRecord.setTaskType(task.getTaskType());
        metadataTaskRecord.setDatasourceInfoId(task.getDatasourceInfoId());
        metadataTaskRecord.setDatasourceType(datasourceTypeName);
        metadataTaskRecord.setCollectionOperator(AuthUtil.getCurrentUser().getUsername());
        metadataTaskRecord.setMetadataTaskId(metadataTaskId);
        metadataTaskRecord.setStartTime(new Date());
        metadataTaskRecordService.add(metadataTaskRecord);
        //保存此次执行的配置记录
        MetadataTask detail = new MetadataTask();
        BeanUtil.copyProperties(task, detail);
        detail.setIsDefault(SwitchStatus.OFF.getCode());

        detail.setId(metadataTaskId);
        detail.setMetadataTaskRecordId(metadataTaskRecordId);
        addDetail(detail);
        return metadataTaskRecordId;
    }

    public String stopTask(String metadataTaskRecordId) {
        MetadataTaskDTO taskDTO = tasks.get(metadataTaskRecordId);
        if (Objects.nonNull(taskDTO) && taskDTO.isRunning()) {
            taskDTO.setRunning(false);
        }
        //将记录状态改为中断
        MetadataTaskRecord metadataTaskRecord = new MetadataTaskRecord();
        metadataTaskRecord.setId(metadataTaskRecordId);
        metadataTaskRecord.setStatus(MetadataCollectionStatusEnum.INTERRUPTED.getCode());
        metadataTaskRecordService.update(metadataTaskRecordId, metadataTaskRecord);
        return "TaskRecord- " + metadataTaskRecordId + " stop initiated";
    }

    //启动立即采集任务
    public void startFailedTask(MetadataTask task) {
        //全量
        task.setTaskWay(MetadataTaskWayEnum.ALL.getCode());
        //库表采集
        task.setAcquisitionScope(MetadataCollectionRangeEnum.TABLE_SELECTION.getCode());
        //手动
        task.setTaskType(SwitchStatus.ON.getCode());
        //不是默认采集任务
        task.setIsDefault(SwitchStatus.OFF.getCode());
        //定时关闭
        task.setIsTiming(SwitchStatus.OFF.getCode());
        List<MetadataTableCommonDetail> metadataTableCommonDetails = task.getMetadataTableCommonDetails();
        //指定路径
        String filePathSpecified = "";
        //根路径
        String filePath = task.getFilePath();
        //如果不为空则说明有指定记录
        if (CollUtil.isNotEmpty(metadataTableCommonDetails)) {
            for (MetadataTableCommonDetail metadataTableCommonDetail : metadataTableCommonDetails) {
                filePathSpecified += metadataTableCommonDetail.getFilePath() + ",";
                filePath = metadataTableCommonDetail.getDbName();
            }
            //filePathSpecified 去掉最后一个逗号
            filePathSpecified = filePathSpecified.substring(0, filePathSpecified.length() - 1);
            task.setFilePath(filePath);
            task.setFilePathSpecified(filePathSpecified);
            //默认不采集子集
            task.setIsRecursion(SwitchStatus.OFF.getCode());
            //采集存储层级 默认3层
            task.setMaxLevel(3);
        }


        startTask(task);
    }

    //快速选表：判断表是否存在

    /**
     * 修复为判断库或者 库、模式是否存在
     *
     * @param task
     * @return
     */
    public List<String> isTablesExists(MetadataTask task) {
        List<String> notExistTable = new ArrayList<>();
        String selectTable = task.getSelectTable();
        if (StringUtils.isEmpty(selectTable)) {
            return notExistTable;
        }
        //构建dsx 数据源对象
        DatasourceDTO datasourceDTO = getDatasourceDTO(task.getDatasourceInfoId());
        //获取所有库或者模式
        List<MetadataNodeVO> allDbsNode = getAllDbsNode(task.getDatasourceInfoId());
        //若为空，则直接返回
        if (CollUtil.isEmpty(allDbsNode)) {
            return notExistTable;
        }
        //将查询值进行分割，并生成list
        List<String> tableNames = StrUtil.split(selectTable, ",");
        for (String tableName : tableNames) {
            Map<String, MetadataNodeVO> map = allDbsNode.stream().collect(Collectors.toMap(MetadataNodeVO::getDbName, Function.identity()));
            //是否有模式
            if (DatasourceTypeDatabase.schemaDataTypeList().contains(datasourceDTO.getDataType().toLowerCase())) {
                //获取库名：从tableName截取第一个
                String dbName = StrUtil.subBefore(tableName, ".", false);
                if (!map.containsKey(dbName)) {
                    notExistTable.add(tableName);
                    continue;
                }
                //获取模式名：从tableName截取第2个
                String schemaName = StrUtil.subAfter(tableName, ".", false);
                if (map.get(dbName).getChildrens() == null) {
                    notExistTable.add(schemaName);
                    continue;
                }
                //获取模式集合
                List<MetadataNodeVO> childrens = map.get(dbName).getChildrens();
                List<String> schemas = childrens.stream().map(MetadataNodeVO::getSchemaName).collect(Collectors.toList());
                if (!schemas.contains(schemaName)) {
                    notExistTable.add(schemaName);

                }
            } else {
                String dbName = StrUtil.subBefore(tableName, ".", false);
                //将 allDbsNode 以dbName为key，value为MetadataNodeVO的list 得map
                if (!map.containsKey(dbName)) {
                    notExistTable.add(tableName);
                }
            }
        }
        return notExistTable;
    }

    public void deleteByTaskRecordId(String taskRecordId) {
        EqCondition delete = new EqCondition("metadataTaskRecordId", taskRecordId);
        this.deleteBy(delete);
    }

    /**
     * 获取数据源的默认配置信息
     */
    public List<MataDataTaskTemplate> getDefaultTask(String datasourceInfoId, String datasourceName) {
        List<MataDataTaskTemplate> mataDataTaskTemplates = new ArrayList<>();
        //获取数据源得默认配置任务
        MetadataTask infoByDsId = getInfoByDsId(datasourceInfoId);
        if (Objects.isNull(infoByDsId)) {
            return mataDataTaskTemplates;
        }

        //获取采集方式
        Integer taskWay = infoByDsId.getTaskWay();

        //获取选择范围
        Integer acquisitionScope = infoByDsId.getAcquisitionScope();
        if (acquisitionScope == MetadataCollectionRangeEnum.DATABASE_SELECTION.getCode()) {
            //获取到库资源
            List<MetadataSelect> metadataSelectList = infoByDsId.getMetadataSelect();
            for (MetadataSelect metadataSelect : metadataSelectList) {
                MataDataTaskTemplate mataDataTaskTemplate = new MataDataTaskTemplate();
                mataDataTaskTemplate.setDatasourceName(datasourceName);
                mataDataTaskTemplate.setAcquisitionScope(MetadataCollectionRangeEnum.DATABASE_SELECTION.getDescription());
                mataDataTaskTemplate.setTaskObject(infoByDsId.getTaskObject());
                mataDataTaskTemplate.setDbName(metadataSelect.getName());
                mataDataTaskTemplate.setSchemaName(metadataSelect.getSchemaName());
                mataDataTaskTemplate.setType(metadataSelect.getType());
                mataDataTaskTemplate.setBlacklist(infoByDsId.getBlacklist());
                mataDataTaskTemplate.setWhitelist(infoByDsId.getWhitelist());
                MetadataTaskWayEnum byType = MetadataTaskWayEnum.getByType(taskWay);
                mataDataTaskTemplate.setTaskWay(byType.getChineseName());
                mataDataTaskTemplates.add(mataDataTaskTemplate);
            }
            return mataDataTaskTemplates;
        }
        //表选择
        if (acquisitionScope == MetadataCollectionRangeEnum.TABLE_SELECTION.getCode()) {
            List<MetadataSelect> metadataSelectList = infoByDsId.getMetadataSelect();
            for (MetadataSelect metadataSelect : metadataSelectList) {
                MataDataTaskTemplate mataDataTaskTemplate = new MataDataTaskTemplate();
                mataDataTaskTemplate.setDatasourceName(datasourceName);
                mataDataTaskTemplate.setAcquisitionScope(MetadataCollectionRangeEnum.TABLE_SELECTION.getDescription());
                mataDataTaskTemplate.setDbName(metadataSelect.getDbName());
                mataDataTaskTemplate.setSchemaName(metadataSelect.getSchemaName());
                mataDataTaskTemplate.setTableName(metadataSelect.getName());
                mataDataTaskTemplate.setIndexTableName(metadataSelect.getTableName());
                mataDataTaskTemplate.setType(metadataSelect.getType());
                MetadataTaskWayEnum byType = MetadataTaskWayEnum.getByType(taskWay);
                mataDataTaskTemplate.setTaskWay(byType.getChineseName());
                mataDataTaskTemplates.add(mataDataTaskTemplate);
            }
            return mataDataTaskTemplates;
        }
        //快速选表

        if (acquisitionScope == MetadataCollectionRangeEnum.QUICK_SELECT_TABLE.getCode()) {
            String selectTable = infoByDsId.getSelectTable();
            if (StringUtils.isEmpty(selectTable)) {
                return mataDataTaskTemplates;
            }
            String[] split = selectTable.split(",");
            for (String table : split) {
                String[] split1 = table.split("\\.");
                String dbName = split1[0];
                //如果split1 长度大于2 2个数据是模式 3个是表名
                String schemaName = null;
                if (split1.length > 2) {
                    schemaName = split1[1];
                }
                String tableName = split1[split1.length - 1];
                MataDataTaskTemplate mataDataTaskTemplate = new MataDataTaskTemplate();
                mataDataTaskTemplate.setDatasourceName(datasourceName);
                mataDataTaskTemplate.setAcquisitionScope(MetadataCollectionRangeEnum.QUICK_SELECT_TABLE.getDescription());
                mataDataTaskTemplate.setTaskObject(infoByDsId.getTaskObject());
                mataDataTaskTemplate.setDbName(dbName);
                mataDataTaskTemplate.setSchemaName(schemaName);
                mataDataTaskTemplate.setTableName(tableName);
                mataDataTaskTemplates.add(mataDataTaskTemplate);
                return mataDataTaskTemplates;
            }
            //如果filePath  不为空这是文件类型
            if (StringUtils.isNotEmpty(infoByDsId.getFilePath())) {
                MataDataTaskTemplate mataDataTaskTemplate = new MataDataTaskTemplate();
                mataDataTaskTemplate.setDatasourceName(datasourceName);
                mataDataTaskTemplate.setTaskObject(MetadataTaskObjectEnum.FILE.getType());
                mataDataTaskTemplate.setFilePath(infoByDsId.getFilePath());
                mataDataTaskTemplate.setFilePathSpecified(infoByDsId.getFilePathSpecified());
                mataDataTaskTemplate.setMaxLevel(infoByDsId.getMaxLevel());
                mataDataTaskTemplate.setIsRecursion(infoByDsId.getIsRecursion());
                mataDataTaskTemplate.setBlacklist(infoByDsId.getBlacklist());
                mataDataTaskTemplate.setWhitelist(infoByDsId.getWhitelist());
                mataDataTaskTemplates.add(mataDataTaskTemplate);
                return mataDataTaskTemplates;
            }
        }
        return mataDataTaskTemplates;
    }

}
