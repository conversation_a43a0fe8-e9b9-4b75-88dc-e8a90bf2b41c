package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.joyadata.cms.model.Dept;
import com.joyadata.cms.model.User;
import com.joyadata.dsc.enums.BusinessEnum;
import com.joyadata.dsc.enums.BusinessStatusEnum;
import com.joyadata.dsc.feign.CmsFeignService;
import com.joyadata.dsc.model.business.BusinessSystem;
import com.joyadata.dsc.model.business.BusinessSystemImportErrorVO;
import com.joyadata.dsc.model.business.BusinessSystemImportVO;
import com.joyadata.dsc.model.business.BusinessSystemQuery;
import com.joyadata.dsc.model.migration.dto.BusinessSystemTemplate;
import com.joyadata.dsc.utils.excel.ExcelBigNumberConvert;
import com.joyadata.dsc.utils.excel.SheetForBusinessImportListener;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.sql.*;
import com.joyadata.service.BaseService;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/2
 */
@Service
@Slf4j
public class BusinessSystemService extends BaseService<BusinessSystem> {

    @Autowired
    private HttpRequestFeignService httpRequestFeignService;
    @Autowired
    private CmsFeignService cmsFeignService;


    //从yml 文件获取系统等级配置
    @Value("${dsh_system_security_level_url}")
    private String dsh_system_security_level_url;

    /**
     * 判断业务系统名称是否重复
     */
    public JSONObject validBusinessSystemName(BusinessSystem entity) {
        //校验同级目录的name是否重复
        IQueryWrapper<BusinessSystem> query = getService(BusinessSystem.class).getQuery()
                //相同名称的
                .eq("businessName", entity.getBusinessName());
        if (StringUtils.isNotEmpty(entity.getId())) {
            //如果是修改，则需要排除自己本身
            query.ne("id", entity.getId());
        }
        Integer total = query.eq("delFlag", Boolean.FALSE).total();
        JSONObject jsonObject = new JSONObject();
        String msg = "";
        if (total > 0) {
            jsonObject.put("flag", 0);
            msg = "业务系统名称已存在：" + entity.getBusinessName() + ",请更改名称";
            jsonObject.put("msg", msg);
            return jsonObject;
        }
        //是否删除

        IQueryWrapper<BusinessSystem> queryTrue = getService(BusinessSystem.class).getQuery()
                //相同名称的
                .eq("businessName", entity.getBusinessName());
        if (StringUtils.isNotEmpty(entity.getId())) {
            //如果是修改，则需要排除自己本身
            queryTrue.ne("id", entity.getId());
        }
        Integer delFlag = queryTrue.eq("delFlag", Boolean.TRUE).total();
        if (delFlag > 0) {
            jsonObject.put("flag", 1);
            msg = "业务系统已被删除：" + entity.getBusinessName() + ",可请联系管理员从数据恢复处恢复业务系统";
            jsonObject.put("msg", msg);
            return jsonObject;
        }
        return jsonObject;
    }

    /**
     * 获取业务系统列表 但是不含所传业务系统id
     *
     * @return
     */
    public List<BusinessSystem> getBusinessSystemList(String id) {
        List<BusinessSystem> businessSystems = getService(BusinessSystem.class).getQuery().eq("delFlag", Boolean.FALSE).list();
        List<BusinessSystem> businessSystemList = new ArrayList<>();
        for (BusinessSystem businessSystem : businessSystems) {
            if (!businessSystem.getId().equals(id)) {
                businessSystemList.add(businessSystem);
            }
        }
        return businessSystemList;
    }

    @Override
    public List<BusinessSystem> getList(List<ConditionGroup> conditionGroupList, List<ConditionGroup> orConditionGroupList, GroupByCondition groupByCondition, QueryFilter queryFilter, Integer page, Integer pager) {
        List<BusinessSystem> list = super.getList(conditionGroupList, orConditionGroupList, groupByCondition, queryFilter, page, pager);
        //获取用户
        if (CollUtil.isNotEmpty(list)) {
            Map<String, User> userMap = getUserMap(false);
            for (BusinessSystem businessSystem : list) {
                //处理负责人名称
                if (Objects.nonNull(userMap) && userMap.containsKey(businessSystem.getManager())) {
                    User user = userMap.get(businessSystem.getManager());
                    businessSystem.setManagerName(user.getNickname() + "(" + user.getUsername() + ")");
                }
            }
        }
        return list;
    }

    @Override
    public Integer delete(String id, BusinessSystem bean) {
        BusinessSystem businessSystem = new BusinessSystem();
        businessSystem.setId(id);
        businessSystem.setDelFlag(Boolean.TRUE);
        Integer update = update(businessSystem);
        updateBusinessId(id);
        return update;
    }

    public List<BusinessSystem> batchDelete( List<String> ids) {
        List<BusinessSystem> businessSystems = new ArrayList<>();
        if (CollUtil.isNotEmpty(ids)) {
            for (String id : ids) {
                //查询此id下是否挂载数据源，如果有则不允许删除
//                List<BusinessSystem> id1 = getService(BusinessSystem.class).getQuery().eq("id", id).list();
                BusinessSystem businessSystem = getById(id);
                if (CollUtil.isNotEmpty(new ArrayList<>())) {
                    businessSystems.add(businessSystem);
                } else {
                    delete(id, businessSystem);
                }
            }
        }
        return businessSystems;
    }

    @Override
    public BusinessSystem add(String id, BusinessSystem bean) {
        if (null != id) {
            bean.setId(id);
        }
        bean.setUpdateBy(bean.getCreateBy());
        validEntityBeforeSave(bean, true);
        return super.add(bean);
    }

    @Override
    public Integer update(String id, BusinessSystem bean) {
        BusinessSystem businessSystem = new BusinessSystem();
        businessSystem.setId(id);
        businessSystem.setUpdateBy(bean.getUpdateBy());
        validEntityBeforeSave(bean, false);
        return super.update(id, bean);
    }

    /**
     * 保存或者编辑前的数据校验
     */
    private void validEntityBeforeSave(BusinessSystem entity, boolean insertFlag) {
        //校验同级目录的name是否重复
        IQueryWrapper<BusinessSystem> query = getService(BusinessSystem.class).getQuery()
                //相同名称的
                .eq("businessName", entity.getBusinessName());
        if (!insertFlag) {
            //如果是修改，则需要排除自己本身
            query.ne("id", entity.getId());
        }

        //判断数据delflag为false状态提示业务系统名重复 为true 提示业务系统已被删除
        query.eq("delFlag", false);
        List<BusinessSystem> list = query.list();
        if (CollectionUtil.isNotEmpty(list)) {
            //判断数据delflag为false状态提示业务系统名重复 为true 提示业务系统已被删除
            throw new AppErrorException("业务系统名称已存在：" + entity.getBusinessName() + ",请更改名称");
        }
        //校验同级目录的name是否重复
        IQueryWrapper<BusinessSystem> query1 = getService(BusinessSystem.class).getQuery()
                //相同名称的
                .eq("businessName", entity.getBusinessName());
        if (!insertFlag) {
            //如果是修改，则需要排除自己本身
            query1.ne("id", entity.getId());
        }
        query1.eq("delFlag", true);
        List<BusinessSystem> list1 = query1.list();
        if (CollectionUtil.isNotEmpty(list1)) {
            throw new AppErrorException("业务系统已被删除：" + entity.getBusinessName() + ",可请联系管理员从数据恢复处恢复业务系统");
        }
    }

    public List<BusinessSystem> getListByDel(Boolean del) {
        List<BusinessSystem> delFlag = getService(BusinessSystem.class).getQuery()
                //相同名称的
                .eq("delFlag", del).list();
        return delFlag;

    }

    /**
     * 把挂载到当前业务系统的数据源挂载到 未归属 业务分类下
     *
     * @param id
     */
    public void updateBusinessId(String id) {
//        // 查询业务系统包含数据源的数据
//        List<DatasourceInfo> datasourceInfos = getService(DatasourceInfo.class)
//                .getQuery()
//                .in("businessSystemId", id)
//                .list();
//        if (CollUtil.isNotEmpty(datasourceInfos)) {
//            for (DatasourceInfo datasourceInfo : datasourceInfos) {
//                DatasourceInfo updateModel = new DatasourceInfo();
//                updateModel.setId(datasourceInfo.getId());
//                updateModel.setBusinessSystemId(Constants.UNOWNED_BUSINESS_ID);
//                updateModel.setLastModificationTime(new Date());
//                getService(DatasourceInfo.class)
//                        .update(datasourceInfo.getId(), updateModel);
//            }
//        }
    }

    /**
     * 导出业务系统信息模板
     * 通过HTTP响应输出Excel格式的文件，用于导入业务系统信息
     * 该方法配置响应头，以确保文件正确下载，并使用EasyExcel库生成Excel文件
     *
     * @param response HttpServletResponse对象，用于设置响应头和输出流
     */
    public void exportTemplate(HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            // 初始化HTTP响应，确保文件下载时的响应头正确设置
            response.reset();
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/vnd.ms-excel");

            // 创建ExcelWriter对象，用于写入Excel文件
            // 指定要写入的实体类类型，配置自动关闭流、自动适配列宽、大数值自动转换以及Excel文件类型
            excelWriter = EasyExcel.write(response.getOutputStream(), BusinessSystemImportVO.class)
                    .autoCloseStream(false)
                    // 自动适配
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    // 大数值自动转换 防止失真
                    .registerConverter(new ExcelBigNumberConvert())
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();

            // 创建WriteSheet对象，指定工作表的索引、名称、表头信息
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "业务系统信息").head(BusinessSystemImportVO.class).needHead(Boolean.TRUE).build();

            // 写入空数据，实际上是为了生成模板
            excelWriter.write(new ArrayList<>(), writeSheet);
        } catch (Exception e) {
            // 日志记录导出模板失败的情况
            throw new RuntimeException("业务系统导出模板失败:" + e.getMessage());
        } finally {
            // 确保ExcelWriter资源被正确关闭
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 业务系统导出
     *
     * @param businessSystemQuery
     * @param response
     */
    public void export(BusinessSystemQuery businessSystemQuery, HttpServletResponse response) {

        IQueryWrapper<BusinessSystem> query = getService(BusinessSystem.class).getQuery();
        query.eq("delFlag", false);
        //名称不为空时模糊查询
        if (StringUtils.isNotEmpty(businessSystemQuery.getBusinessName())) {
            query.like("businessName", businessSystemQuery.getBusinessName());
        }
        //创建人不为空时模糊查询
        if (StringUtils.isNotEmpty(businessSystemQuery.getCreateBy())) {
            query.eq("createBy", businessSystemQuery.getCreateBy());
        }

        //创建时间不为空时，查询创建时间在指定时间范围内的数据
        if (StringUtils.isNotEmpty(businessSystemQuery.getCreateStartTime())) {
            query.ge("createTime", businessSystemQuery.getCreateStartTime());
        }
        if (StringUtils.isNotEmpty(businessSystemQuery.getCreateEndTime())) {
            query.le("createTime", businessSystemQuery.getCreateEndTime());
        }
        //id集合不为空时，查询id集合的数据
        if (CollUtil.isNotEmpty(businessSystemQuery.getBusinessIds())) {
            query.in("id", businessSystemQuery.getBusinessIds());
        }
        //业务状态不为空时，查询业务状态为指定状态的数据
        if (StringUtils.isNotEmpty(businessSystemQuery.getBusinessStatus())) {
            query.eq("businessStatus", businessSystemQuery.getBusinessStatus());
        }
        if (StringUtils.isNotEmpty(businessSystemQuery.getBusinessType())) {
            query.eq("businessType", businessSystemQuery.getBusinessType());
        }
        //所在部门
        if (StringUtils.isNotEmpty(businessSystemQuery.getDepartId())) {
            query.eq("departId", businessSystemQuery.getDepartId());
        }

        List<BusinessSystem> businessList = query.list();
        List<BusinessSystemImportVO> dataList = new ArrayList<>();
        ExcelWriter excelWriter = null;
        try {
            response.reset();
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/vnd.ms-excel");
            excelWriter = EasyExcel.write(response.getOutputStream(), BusinessSystemImportVO.class)
                    .autoCloseStream(false)
                    // 自动适配
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    // 大数值自动转换 防止失真
                    .registerConverter(new ExcelBigNumberConvert())
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();
            //将businessList 集合转为dataList集合
            //businessList不为空时，调用cms 获取所有部门列表得接口 和 获取所有负责人列表得接口
            Map<String, Dept> deptMap = new HashMap<>();
            Map<String, User> userMap = new HashMap<>();
            if (CollUtil.isNotEmpty(businessList)) {
                //获取部门列表
                deptMap = getDeptMap(false);
                //获取用户列表
                userMap = getUserMap(false);
            }
            for (BusinessSystem datasourceBusinessVO : businessList) {
                BusinessSystemImportVO datasourceBusinessImportVO = new BusinessSystemImportVO();
                datasourceBusinessImportVO.setBusinessName(datasourceBusinessVO.getBusinessName());
                datasourceBusinessImportVO.setSimpleName(datasourceBusinessVO.getSimpleName());
                datasourceBusinessImportVO.setDepartName(datasourceBusinessVO.getDepartName());
                //处理业务状态
                datasourceBusinessImportVO.setBusinessStatus(BusinessStatusEnum.getByType(datasourceBusinessVO.getBusinessStatus()).getChineseName());
                //处理业务系统类型
                datasourceBusinessImportVO.setBusinessType(BusinessEnum.getByType(datasourceBusinessVO.getBusinessType()).getChineseName());

                datasourceBusinessImportVO.setInterviewAddress(datasourceBusinessVO.getInterviewAddress());
                datasourceBusinessImportVO.setRemark(datasourceBusinessVO.getRemark());
                //处理部门对应值
                String departName = datasourceBusinessVO.getDepartName();
                if (StringUtils.isNotEmpty(departName) && deptMap.containsKey(departName)) {
                    Dept dept = deptMap.get(departName);
                    //部门名称(部门编码) 拼接
                    String deptName = dept.getName() + "(" + dept.getCode() + ")";
                    datasourceBusinessImportVO.setDepartName(deptName);
                }
                //处理负责人对应值
                String manager = datasourceBusinessVO.getManager();
                if (StringUtils.isNotEmpty(manager) && userMap.containsKey(manager)) {
                    //用户名称(用户编码) 拼接
                    User user = userMap.get(manager);
                    datasourceBusinessImportVO.setManager(user.getNickname() + "(" + user.getUsername() + ")");
                }
                //系统等级
                if (StringUtils.isNotEmpty(datasourceBusinessVO.getSecurityLevel())) {
                    datasourceBusinessImportVO.setSecurityLevel("L" + datasourceBusinessVO.getSecurityLevel());
                }
                dataList.add(datasourceBusinessImportVO);
            }
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "业务系统列表").head(BusinessSystemImportVO.class).needHead(Boolean.TRUE).build();
            excelWriter.write(dataList, writeSheet);
        } catch (Exception e) {
            throw new RuntimeException("业务系统导出失败:" + e.getMessage());
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 业务系统导入
     *
     * @param file
     * @return
     * @throws Exception
     */
    public List<BusinessSystemImportErrorVO> businessImport(MultipartFile file) throws Exception {
        //获取导入文件中得表
        long l0 = System.currentTimeMillis();
        log.info("业务系统导入开始时间：{}", l0);
        SheetForBusinessImportListener readListener = new SheetForBusinessImportListener(this);
        EasyExcel.read(file.getInputStream(),
                BusinessSystemImportVO.class,
                readListener).sheet(0).head(BusinessSystemImportVO.class).doRead();
        //获取数据
        List<BusinessSystemImportVO> dataList = readListener.getDataList();

        //获取错误数据
        List<BusinessSystemImportErrorVO> businessSystemImportErrorVOS = readListener.getBusinessSystemImportErrorVOS();

        //判断模板数据是否为空
        if (CollUtil.isEmpty(dataList) && CollUtil.isEmpty(businessSystemImportErrorVOS)) {
            throw new AppErrorException("模板数据为空,不能进行导入");
        }
        //处理数据
        List<BusinessSystem> datasourceBusinesses = new ArrayList<>();
        int i = 0;
        if (!dataList.isEmpty()) {
            for (BusinessSystemImportVO datasourceInfoImportVO : dataList) {
                //构建DatasourceBusiness对象
                BusinessSystem business = new BusinessSystem();
                business.setBusinessName(datasourceInfoImportVO.getBusinessName());
                business.setSimpleName(datasourceInfoImportVO.getSimpleName());
                business.setDepartName(datasourceInfoImportVO.getDepartName());
                business.setBusinessStatus(datasourceInfoImportVO.getBusinessStatus());
                business.setBusinessType(datasourceInfoImportVO.getBusinessType());

                business.setInterviewAddress(datasourceInfoImportVO.getInterviewAddress());

                business.setRemark(datasourceInfoImportVO.getRemark());

                business.setManager(datasourceInfoImportVO.getManager());
                if (StringUtils.isNotEmpty(datasourceInfoImportVO.getSecurityLevel())) {
                    String level = datasourceInfoImportVO.getSecurityLevel().substring(1);
                    business.setSecurityLevel(level);
                }
                datasourceBusinesses.add(business);
                i++;
                log.info("业务系统导入第[{}]行数据，业务系统名称类型[{}]", i, datasourceInfoImportVO.getBusinessName());
            }
        }
        //批量插入数据源
        log.info("业务系统导入总数为[{}]行数据", datasourceBusinesses.size());
        if (!datasourceBusinesses.isEmpty()) {
            super.add(datasourceBusinesses);
            long l1 = System.currentTimeMillis();
            log.info("业务系统导入结束时间：{}", l1);
            log.info("业务系统导入总共耗时：{}分钟", (l1 - l0) / (1000 * 60));
        }

        log.info("业务系统导入成功数据量[" + datasourceBusinesses.size() + "]");
        //错误数据不为空，返回数组
        if (!businessSystemImportErrorVOS.isEmpty()) {
            return businessSystemImportErrorVOS;
        }

        return new ArrayList<>();
    }

    /**
     * 业务系统导入失败数据导出
     *
     * @param businessSystemImportVOS
     */
    public void exportBusinessSystemErrorData(List<BusinessSystemImportErrorVO> businessSystemImportVOS, HttpServletResponse response) {
        List<BusinessSystemImportVO> dataList = new ArrayList<>();
        ExcelWriter excelWriter = null;
        try {
            response.reset();
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/vnd.ms-excel");
            excelWriter = EasyExcel.write(response.getOutputStream(), BusinessSystemImportVO.class)
                    .autoCloseStream(false)
                    // 自动适配
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    // 大数值自动转换 防止失真
                    .registerConverter(new ExcelBigNumberConvert())
                    .excelType(ExcelTypeEnum.XLSX)
                    .build();
            //将businessList 集合转为dataList集合
            //businessList不为空时，调用cms 获取所有部门列表得接口 和 获取所有负责人列表得接口
            Map<String, Dept> deptMap = new HashMap<>();
            Map<String, User> userMap = new HashMap<>();
            if (CollUtil.isNotEmpty(businessSystemImportVOS)) {
                //获取部门列表
                deptMap = getDeptMap(false);
                //获取用户列表
                userMap = getUserMap(false);
            }
            for (BusinessSystemImportErrorVO datasourceBusinessVO : businessSystemImportVOS) {
                BusinessSystemImportVO datasourceBusinessImportVO = new BusinessSystemImportVO();
                datasourceBusinessImportVO.setBusinessName(datasourceBusinessVO.getBusinessName());
                datasourceBusinessImportVO.setSimpleName(datasourceBusinessVO.getSimpleName());
                datasourceBusinessImportVO.setDepartName(datasourceBusinessVO.getDepartName());
                //处理业务状态
                datasourceBusinessImportVO.setBusinessStatus(BusinessStatusEnum.getByType(datasourceBusinessVO.getBusinessStatus()).getChineseName());
                //处理业务系统类型
                datasourceBusinessImportVO.setBusinessType(BusinessEnum.getByType(datasourceBusinessVO.getBusinessType()).getChineseName());

                datasourceBusinessImportVO.setInterviewAddress(datasourceBusinessVO.getInterviewAddress());

                datasourceBusinessImportVO.setRemark(datasourceBusinessVO.getRemark());

                //处理部门对应值
                String departName = datasourceBusinessVO.getDepartName();
                if (StringUtils.isNotEmpty(departName) && deptMap.containsKey(departName)) {
                    Dept dept = deptMap.get(departName);
                    //部门名称(部门编码) 拼接
                    String deptName = dept.getName() + "(" + dept.getCode() + ")";
                    datasourceBusinessImportVO.setDepartName(deptName);
                }
                //处理负责人对应值
                String manager = datasourceBusinessVO.getManager();
                if (StringUtils.isNotEmpty(manager) && userMap.containsKey(manager)) {
                    //用户名称(用户编码) 拼接
                    User user = userMap.get(manager);
                    datasourceBusinessImportVO.setManager(user.getNickname() + "(" + user.getUsername() + ")");
                }
                dataList.add(datasourceBusinessImportVO);
            }
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "业务系统错误数据列表").head(BusinessSystemImportVO.class).needHead(Boolean.TRUE).build();
            excelWriter.write(dataList, writeSheet);
        } catch (Exception e) {
            throw new RuntimeException("业务系统错误数据列表导出失败:" + e.getMessage());
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 获取部门map
     */
    public Map<String, Dept> getDeptMap(Boolean importFlag) {
        Map<String, Dept> deptMap = new HashMap<>();
        //获取token
        String token = ThreadLocalUserUtil.getCurrentUserToken();
        Map<String, Object> requestHeader = new HashMap<>();
        requestHeader.put("token", token);
        //获取部门列表
        try {
            List<Dept> deptList = cmsFeignService.getDeptList(null);
            //deptList 不为空时，将部门列表转为map，id作为key，Dept 作为value
            if (CollUtil.isNotEmpty(deptList)) {
                //判断是否为导入，如果是导入，则将部门名称(部门编码)作为key，Dept 作为value
                if (importFlag) {
                    //则将部门名称(部门编码)作为key，Dept 作为value
                    for (Dept dept : deptList) {
                        //部门名称(部门编码) 拼接
                        String deptName = dept.getName() + "(" + dept.getCode() + ")";
                        deptMap.put(deptName, dept);
                    }
                } else {
                    //将部门列表转为map，id作为key，Dept 作为value
                    deptMap = deptList.stream().collect(Collectors.toMap(Dept::getId, dept -> dept));
                }

            }

        } catch (Exception e) {
            log.error("获取部门列表失败，{}", e.getMessage());
        }
        return deptMap;
    }

    //获取用户map
    public Map<String, User> getUserMap(Boolean importFlag) {
        Map<String, User> userMap = new HashMap<>();
        //获取token
        String token = ThreadLocalUserUtil.getCurrentUserToken();
        Map<String, Object> requestHeader = new HashMap<>();
        requestHeader.put("token", token);
        //获取部门列表
        try {
            //获取负责人列表
            List<User> userList = cmsFeignService.getUserList(null, null, null, null, null, null);
            //userList 不为空时，将部门列表转为map，id作为key，User 作为value
            if (CollUtil.isNotEmpty(userList)) {
                if (importFlag) {
                    //则将用户名称(用户编码)-部门id作为key，User 作为value
                    for (User user : userList) {
                        //用户名称(用户编码) 拼接
                        String userName = user.getNickname() + "(" + user.getUsername() + ")-" + user.getDeptId();
                        userMap.put(userName, user);
                    }
                } else {
                    userMap = userList.stream().collect(Collectors.toMap(User::getId, user -> user));
                }
            }
        } catch (Exception e) {
            log.error("获取用户列表失败，{}", e.getMessage());
        }
        return userMap;
    }

    /**
     * 获取系统等级列表
     */
    public List<JSONObject> getSystemSecurityLevelList() {
        //获取token
        String token = ThreadLocalUserUtil.getCurrentUserToken();
        Map<String, Object> requestHeader = new HashMap<>();
        requestHeader.put("token", token);
        try {
            //获取安全分级列表
            JSONObject levelUrl = httpRequestFeignService.get4url(dsh_system_security_level_url, null, requestHeader, JSONObject.class, false);
            Objects.requireNonNull(levelUrl, "获取资产中心安全分级接口调用失败，请检查token相关参数!!!");
            String levelListStr = levelUrl.getString("result");
            List<JSONObject> levelList = JSONObject.parseObject(levelListStr, new TypeReference<List<JSONObject>>() {
            });
            return levelList;
        } catch (Exception e) {
            log.error("获取资产中心安全分级失败，{}", e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * 将系统等级列表 转为 map 以 level 作为key，JSONObject 作为value
     */
    public Map<String, JSONObject> getSystemSecurityLevelMap() {
        List<JSONObject> levelList = getSystemSecurityLevelList();
        Map<String, JSONObject> levelMap = new HashMap<>();
        if (CollUtil.isNotEmpty(levelList)) {
            levelMap = levelList.stream().collect(Collectors.toMap(jsonObject -> jsonObject.getString("level"), jsonObject -> jsonObject));
        }
        return levelMap;
    }


    /****
     * 根据业务系统id 将业务系统信息转换为业务系统导出模板内容
     */
    public BusinessSystemTemplate getBusinessSystemExportTemplate(String businessSystemId, String name) {
        BusinessSystemTemplate businessSystemTemplate = new BusinessSystemTemplate();
        BusinessSystem datasourceBusinessVO = getById(businessSystemId);
        businessSystemTemplate.setBusinessName(datasourceBusinessVO.getBusinessName());
        businessSystemTemplate.setSimpleName(datasourceBusinessVO.getSimpleName());
        businessSystemTemplate.setDepartName(datasourceBusinessVO.getDepartName());
        //处理业务状态
        businessSystemTemplate.setBusinessStatus(BusinessStatusEnum.getByType(datasourceBusinessVO.getBusinessStatus()).getChineseName());
        //处理业务系统类型
        businessSystemTemplate.setBusinessType(BusinessEnum.getByType(datasourceBusinessVO.getBusinessType()).getChineseName());

        businessSystemTemplate.setInterviewAddress(datasourceBusinessVO.getInterviewAddress());
        businessSystemTemplate.setRemark(datasourceBusinessVO.getRemark());
        //处理部门对应值
        Map<String, Dept> deptMap = new HashMap<>();
        Map<String, User> userMap = new HashMap<>();
        //获取部门列表
        deptMap = getDeptMap(false);
        //获取用户列表
        userMap = getUserMap(false);

        String departName = datasourceBusinessVO.getDepartName();
        if (StringUtils.isNotEmpty(departName) && deptMap.containsKey(departName)) {
            Dept dept = deptMap.get(departName);
            //部门名称(部门编码) 拼接
            String deptName = dept.getName() + "(" + dept.getCode() + ")";
            businessSystemTemplate.setDepartName(deptName);
        }
        //处理负责人对应值
        String manager = datasourceBusinessVO.getManager();
        if (StringUtils.isNotEmpty(manager) && userMap.containsKey(manager)) {
            //用户名称(用户编码) 拼接
            User user = userMap.get(manager);
            businessSystemTemplate.setManager(user.getNickname() + "(" + user.getUsername() + ")");
        }
        //系统等级
        if (StringUtils.isNotEmpty(datasourceBusinessVO.getSecurityLevel())) {
            businessSystemTemplate.setSecurityLevel("L" + datasourceBusinessVO.getSecurityLevel());
        }
        return businessSystemTemplate;
    }


    public List<BusinessSystem> batchDeleteBusinessSystem(List<String> ids) {
        List<BusinessSystem> businessSystems = new ArrayList<>();
        if (CollUtil.isNotEmpty(ids)) {
            WhereCondition whereConditions = new InCondition("id", ids);
            super.deleteBy(whereConditions);
        }
        return businessSystems;
    }
}
