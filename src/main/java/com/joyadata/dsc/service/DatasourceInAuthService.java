package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.joyadata.cores.auth.util.AuthUtil;
import com.joyadata.dsc.collector.MetadataCollector;
import com.joyadata.dsc.collector.MetadataCollectorFactory;
import com.joyadata.dsc.enums.MetadataTaskObjectEnum;
import com.joyadata.dsc.model.datasoure.*;
import com.joyadata.dsc.model.datasoure.dto.DatasourceAuthDTO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceAuthVO;
import com.joyadata.dsc.model.metadata.MetadataTableCommon;
import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import com.joyadata.dsc.utils.DatasourceUtil;
import com.joyadata.dsc.utils.MD5Util;
import com.joyadata.exception.AppErrorException;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.sql.InCondition;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/9
 */
@Service
@Slf4j
public class DatasourceInAuthService extends BaseService<DatasourceAuth> {

    @Autowired
    MetadataTableCommonService metadataTableCommonService;

    @Autowired
    DatasourceUtil datasourceUtil;
    // 生成唯一键的方法
    private  String generateUniqueKey(DatasourceAuth auth) {
        return auth.getDatasourceInfoId()+":"+ auth.getDbName() + ":" + Optional.ofNullable(auth.getSchemaName()).orElse("") + ":" + auth.getMetadataTableCommonId();
    }


    /**
     * 获取DatasourceDTO datasourceDTO 对象
     * todo 需要完成
     */
    public DatasourceDTO getDatasourceDTO(String datasourceId) {
        DatasourceDTO datasourceDTO = datasourceUtil.getDatasourceDTO(datasourceId);
        return datasourceDTO;
    }

    //判断此数据源有没有采集过
    public Boolean isCollection(String datasourceinfoId)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        DatasourceDTO datasourceDTO = getDatasourceDTO(datasourceinfoId);
        // 获取数据源类型名称，用于后续确定数据源的类型
        String datasourceTypeName = datasourceDTO.getDataType();
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceTypeName.toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        //如果collector为空 则不支持此数据源类型
        if (collector==null){
            throw new AppErrorException("数据源类型暂不支持获取库表"+datasourceTypeName);
        }
        return collector.isCollection(datasourceinfoId);
    }


    //根据数据源id，查询其下所有表得dbname或者schema分组，并去重
    public List<MetadataNodeVO> getDbNameOrSchemaNode(String datasourceinfoId, String dbName)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        DatasourceDTO datasourceDTO = getDatasourceDTO(datasourceinfoId);
        // 获取数据源类型名称，用于后续确定数据源的类型
        String datasourceTypeName = datasourceDTO.getDataType();
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceTypeName.toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        //如果collector为空 则不支持此数据源类型
        if (collector==null){
            throw new AppErrorException("数据源类型暂不支持获取库、模式"+datasourceTypeName);
        }
        List<MetadataNodeVO> dbNameByDatasourceinfoId = collector.getDbNameOrSchemaNode(datasourceinfoId,dbName,datasourceTypeName);
        return dbNameByDatasourceinfoId;
    }

    //根据数据源id，查询其下所有表得dbname或者schema分组，并去重
    public List<DatasourceAuthVO> getDbNameOrSchemaList(String datasourceinfoId, String dbName)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        DatasourceDTO datasourceDTO = getDatasourceDTO(datasourceinfoId);
        // 获取数据源类型名称，用于后续确定数据源的类型
        String datasourceTypeName = datasourceDTO.getDataType();
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceTypeName.toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        //如果collector为空 则不支持此数据源类型
        if (collector==null){
            throw new AppErrorException("数据源类型暂不支持获取库表"+datasourceTypeName);
        }
        List<DatasourceAuthVO> dbNameOrSchemaList = collector.getDbNameOrSchemaList(datasourceinfoId, dbName,datasourceTypeName);
        //将authType设置为db
        dbNameOrSchemaList.forEach(dbNameOrSchema -> {
            dbNameOrSchema.setAuthType(MetadataTaskObjectEnum.DB.getType());
        });
        //判断是否被授权
        List<DatasourceAuth> datasourceAuths = getService(DatasourceAuth.class)
                .getQuery()
                .eq("datasourceInfoId", datasourceinfoId)
                .eq("authType", MetadataTaskObjectEnum.DB.getType())
                .list();

        //包含将数据源id dbname schema metadataTableId 生成唯一值，生成map<schema,List<DatasourceAuth>>
        // 生成唯一键并聚合
        if(CollUtil.isEmpty(datasourceAuths)){
            return dbNameOrSchemaList;
        }
        Map<String, List<DatasourceAuth>> result = datasourceAuths.stream()
                .collect(Collectors.groupingBy(auth -> generateUniqueKey(auth)));


        List<DatasourceAuthVO> datasourceAuthVOS=new ArrayList<>();
        for (DatasourceAuthVO auth : dbNameOrSchemaList) {
            String key = auth.getDatasourceInfoId() + ":" + auth.getDbName() + ":" + Optional.ofNullable(auth.getSchemaName()).orElse("") + ":" + auth.getMetadataTableCommonId();
            //判断是否被授权
            if (result.containsKey(key)) {
                auth.setIsAuth(true);
                //授权后将项目id返回
                List<String> projectIds = result.get(key).stream().map(DatasourceAuth::getProjectId).collect(Collectors.toList());
                auth.setProjectIds(projectIds);
                auth.setDatasourceAuths(result.get(key));
            }else{
                auth.setIsAuth(false);
            }
            datasourceAuthVOS.add(auth);
        }
        return datasourceAuthVOS;
    }



    public List<DatasourceAuthVO> getTableList(DatasourceAuthDTO table,Integer page,Integer pager)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        DatasourceDTO datasourceDTO = getDatasourceDTO(table.getDatasourceInfoId());
        // 获取数据源类型名称，用于后续确定数据源的类型
        String datasourceTypeName = datasourceDTO.getDataType();
        // 根据数据源类型名称获取对应的数据库枚举类型
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.getDbEnum(datasourceTypeName.toLowerCase());
        // 根据数据库枚举类型获取相应的元数据收集器
        MetadataCollector collector = MetadataCollectorFactory.getCollector(dataSourceTypeEnum);
        //如果collector为空 则不支持此数据源类型
        if (collector==null){
            throw new AppErrorException("数据源类型暂不支持获取库表"+datasourceTypeName);
        }
        MetadataTableCommon table1=new MetadataTableCommon();
        table1.setDbName(table.getDbName());
        table1.setSchemaName(table.getSchemaName());
        table1.setDatasourceInfoId(table.getDatasourceInfoId());
        table1.setType(table.getMetadataTableType());
        table1.setName(table.getMetadataTableName());
        List<DatasourceAuthVO> dbNameOrSchemaList = collector.getTableList(table1,page,pager,null);
        dbNameOrSchemaList.forEach(dbNameOrSchema -> {
            dbNameOrSchema.setAuthType(MetadataTaskObjectEnum.TABLE.getType());
        });
        //判断是否被授权
        List<DatasourceAuth> datasourceAuths = getService(DatasourceAuth.class)
                .getQuery()
                .eq("datasourceInfoId", table.getDatasourceInfoId())
                .eq("authType", MetadataTaskObjectEnum.TABLE.getType())
                .list();

        //包含将数据源id dbname schema metadataTableId 生成唯一值，生成map<schema,List<DatasourceAuth>>
        // 生成唯一键并聚合
        List<String> tableNames = table.getTableNames();
        if(CollUtil.isEmpty(datasourceAuths)){
            //当getProjectIds不为空时将，集合设置到返回值中
            List<String> projectIds = table.getProjectIds();
            for (DatasourceAuthVO datasourceAuthVO : dbNameOrSchemaList) {
                if(CollUtil.isNotEmpty(tableNames)){
                    if(tableNames.contains(datasourceAuthVO.getMetadataTableName())){
                        datasourceAuthVO.setIsSelect(true);
                        datasourceAuthVO.setProjectIds(projectIds);
                        continue;
                    }
                }
                datasourceAuthVO.setIsSelect(false);
            }
            return dbNameOrSchemaList;
        }
        Map<String, List<DatasourceAuth>> result = datasourceAuths.stream()
                .collect(Collectors.groupingBy(auth -> generateUniqueKey(auth)));
        List<DatasourceAuthVO> datasourceAuthVOS=new ArrayList<>();
        for (DatasourceAuthVO auth : dbNameOrSchemaList) {
            String key = auth.getDatasourceInfoId() + ":" + auth.getDbName() + ":" + Optional.ofNullable(auth.getSchemaName()).orElse("") + ":" + auth.getMetadataTableCommonId();
            //判断是否被授权
            List<String> projectIds1 = table.getProjectIds();
            if (result.containsKey(key)) {
                auth.setIsAuth(true);
                //授权后将项目id返回
                List<String> projectIds = result.get(key).stream().map(DatasourceAuth::getProjectId).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(projectIds1)){
                    //判断是否被选中
                    if(tableNames.contains(auth.getMetadataTableName())){
                        auth.setIsSelect(true);
                        projectIds.addAll(projectIds1);   //对projectIds去重
                    }else{
                        auth.setIsSelect(false);
                    }
                    projectIds = projectIds.stream().distinct().collect(Collectors.toList());
                }
                auth.setProjectIds(projectIds);
                auth.setDatasourceAuths(result.get(key));
            }else{
                auth.setIsAuth(false);
                if(CollUtil.isNotEmpty(projectIds1)){
                    if(tableNames.contains(auth.getMetadataTableName())){
                        auth.setIsSelect(true);
                        auth.setProjectIds(projectIds1);
                    }else{
                        auth.setIsSelect(false);
                    }
                }
            }
            datasourceAuthVOS.add(auth);
        }
        return datasourceAuthVOS;
    }

    public Integer getTableTotal(DatasourceAuthDTO table)  {
        MetadataTableCommon table1=new MetadataTableCommon();
        table1.setDbName(table.getDbName());
        table1.setSchemaName(table.getSchemaName());
        table1.setDatasourceInfoId(table.getDatasourceInfoId());
        table1.setType(table.getMetadataTableType());
        table1.setName(table.getMetadataTableName());
        Integer tableTotalByDatasourceinfoId = metadataTableCommonService.getTableTotalByDatasourceinfoId(table1,null);
        return tableTotalByDatasourceinfoId;
    }



    public void addBatch(DatasourceAuthDTO datasourceAuthDTOList) {
        List<DatasourceAuth> datasourceAuthsList=new ArrayList<>();
        //构建选中的数据源对象
        List<DatasourceAuthVO> datasourceAuths = datasourceAuthDTOList.getDatasourceAuths();
        for (DatasourceAuthVO datasourceAuth : datasourceAuths) {
            //获取授权项目id，并以逗号分隔，构建数据源授权对象
            List<String> projectIds = datasourceAuth.getProjectIds();
            if(CollUtil.isEmpty(projectIds)){
                continue;
            }
            //判断是否授权
            Boolean isAuth = datasourceAuth.getIsAuth();
            if(isAuth){
                //授权过
                //获取授权id
                List<DatasourceAuth> datasourceAuths1 = datasourceAuth.getDatasourceAuths();
                if(CollUtil.isNotEmpty(datasourceAuths1)){
                    //获取授权id集合
                    List<String> ids = datasourceAuths1.stream().map(DatasourceAuth::getProjectId).collect(Collectors.toList());
                    //获取到ids集合比projectIds少的集合，并生成新的集合
                    List<String> collect = projectIds.stream().filter(id -> !ids.contains(id)).collect(Collectors.toList());
                   if(CollUtil.isNotEmpty(collect)){
                        for (String id : collect) {
                            DatasourceAuth datasourceAuth1 = new DatasourceAuth();
                            datasourceAuth1.setProjectId(id);
                            datasourceAuth1.setDatasourceInfoId(datasourceAuthDTOList.getDatasourceInfoId());
                            datasourceAuth1.setDbName(datasourceAuth.getDbName());
                            datasourceAuth1.setSchemaName(datasourceAuth.getSchemaName());
                            datasourceAuth1.setAuthType(datasourceAuth.getAuthType());
                            datasourceAuth1.setMetadataTableCommonId(datasourceAuth.getMetadataTableCommonId());
                            datasourceAuth1.setMetadataTableType(datasourceAuth.getMetadataTableType());
                            datasourceAuthsList.add(datasourceAuth1);
                        }
                    }
                }
            }else {
                for (String project : projectIds) {
                    DatasourceAuth datasourceAuth1 = new DatasourceAuth();
                    datasourceAuth1.setProjectId(project);
                    datasourceAuth1.setDatasourceInfoId(datasourceAuthDTOList.getDatasourceInfoId());
                    datasourceAuth1.setDbName(datasourceAuth.getDbName());
                    datasourceAuth1.setSchemaName(datasourceAuth.getSchemaName());
                    datasourceAuth1.setAuthType(datasourceAuth.getAuthType());
                    datasourceAuth1.setMetadataTableCommonId(datasourceAuth.getMetadataTableCommonId());
                    datasourceAuth1.setMetadataTableType(datasourceAuth.getMetadataTableType());
                    datasourceAuthsList.add(datasourceAuth1);
                }
            }
        }
        //当datasourceAuthsList不为空时批量插入
        if (CollUtil.isNotEmpty(datasourceAuthsList)) {
            //datasourceAuthsList 每一百条执行一次保存
            int batchSize = 100;
            for (int i = 0; i < datasourceAuthsList.size(); i += batchSize) {
                int end = Math.min(i + batchSize, datasourceAuthsList.size());
                List<DatasourceAuth> batch = datasourceAuthsList.subList(i, end);
                getService(DatasourceAuth.class).add(batch);
            }
            //根据数据源id获取数据源对象
            DatasourceInfo datasourceInfoId = getService(DatasourceInfo.class).getQuery().
                    eq("id", datasourceAuthDTOList.getDatasourceInfoId()).one();
            DatasourceInfo datasourceInfo=new DatasourceInfo();
            datasourceInfo.setId(datasourceAuthDTOList.getDatasourceInfoId());
            datasourceInfo.setAuthFlag(1);
//            datasourceInfo.setAuthType(datasourceAuth.getAuthType());
            datasourceInfo.setAuthTime(new Date());
            datasourceInfo.setAuthOperator(AuthUtil.getCurrentUser().getUsername());
            datasourceInfo.setLastModificationTime(datasourceInfoId.getLastModificationTime());
            //修改数据源对象
            getService(DatasourceInfo.class).update(datasourceInfo);
        }
    }


    public void deleteBatch(DatasourceAuthDTO datasourceAuthDtoS) {
        List<DatasourceAuth> datasourceAuthsList=new ArrayList<>();
        //构建选中的数据源对象
        List<DatasourceAuthVO> datasourceAuths = datasourceAuthDtoS.getDatasourceAuths();
        for (DatasourceAuthVO datasourceAuth : datasourceAuths) {
            List<String> projectIds = datasourceAuth.getProjectIds();
            //若为空，则表示项目全删除
            if(CollUtil.isEmpty(projectIds)){
                datasourceAuthsList.addAll(datasourceAuth.getDatasourceAuths());
               continue;
            }
            //获取项目id
            List<DatasourceAuth> datasourceAuths1 = datasourceAuth.getDatasourceAuths();
            if(CollUtil.isNotEmpty(datasourceAuths1)) {
                //以项目id作为key，DatasourceAuth作为value 生成map
                Map<String, DatasourceAuth> datasourceAuthMap = datasourceAuths1.stream().collect(Collectors.toMap(DatasourceAuth::getProjectId, datasourceAuth1 -> datasourceAuth1));
                //获取授权id集合
                List<String> ids = datasourceAuths1.stream().map(DatasourceAuth::getProjectId).collect(Collectors.toList());
                //获取到ids集合比projectIds多的数据，并生成新的集合,就是取消授权的项目
                List<String> collect = ids.stream().filter(id -> !projectIds.contains(id)).collect(Collectors.toList());
                //构建数据源授权对象
                for (String project : collect) {
                    if(datasourceAuthMap.containsKey(project)){
                        DatasourceAuth datasourceAuth1 = datasourceAuthMap.get(project);
                        datasourceAuthsList.add(datasourceAuth1);
                    }
                }
            }
        }
        //当datasourceAuthsList不为空时批量删除
        if (CollUtil.isNotEmpty(datasourceAuthsList)) {
            List<String> ids = datasourceAuthsList.stream().map(DatasourceAuth::getId).collect(Collectors.toList());
            InCondition inCondition = new InCondition("id", ids);
            deleteBy(inCondition);
            //根据数据源id修改数据源
            //根据数据源id获取数据源对象
            DatasourceInfo datasourceInfoId = getService(DatasourceInfo.class).getQuery().
                    eq("id", datasourceAuthDtoS.getDatasourceInfoId()).one();
            DatasourceInfo datasourceInfo=new DatasourceInfo();
            datasourceInfo.setId(datasourceAuthDtoS.getDatasourceInfoId());
            datasourceInfo.setAuthFlag(1);
//            datasourceInfo.setAuthType(datasourceAuthDtoS.getAuthType());
            datasourceInfo.setAuthTime(new Date());
            datasourceInfo.setAuthOperator(AuthUtil.getCurrentUser().getUsername());
            datasourceInfo.setLastModificationTime(datasourceInfoId.getLastModificationTime());
        }
    }



    //获取以授权的列表
    public List<DatasourceAuthVO> getAuthList(DatasourceAuthDTO authDTO) {
        List<DatasourceAuthVO> datasourceAuthVOS=new ArrayList<>();
        IQueryWrapper<DatasourceAuth> query = getService(DatasourceAuth.class)
                .getQuery();
        query.eq("datasourceInfoId", authDTO.getDatasourceInfoId());
        query.withs("metadataTableName","metadataTableType");
        if(StringUtils.isNotEmpty(authDTO.getDbName())){
            if(MetadataTaskObjectEnum.TABLE.getType().equalsIgnoreCase(authDTO.getAuthType())){
                query.eq("dbName", authDTO.getDbName());
            }else{
                query.like("dbName", authDTO.getDbName());
            }
        }
        if(StringUtils.isNotEmpty(authDTO.getMetadataTableName())){
            query.like("metadataTableName", authDTO.getMetadataTableName());
        }
        if(StringUtils.isNotEmpty(authDTO.getMetadataTableType())){
            query.eq("metadataTableType", authDTO.getMetadataTableType());
        }

        if(StringUtils.isNotEmpty(authDTO.getAuthType())){
            query.eq("authType", authDTO.getAuthType());
        }
        List<DatasourceAuth> datasourceAuths = query.list();
        if(CollUtil.isEmpty(datasourceAuths)){
            return datasourceAuthVOS;
        }
        //包含将dbname schema metadataTableId 生成唯一值，生成map<schema,List<DatasourceAuth>>

        // 生成唯一键并聚合
        Map<String, List<DatasourceAuth>> result = datasourceAuths.stream()
                .collect(Collectors.groupingBy(auth -> generateUniqueKey(auth)));

        // 输出结果
        result.forEach((key, value) -> {
            DatasourceAuthVO datasourceAuthVO=new DatasourceAuthVO();
            DatasourceAuth datasourceAuth = value.get(0);
            datasourceAuthVO.setId(datasourceAuth.getId());
            datasourceAuthVO.setDatasourceInfoId(datasourceAuth.getDatasourceInfoId());
            datasourceAuthVO.setDbName(datasourceAuth.getDbName());
            datasourceAuthVO.setSchemaName(datasourceAuth.getSchemaName());
            datasourceAuthVO.setMetadataTableCommonId(datasourceAuth.getMetadataTableCommonId());
            datasourceAuthVO.setMetadataTableName(datasourceAuth.getMetadataTableName());
            datasourceAuthVO.setAuthType(datasourceAuth.getAuthType());
            //获取项目id数组
            if(CollUtil.isNotEmpty(value)){
                //获取项目id数组
                List<String> collect = value.stream().map(DatasourceAuth::getProjectId).collect(Collectors.toList());
                datasourceAuthVO.setProjectIds(collect);
            }
            datasourceAuthVO.setDatasourceAuths(value);
            datasourceAuthVO.setIsAuth(true);
            datasourceAuthVO.setCreateByName(datasourceAuth.getCreateByName());
            datasourceAuthVO.setLastModificationTime(datasourceAuth.getLastModificationTime());
            String uuid = MD5Util.tableIdToMD5(datasourceAuth.getDbName(), datasourceAuth.getSchemaName(), datasourceAuth.getDatasourceInfoId());
            datasourceAuthVO.setUuid(uuid);
            datasourceAuthVO.setMetadataTableType(datasourceAuth.getMetadataTableType());
            datasourceAuthVOS.add(datasourceAuthVO);
        });
        return datasourceAuthVOS;
    }

    //快速选表，获取到哪些表不存在
    public List<String> getNotExistTable(DatasourceAuthDTO datasourceAuthDTO) {
        List<String> notExistTableList = new ArrayList<>();
        MetadataTableCommon table1=new MetadataTableCommon();
        table1.setDbName(datasourceAuthDTO.getDbName());
        table1.setSchemaName(datasourceAuthDTO.getSchemaName());
        table1.setDatasourceInfoId(datasourceAuthDTO.getDatasourceInfoId());
        table1.setType(datasourceAuthDTO.getMetadataTableType());
        table1.setName(datasourceAuthDTO.getMetadataTableName());
        List<String> tablesExist = metadataTableCommonService.getTablesExist(table1, datasourceAuthDTO.getTableNames());
        //获取到哪些表不存在
        if(tablesExist.isEmpty()){
            return datasourceAuthDTO.getTableNames();
        }
        List<String> tableNames = datasourceAuthDTO.getTableNames();

        //获取到tableNames在tablesExist哪些没有并生成新得集合
        notExistTableList = tableNames.stream()
                .filter(tableName -> !tablesExist.contains(tableName))
                .collect(Collectors.toList());
        return notExistTableList;

    }


}
