package com.joyadata.dsc.service;

import com.joyadata.dsc.model.metadata.MetadataColumnCommonDetail;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * MetadataTaskService类用于处理元数据任务的相关操作
 * 它继承自BaseService类，为MetadataTask实体提供数据访问和业务逻辑操作的封装
 * <AUTHOR>
 */
@Service
@Slf4j
public class MetadataColumnCommonDetailService extends BaseService<MetadataColumnCommonDetail> {

    public void deleteByTaskRecordId(String taskRecordId){
        EqCondition delete = new EqCondition("metadataTaskRecordId",taskRecordId);
        this.deleteBy(delete);
    }

}
