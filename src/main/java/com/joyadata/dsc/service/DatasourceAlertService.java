package com.joyadata.dsc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.cms.model.ProductEvent;
import com.joyadata.cms.model.Role;
import com.joyadata.cms.model.User;
import com.joyadata.csc.model.AlarmCondition;
import com.joyadata.csc.model.AlarmStrategy;
import com.joyadata.dsc.enums.AlertStatusEnum;
import com.joyadata.dsc.enums.NoticeWayEnum;
import com.joyadata.dsc.enums.ReceiveControlEnum;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.DatasourceAlertEvent;
import com.joyadata.dsc.model.datasoure.DatasourceAlertStrategy;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.migration.dto.DatasourceAlertTemplate;
import com.joyadata.dsc.properties.DatabaseProperties;
import com.joyadata.exception.AppWarningException;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.model.BaseBean;
import com.joyadata.model.web.Response;
import com.joyadata.service.BaseService;
import com.joyadata.tms.model.Product;
import com.joyadata.tms.model.Project;
import com.joyadata.util.GeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.telnet.TelnetClient;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName DatasourceAlertService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/6 16:14
 * @Version 1.0
 **/
@Service
@Slf4j
public class DatasourceAlertService extends BaseService<DatasourceAlert> {

    private JoyaFeignService<Product> productJoyaFeignService = FeignFactory.make(Product.class);
    private JoyaFeignService<ProductEvent> productEventJoyaFeignService = FeignFactory.make(ProductEvent.class);
    private JoyaFeignService<AlarmStrategy> alarmStrategyJoyaFeignService = FeignFactory.make(AlarmStrategy.class);
    @Autowired
    private DatabaseProperties databaseProperties;

    public Product getProduct(String code) {
        //sourceMetadata
        return productJoyaFeignService.getQuery().eq("code", code).one();
    }

    public List<ProductEvent> getProductEvent(String productId) {
        return productEventJoyaFeignService.getQuery().eq("productId", productId).list();
    }

    public DatasourceAlert save(DatasourceAlert datasourceAlert) {
        DatasourceInfo datasourceInfo = getService(DatasourceInfo.class)
                .getQuery()
                .eq("id", datasourceAlert.getDatasourceInfoId())
                .one(); // 根据id查询数据
        if (null == datasourceInfo) {
            throw new AppWarningException(Response.Code.Warning, "数据源无效");
        }
        setNoticeUser(datasourceAlert);
        //datasourceAlert.setMsgFormat(databaseProperties.getAlertMsgFormat());
        Product product = getProduct("sourceMetadata"); // 调用接口
        if (null == product) {
            throw new AppWarningException(Response.Code.Warning, "未初始化数据源中心产品数据");
        }
        // 获取全部事件
        List<ProductEvent> productEvents = getProductEvent(product.getId());
        if (CollUtil.isEmpty(productEvents)) {
            throw new AppWarningException(Response.Code.Warning, "未初始化数据源中心事件信息");
        }
        Map<String, ProductEvent> eventMap = productEvents.stream().collect(Collectors.toMap(BaseBean::getId, t -> t, (v1, v2) -> v1));
        List<DatasourceAlertEvent> datasourceAlertEvents = datasourceAlert.getDatasourceAlertEvents();
        if (CollectionUtils.isEmpty(datasourceAlertEvents)) {
            throw new AppWarningException(Response.Code.Warning, "告警事件必填");
        }

        for (DatasourceAlertEvent datasourceAlertEvent : datasourceAlertEvents) {
            // 获取选中事件
            ProductEvent productEvent = eventMap.get(datasourceAlertEvent.getEventId());
            // 转成告警策略对象
            AlarmStrategy alarmStrategy = convertAlarmStrategy(datasourceAlert, datasourceAlertEvent, productEvent);
            if (StrUtil.isNotBlank(datasourceAlert.getUserIds())) {
                alarmStrategy.setName(String.format("【%s】【%s】-用户", datasourceInfo.getDatasourceName(), productEvent.getName()));
                alarmStrategy.setReceiveType("user");
                JSONArray jsonArray = getSubscriber(datasourceAlert, "user");
                alarmStrategy.setSubscriber(jsonArray);
                // 写入告警策略
                AlarmStrategy strategy = alarmStrategyJoyaFeignService.add(alarmStrategy);
                DatasourceAlertStrategy datasourceAlertStrategy = new DatasourceAlertStrategy();
                datasourceAlertStrategy.setAlarmStrategyId(strategy.getId());
                datasourceAlertStrategy.setNoticeType("user");
                datasourceAlert.addDatasourceAlertStrategy(datasourceAlertStrategy);
            }

            if (StrUtil.isNotBlank(datasourceAlert.getRoleIds())) {
                alarmStrategy.setName(String.format("【%s】【%s】-角色", datasourceInfo.getDatasourceName(), productEvent.getName()));
                alarmStrategy.setReceiveType("role");
                JSONArray jsonArray = getSubscriber(datasourceAlert, "role");
                alarmStrategy.setSubscriber(jsonArray);
                // 写入告警策略
                AlarmStrategy strategy = alarmStrategyJoyaFeignService.add(alarmStrategy);
                DatasourceAlertStrategy datasourceAlertStrategy = new DatasourceAlertStrategy();
                datasourceAlertStrategy.setAlarmStrategyId(strategy.getId());
                datasourceAlertStrategy.setNoticeType("role");
                datasourceAlert.addDatasourceAlertStrategy(datasourceAlertStrategy);
            }

            if (StrUtil.isNotBlank(datasourceAlert.getRequestUrl())) {
                alarmStrategy.setName(String.format("【%s】【%s】-HTTP", datasourceInfo.getDatasourceName(), productEvent.getName()));
                alarmStrategy.setReceiveType("");
                JSONArray jsonArray = getSubscriber(datasourceAlert, datasourceAlert.getRequestMethod());
                alarmStrategy.setSubscriber(jsonArray);
                // 写入告警策略
                AlarmStrategy strategy = alarmStrategyJoyaFeignService.add(alarmStrategy);
                DatasourceAlertStrategy datasourceAlertStrategy = new DatasourceAlertStrategy();
                datasourceAlertStrategy.setAlarmStrategyId(strategy.getId());
                datasourceAlertStrategy.setNoticeType("http");
                datasourceAlert.addDatasourceAlertStrategy(datasourceAlertStrategy);
            }

            // 写入告警策略
            //AlarmStrategy strategy = alarmStrategyJoyaFeignService.add(alarmStrategy);
            // 告警策略id写回 数据源告警事件
            //datasourceAlertEvent.setAlarmStrategyId(strategy.getId());
        }

        datasourceAlert.setId(GeneratorUtil.genCode());
        datasourceAlert.setCascades(new String[]{"datasourceAlertEvents", "datasourceAlertStrategies"});
        return add(datasourceAlert.getId(), datasourceAlert);
    }

    @NotNull
    private JSONArray getSubscriber(DatasourceAlert datasourceAlert, String recipient) {
        Map<String, Object> subscriber = new HashMap<>();
        subscriber.put("type", datasourceAlert.getNoticeWay()); // 通知方式
        subscriber.put("enable", true);
        subscriber.put("recipient", recipient); // 通知范围
        subscriber.put("formatClass", datasourceAlert.getMsgFormat());
        subscriber.put("roleNames", datasourceAlert.getRoleNames());
        subscriber.put("roleIds", datasourceAlert.getRoleIds());
        subscriber.put("receivesNames", datasourceAlert.getUserNames());
        subscriber.put("receives", datasourceAlert.getUserIds());
        subscriber.put("receiveAddress", datasourceAlert.getRequestUrl());
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(subscriber);
        return jsonArray;
    }

    private AlarmStrategy convertAlarmStrategy(DatasourceAlert datasourceAlert,
                                               DatasourceAlertEvent datasourceAlertEvent,
                                               ProductEvent productEvent) {
        AlarmStrategy alarmStrategy = new AlarmStrategy();
        //alarmStrategy.setName(String.format("【%s】【%s】告警配置", datasourceInfo.getDatasourceName(), productEvent.getName())); // 【数据源名称】 + 告警配置
        alarmStrategy.setProductId(productEvent.getProductId()); // 获取数据源中心的产品id
        //alarmStrategy.setAlarmLevel(datasourceAlertEvent.getAlertLevel()); // form表单提供或者关联到告警事件上
        alarmStrategy.setAlarmLevel(AlertStatusEnum.getName(productEvent.getCode()));
        alarmStrategy.setEventCode(productEvent.getCode());
        alarmStrategy.setEventName(productEvent.getName());
        alarmStrategy.setEventCategory(productEvent.getCategory());
        alarmStrategy.setSuggestion(productEvent.getSuggestion()); // 处理建议
        alarmStrategy.setContent(productEvent.getContent());
        //alarmStrategy.setTriggerType("全部"); // 触发策略，默认值：全部
        alarmStrategy.setTriggerType("条件触发"); // 触发策略，默认值：全部
        alarmStrategy.setActive(datasourceAlertEvent.getAlertStatus()); // form表单提供
        alarmStrategy.setReceiveAlarmType(datasourceAlert.getNoticeWay()); // form表单提供, {1:邮箱,2:短信,3:Http,4:企业微信,5:定制企业微信}
        alarmStrategy.setReceiveControl(datasourceAlert.getReceiveControl()); // 接收控制，1每次触发告警时候发送，2当日首次触发告警时候发送
        alarmStrategy.setAppointAlarmStartTime(datasourceAlert.getAppointAlarmStartTime());
        alarmStrategy.setAppointAlarmEndTime(datasourceAlert.getAppointAlarmEndTime());

        alarmStrategy.setMatchExpr("A");
        List<AlarmCondition> alarmConditions = new ArrayList<>();
        AlarmCondition alarmCondition = new AlarmCondition();
        alarmCondition.setName("datasourceId");
        alarmCondition.setConditionType("eq");
        alarmCondition.setValue(datasourceAlert.getDatasourceInfoId());
        alarmCondition.setGroupName("A");
        alarmCondition.setEnable(true);
        alarmConditions.add(alarmCondition);
        alarmStrategy.setAlarmCondition(alarmConditions);
        alarmStrategy.setCascades(new String[]{"alarmCondition"});

        //alarmStrategy.setReceiveType(datasourceAlert.getNoticeType()); // form表单提供，接收方式--> 指定人: user , 指定角色: role, 固定邮箱：fixedEmail 固定手机号：fixedPhone , 其他：other, 指定组(, 分隔 [大家保险定制])：groupIds

        //Map<String, Object> subscriber = new HashMap<>();
        //subscriber.put("type", datasourceAlert.getNoticeWay()); // 通知方式
        //subscriber.put("enable", true);
        //subscriber.put("recipient", datasourceAlert.getNoticeType()); // 通知范围
        //subscriber.put("formatClass", datasourceAlert.getMsgFormat());
        //subscriber.put("roleNames", datasourceAlert.getRoleNames());
        //subscriber.put("roleIds", datasourceAlert.getRoleIds());
        //subscriber.put("receivesNames", datasourceAlert.getUserNames());
        //subscriber.put("receives", datasourceAlert.getUserIds());
        //subscriber.put("receiveAddress", datasourceAlert.getRequestUrl());
        //if (StrUtil.equals("3", datasourceAlert.getNoticeWay())) {
        //    subscriber.put("recipient", datasourceAlert.getRequestMethod());
        //}
        //if (StrUtil.isNotBlank(datasourceAlert.getUserIds())
        //        || StrUtil.isNotBlank(datasourceAlert.getRoleIds())
        //        || StrUtil.isNotBlank(datasourceAlert.getRequestUrl())) {
        //    JSONArray jsonArray = new JSONArray();
        //    jsonArray.add(subscriber);
        //    alarmStrategy.setSubscriber(jsonArray);
        //}

        return alarmStrategy;
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer modify(DatasourceAlert datasourceAlert) {
        DatasourceInfo datasourceInfo = getService(DatasourceInfo.class)
                .getQuery()
                .eq("id", datasourceAlert.getDatasourceInfoId())
                .one(); // 根据id查询数据
        if (null == datasourceInfo) {
            throw new AppWarningException(Response.Code.Warning, "数据源无效");
        }
        DatasourceAlert oriDatasourceAlert = getQuery().lazys("datasourceAlertEvents", "datasourceAlertStrategies")
                .eq("id", datasourceAlert.getId()).one();
        // 校验告警id在数据库存在不存在
        if (null == datasourceAlert.getId() || null == oriDatasourceAlert) {
            save(datasourceAlert);
            return 1;
        }
        setNoticeUser(datasourceAlert);
        //datasourceAlert.setMsgFormat(databaseProperties.getAlertMsgFormat());
        datasourceAlert.setCascades(new String[]{"datasourceAlertEvents", "datasourceAlertStrategies"});
        List<DatasourceAlertStrategy> datasourceAlertStrategies = oriDatasourceAlert.getDatasourceAlertStrategies();
        if (CollUtil.isNotEmpty(datasourceAlertStrategies)) {
            for (DatasourceAlertStrategy datasourceAlertStrategy : datasourceAlertStrategies) {
                // 先调用远程接口删除告警策略
                alarmStrategyJoyaFeignService.delete(datasourceAlertStrategy.getAlarmStrategyId());
            }
        }
        // 再删除数据源告警信息，并级联删除告警事件
        delete(datasourceAlert.getId(), datasourceAlert);
        if (!CollectionUtils.isEmpty(datasourceAlert.getDatasourceAlertEvents())) {
            save(datasourceAlert);
        }
        return 1;
    }

    //public Integer modify(DatasourceAlert datasourceAlert) {
    //    DatasourceInfo datasourceInfo = getService(DatasourceInfo.class)
    //            .getQuery()
    //            .eq("id", datasourceAlert.getDatasourceInfoId())
    //            .one(); // 根据id查询数据
    //    if (null == datasourceInfo) {
    //        throw new AppWarningException(Response.Code.Warning, "数据源无效");
    //    }
    //    DatasourceAlert oriDatasourceAlert = getQuery().lazys("datasourceAlertEvents", "datasourceAlertStrategies")
    //            .eq("id", datasourceAlert.getId()).one();
    //    // 校验告警id在数据库存在不存在
    //    if (null == datasourceAlert.getId() || null == oriDatasourceAlert) {
    //        save(datasourceAlert);
    //        return 1;
    //    }
    //    setNoticeUser(datasourceAlert);
    //    datasourceAlert.setMsgFormat(databaseProperties.getAlertMsgFormat());
    //    datasourceAlert.setCascades(new String[]{"datasourceAlertEvents", "datasourceAlertStrategies"});
    //    // form表单中的告警策略为空则删除数据
    //    if (CollectionUtils.isEmpty(datasourceAlert.getDatasourceAlertEvents())) {
    //        // 使用数据库存在的策略id删除
    //        List<DatasourceAlertEvent> datasourceAlertEvents = oriDatasourceAlert.getDatasourceAlertEvents();
    //        for (DatasourceAlertEvent datasourceAlertEvent : datasourceAlertEvents) {
    //            // 先调用远程接口删除告警策略
    //            alarmStrategyJoyaFeignService.delete(datasourceAlertEvent.getAlarmStrategyId());
    //        }
    //        // 再删除数据源告警信息，并级联删除告警事件
    //        return delete(datasourceAlert.getId(), datasourceAlert);
    //    }
    //
    //    Product product = getProduct("sourceMetadata"); // 调用接口
    //    if (null == product) {
    //        throw new AppWarningException(Response.Code.Warning, "未初始化数据源中心产品数据");
    //    }
    //    // 获取全部事件
    //    List<ProductEvent> productEvents = getProductEvent(product.getId());
    //    if (CollUtil.isEmpty(productEvents)) {
    //        throw new AppWarningException(Response.Code.Warning, "未初始化数据源中心事件信息");
    //    }
    //    Map<String, ProductEvent> eventMap = productEvents.stream().collect(Collectors.toMap(BaseBean::getId, t -> t, (v1, v2) -> v1));
    //
    //    // form表单中的告警策略需要和原策略比较
    //    List<DatasourceAlertEvent> datasourceAlertEvents = Optional.ofNullable(datasourceAlert.getDatasourceAlertEvents()).orElse(new ArrayList<>());
    //    List<DatasourceAlertEvent> oriDatasourceAlertEvents = oriDatasourceAlert.getDatasourceAlertEvents();
    //    List<String> eventIds = datasourceAlertEvents.stream().map(DatasourceAlertEvent::getEventId).collect(Collectors.toList());
    //    List<String> oriEventIds = oriDatasourceAlertEvents.stream().map(DatasourceAlertEvent::getEventId).collect(Collectors.toList());
    //    // 交集数据做更新
    //    List<String> intersectList = (List<String>) CollUtil.intersection(eventIds, oriEventIds);
    //    for (String eventId : intersectList) {
    //        // 获取选中事件
    //        ProductEvent productEvent = eventMap.get(eventId);
    //        // 做更新操作，事件id必须存在，所以使用数据库的事件
    //        DatasourceAlertEvent oriDatasourceAlertEvent = oriDatasourceAlertEvents.stream().filter(item -> item.getEventId().equals(eventId)).findFirst().get();
    //        DatasourceAlertEvent datasourceAlertEvent = datasourceAlertEvents.stream().filter(item -> item.getEventId().equals(eventId)).findFirst().get();
    //        // 设置form表单提交的事件状态
    //        oriDatasourceAlertEvent.setAlertStatus(datasourceAlertEvent.getAlertStatus());
    //        AlarmStrategy alarmStrategy = convertAlarmStrategy(datasourceAlert, oriDatasourceAlertEvent, productEvent);
    //        alarmStrategy.setId(oriDatasourceAlertEvent.getAlarmStrategyId());
    //        alarmStrategyJoyaFeignService.update(alarmStrategy.getId(), alarmStrategy);
    //    }
    //    // form中不存在，数据库中存在，做更新或删除
    //    List<String> willDelCols = (List<String>) CollUtil.subtract(oriEventIds, eventIds);
    //    for (String eventId : willDelCols) {
    //        DatasourceAlertEvent datasourceAlertEvent = oriDatasourceAlertEvents.stream().filter(item -> item.getEventId().equals(eventId)).findFirst().get();
    //        // 先调用远程接口删除告警策略
    //        alarmStrategyJoyaFeignService.delete(datasourceAlertEvent.getAlarmStrategyId());
    //    }
    //    // form中存在，数据库中不存在，做新增
    //    List<String> willAddCols = (List<String>) CollUtil.subtract(eventIds, oriEventIds);
    //    for (String eventId : willAddCols) {
    //        // 获取选中事件
    //        ProductEvent productEvent = eventMap.get(eventId);
    //        DatasourceAlertEvent datasourceAlertEvent = datasourceAlertEvents.stream().filter(item -> item.getEventId().equals(eventId)).findFirst().get();
    //        AlarmStrategy alarmStrategy = convertAlarmStrategy(datasourceAlert, datasourceAlertEvent, productEvent);
    //        AlarmStrategy strategy = alarmStrategyJoyaFeignService.add(alarmStrategy);
    //        // 告警策略id写回 数据源告警事件
    //        datasourceAlertEvent.setAlarmStrategyId(strategy.getId());
    //    }
    //    return update(datasourceAlert.getId(), datasourceAlert);
    //}

    /**
     * 设置通知范围
     * @param datasourceAlert
     */
    private void setNoticeUser(DatasourceAlert datasourceAlert) {
        if (CollUtil.isNotEmpty(datasourceAlert.getUsers())) {
            String userIds = datasourceAlert.getUsers().stream().map(User::getId).collect(Collectors.joining(","));
            String userNames = datasourceAlert.getUsers().stream().map(User::getNickname).collect(Collectors.joining(","));
            datasourceAlert.setUserIds(userIds);
            datasourceAlert.setUserNames(userNames);
        }
        if (CollUtil.isNotEmpty(datasourceAlert.getRoles())) {
            String roleIds = datasourceAlert.getRoles().stream().map(Role::getId).collect(Collectors.joining(","));
            String roleNames = datasourceAlert.getRoles().stream().map(Role::getName).collect(Collectors.joining(","));
            datasourceAlert.setRoleIds(roleIds);
            datasourceAlert.setRoleNames(roleNames);
        }
        if (CollUtil.isNotEmpty(datasourceAlert.getProjects())) {
            String projectIds = datasourceAlert.getProjects().stream().map(Project::getId).collect(Collectors.joining(","));
            String projectNames = datasourceAlert.getProjects().stream().map(Project::getName).collect(Collectors.joining(","));
            datasourceAlert.setProjectIds(projectIds);
            datasourceAlert.setProjectNames(projectNames);
        }
    }

    /**
     * 把数据源产品事件对象ProductEvent转成DatasourceAlertEvent对象提供给前端使用
     * @return
     */
    public List<DatasourceAlertEvent> datasourceAlertEvents() {
        List<DatasourceAlertEvent> datasourceAlertEvents = new ArrayList<>();
        Product product = getProduct("sourceMetadata"); // 调用接口
        if (null == product) {
            throw new AppWarningException(Response.Code.Warning, "未初始化数据源中心产品数据");
        }
        // 获取全部事件
        List<ProductEvent> productEvents = getProductEvent(product.getId());
        if (CollUtil.isEmpty(productEvents)) {
            throw new AppWarningException(Response.Code.Warning, "未初始化数据源中心事件信息");
        }
        for (ProductEvent productEvent : productEvents) {
            DatasourceAlertEvent datasourceAlertEvent = new DatasourceAlertEvent();
            datasourceAlertEvent.setEventId(productEvent.getId());
            datasourceAlertEvent.setEventCode(productEvent.getCode());
            datasourceAlertEvent.setEventName(productEvent.getName());
            datasourceAlertEvent.setAlertLevel(AlertStatusEnum.getName(productEvent.getCode()));
            datasourceAlertEvent.setAlertStatus(true);
            datasourceAlertEvent.afterDbInit();
            datasourceAlertEvents.add(datasourceAlertEvent);
        }
        return datasourceAlertEvents;
    }

    public boolean telnet(String ip, int port, int timeoutMs) {
        TelnetClient telnet = new TelnetClient();
        try {
            telnet.connect(ip, port);
            telnet.setSoTimeout(timeoutMs);
            return true;
        } catch (IOException e) {
            // 连接失败
            log.error(e.getMessage(), e);
            return false;
        } finally {
            try {
                telnet.disconnect(); // 断开连接
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 根据数据源id 获取告警事件
     */
    public DatasourceAlertTemplate getDatasourceAlertEvents(String datasourceId,String datasourceName) {
        //捕获异常
        try {
            DatasourceAlertTemplate datasourceAlertTemplate = new DatasourceAlertTemplate();
            datasourceAlertTemplate.setDatasourceName(datasourceName);
            DatasourceAlert datasourceAlert = getService(DatasourceAlert.class).getQuery()
                    .lazys("datasourceAlertEvents")
                    .eq("datasourceInfoId", datasourceId)
                    .one();
            if(null == datasourceAlert){
                return datasourceAlertTemplate;
            }

            //获取事件
            List<DatasourceAlertEvent> datasourceAlertEvents = datasourceAlert.getDatasourceAlertEvents();
            //将告警事件名称拼接为字符串
            String eventNames = datasourceAlertEvents.stream().map(DatasourceAlertEvent::getEventName).collect(Collectors.joining(","));
            datasourceAlertTemplate.setEventName(eventNames);
            //通知方式转换
            String noticeWay = datasourceAlert.getNoticeWay();
            //用枚举类获取描述
            String descriptionByCode = NoticeWayEnum.getDescriptionByCode(noticeWay);
            datasourceAlertTemplate.setNoticeWay(descriptionByCode);
            //通知范围转换：user、role、project {个人："名称1,名称2",角色:"角色1，角色2",项目："项目1，项目2"}
            //获取用户名称
            String userNames = datasourceAlert.getUserNames();
            //获取角色名称
            String roleNames = datasourceAlert.getRoleNames();
            //获取项目名称
            String projectNames = datasourceAlert.getProjectNames();
            //拼接通知范围 构建成json格式
            JSONObject noticeTypeJson = new JSONObject();
            noticeTypeJson.put("个人", userNames);
            noticeTypeJson.put("角色", roleNames);
            noticeTypeJson.put("项目", projectNames);
            datasourceAlertTemplate.setNoticeType(noticeTypeJson.toJSONString());
            //频率控制转换
            Integer receiveControl = datasourceAlert.getReceiveControl();
            datasourceAlertTemplate.setReceiveControl(ReceiveControlEnum.getDescriptionByCode(receiveControl));
            datasourceAlertTemplate.setMsgFormat(datasourceAlert.getMsgFormat());
            //若频率控制为指定时段，则获取指定时段开始时间和结束时间 且将时间转换为字符串 格式为2024/02/21  00:00:00
            if (receiveControl == ReceiveControlEnum.FIXED_PERIOD.getCode()) {
                Date appointAlarmStartTime = datasourceAlert.getAppointAlarmStartTime();
                datasourceAlertTemplate.setAppointAlarmStartTime(DateUtil.format(appointAlarmStartTime, "yyyy/MM/dd HH:mm:ss"));
                Date appointAlarmEndTime = datasourceAlert.getAppointAlarmEndTime();
                datasourceAlertTemplate.setAppointAlarmEndTime(DateUtil.format(appointAlarmEndTime, "yyyy/MM/dd HH:mm:ss"));
            }
            //若通知方式为http，则获取请求方式、请求地址
            if (noticeWay.equals(NoticeWayEnum.HTTP.getCode())) {
                datasourceAlertTemplate.setRequestMethod(datasourceAlert.getRequestMethod());
                datasourceAlertTemplate.setRequestUrl(datasourceAlert.getRequestUrl());
            }
            return datasourceAlertTemplate;
        } catch (Exception e) {
            log.error("{}获取告警配置异常:{}",datasourceName, e.getMessage());
        }
        return null;
    }
}
