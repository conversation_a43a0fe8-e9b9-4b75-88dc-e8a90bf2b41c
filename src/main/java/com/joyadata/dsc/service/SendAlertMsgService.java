package com.joyadata.dsc.service;

import com.alibaba.fastjson.JSON;
import com.joyadata.cores.kafka.start.interfaces.IJoyaKafkaProducer;
import com.joyadata.cores.kafka.start.util.KafkaClientUtil;
import com.joyadata.dsc.model.datasoure.dto.AlarmMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * @ClassName SendAlertMsgService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/24 14:03
 * @Version 1.0
 **/
@Slf4j
@Service
public class SendAlertMsgService {

    /**
     * 告警中心TOPIC
     */
    private static final String ALERT_CENTER_TOPIC = "APP_EVENTS_V1_R2P1";
    private IJoyaKafkaProducer<String, String> kafkaTemplate = KafkaClientUtil.getProducer(String.class, String.class);

    public void sendMsg(AlarmMsg alarmMsg, String logMsg) {
        String jsonString = JSON.toJSONString(alarmMsg);
        log.info("{}：{}", logMsg, jsonString);
        try {
            kafkaTemplate.send(ALERT_CENTER_TOPIC, UUID.randomUUID().toString(), jsonString);
        } catch (Exception e) {
            log.error(logMsg, e);
        }
    }

    public AlarmMsg convertAlarmMsg(String id, String name, String tenantCode, String productId, String eventCode, Map<String, Object> data) {
        AlarmMsg alarmMsg = new AlarmMsg();
        alarmMsg.setBusinessId(id);
        alarmMsg.setBusinessName(name);
        alarmMsg.setDate(new Date());
        alarmMsg.setEventCode(eventCode);
        alarmMsg.setTenantCode(tenantCode);
        alarmMsg.setProductId(productId);
        alarmMsg.setData(data);
        return alarmMsg;
    }

}
