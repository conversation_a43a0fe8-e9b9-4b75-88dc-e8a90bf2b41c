package com.joyadata.dsc.service;

import com.joyadata.dsc.model.metadata.MetadataColumnCommon;
import com.joyadata.dsc.model.metadata.MetadataTableCommon;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.WhereCondition;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
public class MetadataColumnCommonService extends BaseService<MetadataColumnCommon> {

    /**
     * 根据id 批量修改字段
     * @param metadataColumnCommons
     * @return
     */
    public void batchUpdate(List<MetadataColumnCommon> metadataColumnCommons)   {
        metadataColumnCommons.forEach(columnCommon -> {
            EqCondition update = new EqCondition("id", columnCommon.getId());
            List<WhereCondition> whereConditions=new ArrayList<>();
            whereConditions.add(update);
            getService(MetadataColumnCommon.class).updateBy(whereConditions,columnCommon);
        });
    }


    public List<MetadataColumnCommon> getColsByTableIds(List<String >tableIds)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        List<MetadataColumnCommon> metadataColumns = getService(MetadataColumnCommon.class)
                .getQuery()
                .in("metadataTableCommonId", tableIds).list();
        if(metadataColumns ==null){
            metadataColumns=new ArrayList<>();
        }
        return metadataColumns;
    }
    public List<MetadataColumnCommon> getTablesByTableId(String tableId)  {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        List<MetadataColumnCommon> metadataColumns = getService(MetadataColumnCommon.class)
                .getQuery()
                .eq("metadataTableCommonId", tableId).list();
        if(metadataColumns ==null){
            metadataColumns=new ArrayList<>();
        }
        return metadataColumns;
    }

    //删除表id集合的字段
    public void deleteByTableId(List<MetadataColumnCommon> metadataColumnCommons) {
        metadataColumnCommons.forEach(columnCommon -> {
            columnCommon.setDelFlag(true);
            getService(MetadataColumnCommon.class).update(columnCommon);
        });
    }

}
