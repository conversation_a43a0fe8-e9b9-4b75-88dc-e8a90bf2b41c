package com.joyadata.dsc.audit.aspect;

import com.joyadata.dsc.audit.annotation.LogOperation;
import com.joyadata.dsc.model.record.RecordsOperation;
import com.joyadata.dsc.service.RecordsOperationService;
import com.joyadata.dsc.utils.SpELParserUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
@Slf4j
public class OperationLogAspect {

    @Autowired
    private RecordsOperationService recordsOperationService;

    // 拦截所有@LogOperation注解的方法
    @Around("@annotation(logOperation)")
    public Object logOperation(ProceedingJoinPoint joinPoint, LogOperation logOperation) throws Throwable {
        Object result = null;
        try {
            result = joinPoint.proceed();
            saveOperationLog(joinPoint, logOperation, result);
        } catch (Throwable e) {
            // 忽略
            throw e;
        }
        return result;
    }

    // 构建并保存操作记录
    private void saveOperationLog(ProceedingJoinPoint joinPoint, LogOperation logOperation,
                                  Object result) {
        // 关键点：从 joinPoint 解析 Method
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        RecordsOperation record = new RecordsOperation();

        try {
            // 解析 SpEL
            String datasourceId = SpELParserUtils.parse(
                    logOperation.datasourceId(),
                    method,
                    joinPoint.getArgs(),
                    result
            );
            String details = SpELParserUtils.parse(
                    logOperation.details(),
                    method,
                    joinPoint.getArgs(),
                    result
            );
            String operatorBeans = SpELParserUtils.parse(
                    logOperation.operatorBeans(),
                    method,
                    joinPoint.getArgs(),
                    result
            );
            record.setDatasourceId(datasourceId);
            record.setType(logOperation.type().getTypeName());
            record.setOperatorDetails(details);
            record.setOperatorBeans(operatorBeans);
            recordsOperationService.add(record);
        } catch (Exception e) {
            log.error("操作日志记录失败{}{}", e.getMessage(), e.getCause());
        }
    }

}