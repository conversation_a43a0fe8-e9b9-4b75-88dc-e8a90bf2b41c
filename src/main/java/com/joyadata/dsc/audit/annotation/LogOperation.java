package com.joyadata.dsc.audit.annotation;

import com.joyadata.dsc.enums.OperationEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作记录注解 （仅支持单个数据源的操作）
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogOperation {

    // 数据源id
    String datasourceId();

    // 操作类型
    OperationEnum type();

    // 操作内容（支持SpEL表达式，如"删除了用户#{{#user.id}}"）
    String details();

    /**
     * 操作对象以及业务id
     * jsonString
     */
    String operatorBeans() default "";
}