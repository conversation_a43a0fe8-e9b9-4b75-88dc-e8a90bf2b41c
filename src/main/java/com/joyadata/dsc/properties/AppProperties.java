package com.joyadata.dsc.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName AppPrpperties
 * @Description 应用yml中的属性
 * <AUTHOR>
 * @Date 2025/2/26 14:52
 * @Version 1.0
 **/
@Component
@ConfigurationProperties(prefix = "spring.profiles")
@Getter
@Setter
public class AppProperties {
    private String active;
}
