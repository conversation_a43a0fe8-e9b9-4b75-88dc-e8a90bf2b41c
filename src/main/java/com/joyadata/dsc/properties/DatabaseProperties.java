package com.joyadata.dsc.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName DatabaseProperties
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/2/26 15:15
 * @Version 1.0
 **/
@Component
@ConfigurationProperties(prefix = "database")
@Getter
@Setter
public class DatabaseProperties {
    private String pluginPath;
    private String kerberosPath;
    private String localPluginPath;
    private String localKerberosPath;
    private String pluginBasePath;
    private String alertMsgFormat;

    /**
     * Kerberos文件最大大小，默认10MB
     */
    private String kerberosFileMaxSize = "10MB";

    /**
     * Kerberos文件清理天数，默认30天
     */
    private Integer kerberosCleanupDays = 30;

    /**
     * 是否启用Kerberos文件备份，默认true
     */
    private Boolean kerberosBackupEnabled = true;
}
