package com.joyadata.dsc.scheduler;

import cn.hutool.core.collection.CollUtil;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduled;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduledComponent;
import com.joyadata.dsc.enums.AlertStatusEnum;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.dto.AlarmMsg;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectProgressVO;
import com.joyadata.dsc.service.DatasourceAlertService;
import com.joyadata.dsc.service.DatasourceInfoService;
import com.joyadata.dsc.service.SendAlertMsgService;
import com.joyadata.dsc.utils.TelnetUtils;
import com.joyadata.dsc.utils.jdbc.JdbcUrlBuilderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName NetworkConnectScheduler
 * @Description 数据源网络连接telnet定时任务
 * <AUTHOR>
 * @Date 2025/3/17 18:50
 * @Version 1.0
 **/
@Slf4j
@JoyaScheduledComponent
public class NetworkConnectScheduler {

    @Autowired
    private DatasourceAlertService datasourceAlertService;
    @Autowired
    private DatasourceInfoService datasourceInfoService;
    @Autowired
    private SendAlertMsgService sendAlertMsgService;

    @JoyaScheduled(fixedRate = 1 * 60 * 60 * 1000L, initialDelay = 5 * 1000)
    public void execute() {
        log.info("数据源网络连接telnet定时任务执行...");
        List<DatasourceAlert> datasourceAlerts = datasourceAlertService.setIgnoreTenantCode().getQuery().lazys("datasourceAlertEvents").list();
        if (CollUtil.isEmpty(datasourceAlerts)) {
            return;
        }
        List<String> datasourceInfoIds = datasourceAlerts.stream()
                .filter(item -> null != item.getDatasourceAlertEvents())
                .filter(item -> item.getDatasourceAlertEvents().stream().anyMatch(event -> AlertStatusEnum.B401001.name().equals(event.getEventCode())))
                .map(DatasourceAlert::getDatasourceInfoId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(datasourceInfoIds)) {
            return;
        }
        // 忽略租户查询全部数据源
        List<DatasourceInfo> datasourceInfos = datasourceInfoService
                .setIgnoreTenantCode()
                .getQuery()
                .withs("datasourceTypeName", "datasourceDriverName", "datasourceDriverVersion", "pluginName")
                .lazys("datasourceNodeList", "datasourceFieldData")
                .in("id", datasourceInfoIds)
                .eq("delFlag", 0)
                .list();

        if (CollUtil.isNotEmpty(datasourceInfos)) {
            for (DatasourceInfo datasourceInfo : datasourceInfos) {
                DatasourceInfo updateStatus = new DatasourceInfo();
                updateStatus.setId(datasourceInfo.getId());
                // 获取数据源对应的ip和port，如果数据源是集群则对每一个节点都进行telnet
                List<JdbcUrlBuilderUtil.HostPort> hostPortList = datasourceInfo.getHostPortList();
                if (CollUtil.isNotEmpty(hostPortList)) {
                    for (JdbcUrlBuilderUtil.HostPort hostPort : hostPortList) {
                        //boolean flag = datasourceAlertService.telnet(hostPort.getHost(), hostPort.getPort(), 3);
                        boolean flag = TelnetUtils.isReachable(hostPort.getHost(), hostPort.getPort());
                        if (flag) {
                            updateStatus.setStatus(DatasourceConnectProgressVO.SUCCESS);
                        } else {
                            updateStatus.setStatus(DatasourceConnectProgressVO.FAIL);
                            AlarmMsg alarmMsg = sendAlertMsgService.convertAlarmMsg(datasourceInfo.getId(),
                                    datasourceInfo.getDatasourceName(),
                                    datasourceInfo.getTenantCode(),
                                    null,
                                    AlertStatusEnum.B401001.name(), null);
                            alarmMsg.addAttr("datasourceId", datasourceInfo.getId());
                            alarmMsg.addAttr("dataName", datasourceInfo.getDatasourceName());
                            alarmMsg.addAttr("jdbcUrl", datasourceInfo.getJdbcUrl());
                            alarmMsg.addAttr("userId", datasourceInfo.getCreateBy());
                            alarmMsg.addAttr("ip", hostPort.getHost());
                            alarmMsg.addAttr("port", hostPort.getPort());
                            sendAlertMsgService.sendMsg(alarmMsg, "数据源主机网络连接失败告警消息");
                        }
                    }
                } else {
                    updateStatus.setStatus(DatasourceConnectProgressVO.FAIL);
                    AlarmMsg alarmMsg = sendAlertMsgService.convertAlarmMsg(datasourceInfo.getId(),
                            datasourceInfo.getDatasourceName(),
                            datasourceInfo.getTenantCode(),
                            null,
                            AlertStatusEnum.B401001.name(), null);
                    alarmMsg.addAttr("datasourceId", datasourceInfo.getId());
                    alarmMsg.addAttr("dataName", datasourceInfo.getDatasourceName());
                    alarmMsg.addAttr("jdbcUrl", datasourceInfo.getJdbcUrl());
                    alarmMsg.addAttr("userId", datasourceInfo.getCreateBy());
                    sendAlertMsgService.sendMsg(alarmMsg, "数据源主机网络连接失败告警消息");
                }
                datasourceInfoService.update(updateStatus);
            }
        }
    }
}
