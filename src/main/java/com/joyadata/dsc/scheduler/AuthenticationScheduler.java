package com.joyadata.dsc.scheduler;

import cn.hutool.core.collection.CollUtil;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduled;
import com.joyadata.cores.scheduled.start.annotaion.JoyaScheduledComponent;
import com.joyadata.dsc.enums.AlertStatusEnum;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.dto.AlarmMsg;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectProgressVO;
import com.joyadata.dsc.service.DatasourceAlertService;
import com.joyadata.dsc.service.DatasourceInfoService;
import com.joyadata.dsc.service.SendAlertMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName AuthenticationScheduler
 * @Description 数据源用户名密码定时任务
 * <AUTHOR>
 * @Date 2025/3/17 18:59
 * @Version 1.0
 **/
@Slf4j
@JoyaScheduledComponent
public class AuthenticationScheduler {

    @Autowired
    private DatasourceInfoService datasourceInfoService;
    @Autowired
    private DatasourceAlertService datasourceAlertService;
    @Autowired
    private SendAlertMsgService sendAlertMsgService;

    @JoyaScheduled(fixedRate = 6 * 60 * 60 * 1000L, initialDelay = 5 * 1000)
    public void execute() {
        log.info("数据源用户名密码定时任务执行...");
        List<DatasourceAlert> datasourceAlerts = datasourceAlertService.setIgnoreTenantCode().getQuery().lazys("datasourceAlertEvents").list();
        if (CollUtil.isEmpty(datasourceAlerts)) {
            return;
        }
        List<String> datasourceInfoIds = datasourceAlerts.stream()
                .filter(item -> null != item.getDatasourceAlertEvents())
                .filter(item -> item.getDatasourceAlertEvents().stream().anyMatch(event -> AlertStatusEnum.B401002.name().equals(event.getEventCode())))
                .map(DatasourceAlert::getDatasourceInfoId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(datasourceInfoIds)) {
            return;
        }
        List<DatasourceInfo> datasourceInfos = datasourceInfoService
                .setIgnoreTenantCode()
                .getQuery()
                .withs("datasourceTypeName", "datasourceDriverName", "datasourceDriverVersion", "pluginName")
                .lazys("datasourceNodeList", "datasourceFieldData")
                .in("id", datasourceInfoIds)
                .eq("delFlag", 0)
                .list();
        if (CollUtil.isNotEmpty(datasourceInfos)) {
            for (DatasourceInfo datasourceInfo : datasourceInfos) {
                DatasourceConnectProgressVO datasourceConnectProgressVO = datasourceInfoService.connectByDatasourceInfo(datasourceInfo);
                if (DatasourceConnectProgressVO.FAIL == datasourceConnectProgressVO.getStatus()) {
                    AlarmMsg alarmMsg = sendAlertMsgService.convertAlarmMsg(datasourceInfo.getId(),
                            datasourceInfo.getDatasourceName(),
                            datasourceInfo.getTenantCode(),
                            null,
                            AlertStatusEnum.B401002.name(), null);
                    alarmMsg.addAttr("datasourceId", datasourceInfo.getId());
                    alarmMsg.addAttr("dataName", datasourceInfo.getDatasourceName());
                    alarmMsg.addAttr("jdbcUrl", datasourceInfo.getJdbcUrl());
                    alarmMsg.addAttr("userId", datasourceInfo.getCreateBy());
                    sendAlertMsgService.sendMsg(alarmMsg, "数据源用户名密码错误告警消息");
                }
            }
        }
    }
}
