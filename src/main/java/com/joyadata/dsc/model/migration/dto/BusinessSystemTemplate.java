package com.joyadata.dsc.model.migration.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * sheet1:业务系统模板
 * <AUTHOR>
 */
@Data
public class BusinessSystemTemplate {

    /**
     * 业务系统名称
     */
    @ExcelProperty(value = "系统名称(必填:不重复)")
    private String businessName;

    /**
     * 简称
     */
    @ExcelProperty(value = "简称")
    private String simpleName;

    /**
     * 所属部门
     */
    @ExcelProperty(value = "所属部门(必填)填写部门名称(code)")
    private String departName;

    /**
     * 负责人
     */
    @ExcelProperty(value = "负责人(必填)填写真实姓名(用户名）")
    private String manager;
    /**
     * 访问地址
     */
    @ExcelProperty(value = "访问地址")
    private String interviewAddress;

    /**
     * 系统状态
     */
    @ExcelProperty(value = "目前状况(必填):在用、停用、在建、拟停用、其他")
    private String businessStatus;

    /**
     * 系统类型
     */
    @ExcelProperty(value = "系统类型(必填): 业务系统、安全系统、分析系统、数据系统、其他")
    private String businessType;

    /**
     * 业务系统安全等级
     */
    @ExcelProperty(value = "系统等级:L1、L2、L3、L4、L5等")
    private String securityLevel;



    /**
     * 业务简述
     */
    @ExcelProperty(value = "业务简述")
    protected String remark;

}
