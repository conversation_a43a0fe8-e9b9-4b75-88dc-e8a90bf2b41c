package com.joyadata.dsc.model.migration.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * sheet 5：数据源告警
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/24 16:01
 */
@Data
public class DatasourceAlertTemplate {

    /**
     * 数据源名称
     */
    @ExcelProperty("数据源名称")
    private String datasourceName;

    /**
     * 告警事件，以逗号分隔
     */
    @ExcelProperty("告警事件")
    private String eventName;

    /**
     * 通知方式：通知方式：1:邮箱,2:短信,3:Http,4:企业微信
     */
    @ExcelProperty("通知方式")
    private String noticeWay;
    /**
     * 通知范围：user、role、project {个人："",角色:"",项目：""}
     */
    @ExcelProperty("通知范围")
    private String noticeType;
    /**
     * 频率控制1:每次触发告警时候发送，2:当日首次触发告警时候发送, 3:每小时首次触发发送, 4:指定时段发送
     */
    @ExcelProperty("频率控制")
    private String receiveControl;
    /**
     * 信息格式转换
     */
    @ExcelProperty("信息格式转换")
    private String msgFormat;
    /**
     * 指定时段开始时间
     */
    @ExcelProperty("指定时段开始时间")
    private String appointAlarmStartTime;
    /**
     * 指定时段结束时间
     */
    @ExcelProperty("指定时段结束时间")
    private String appointAlarmEndTime;

    @ExcelProperty("请求方式")
    private String requestMethod;
    /**
     * 请求地址
     */
    @ExcelProperty("请求地址")
    private String requestUrl;


}
