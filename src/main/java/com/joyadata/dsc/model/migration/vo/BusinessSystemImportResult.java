package com.joyadata.dsc.model.migration.vo;

import com.joyadata.dsc.model.business.BusinessSystem;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据源导入业务系统导入结果
 * <AUTHOR>
 */
@Data
public class BusinessSystemImportResult {

    /**
     * 新增
     */
    private List<BusinessSystem> addedList = new ArrayList<>();


    /**
     * 更新
     */
    private List<BusinessSystem> updatedList = new ArrayList<>();

    /**
     * 有误 key 为业务系统名称 value 为错误信息
     */
    private Map<String,String> errBusinessSystemMap = new HashMap<>();
}
