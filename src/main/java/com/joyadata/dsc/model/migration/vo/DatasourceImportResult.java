package com.joyadata.dsc.model.migration.vo;

import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.DatasourceNode;
import com.joyadata.dsc.model.migration.dto.DatasourceInfoTemplate;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据源导入结果
 * <AUTHOR>
 */
@Data
public class DatasourceImportResult {


    /**
     * 导入状态
     */
    private Integer status;

    /**
     * 导入失败原因
     */
    private String errMsg;
    /**
     * 导入数据源信息
     */
    private DatasourceInfoTemplate importDatum;

    /**
     * 构建数据源信息
     */
    private DatasourceInfo datasourceInfo;


    /**
     * 节点信息
     */
    private List<DatasourceNode> node= new ArrayList<>();

    /**
     * 新增还是更新标识
     */
    private String type;






}
