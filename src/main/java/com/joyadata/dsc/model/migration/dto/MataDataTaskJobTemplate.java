package com.joyadata.dsc.model.migration.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import lombok.Data;

import java.sql.Date;

/**
 * sheet1:采集配置-定时配置
 * <AUTHOR>
 */
@Data
public class MataDataTaskJobTemplate {

    /**
     * 数据源名称
     */
    @ExcelProperty(value = "数据源名称")
    private String datasourceName;

    /**
     * 是否开启定时采集：0开启 1：关闭
     *{@link com.joyadata.dsc.enums.SwitchStatus}
     *
     */
    @ExcelProperty(value = "是否开启定时采集")
    private String isTiming;


    /**
     * 采集类型
     * 1: 定时采集使用
     */
    @ExcelProperty(value = "生效开始时间")
    private String effectiveStartDate;

    /**
     * 采集类型
     * 1: 定时采集使用
     */
    @ExcelProperty(value = "生效结束时间")
    private String effectiveEndDate;
    /**
     *
     * 频率类型：推荐0 自定义1
     */
    @ExcelProperty(value = "频率类型：推荐 自定义")
    private String cronType;
    /**
     *
     * 定时采集的频率
     */
    @ExcelProperty(value = "定时配置（corn表达式）")
    private String executionFrequency;


}
