package com.joyadata.dsc.model.migration;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 导入导出记录表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_data_migration_detail", label = "dsc_data_migration_detail", comment = "数据源迁移明细表")
@JoyadataIndexs({
        @JoyadataIndex(
                type = "UNIQUE INDEX",
                name = "uk_dsc_data_migration_detail_te_pr_da_de",
                columns = {"project,tenant_code,name,del_flag"},
                comment = "导入导出名称",
                errorMsg = "导入导出名称不能重复"),
        @JoyadataIndex(
                type = "UNIQUE INDEX",
                name = "uk_dsc_data_migration_detail_te_pr_id",
                columns = {"project,tenant_code,id,del_flag"},
                comment = "数据源迁移id唯一",
                errorMsg = "数据源迁移id不能重复")
})
public class DataMigrationDetail extends BaseBean {

    /**
     * 数据源名称
     */
    @JoyadataColumn(label = "数据源名称", nullable = false, length = 100)
    private String datasourceName;
    /**
     * 数据源id
     */
    @JoyadataColumn(label = "数据源id", nullable = false, length = 100)
    private String datasourceId;

    /**
     * 业务系统名称
     */
    @JoyadataColumn(label = "业务系统名称")
    private String businessName;

    /**
     * 业务系统id
     */
    @JoyadataColumn(label = "业务系统id")
    private String businessId;

    /**
     * 数据源迁移导入导出类id
     */
    @JoyadataColumn(label = "数据源迁移导入导出类id")
    private String dataMigrationId;

    /**
     * 数据源迁移导入导出状态
     */
    @JoyadataColumn( label = "status", comment = "数据源迁移导入导出状态")
    private Integer status;
    /**
     * 数据源迁移失败原因
     */
    @JoyadataColumn( label = "error_msg", comment = "数据源迁移失败原因")
    private String errorMsg;

    /**
     * 类型 业务系统 还是数据源
     */
    @JoyadataColumn( label = "type", comment = "数据源迁移类型")
    private String type;
    /**
     * 数据源导入成功数
     */
    @JoyadataColumn( label = "success_count", comment = "数据源导入成功数")
    private Integer successCount;
    /**
     * 数据源导入失败数
     */
    @JoyadataColumn( label = "fail_count", comment = "数据源导入失败数")
    private Integer failCount;






}
