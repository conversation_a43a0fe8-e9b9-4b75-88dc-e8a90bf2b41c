package com.joyadata.dsc.model.migration;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataIndex;
import com.joyadata.util.sql.annotation.JoyadataIndexs;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数据源迁移任务
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_data_migration", label = "dsc_data_migration", comment = "数据源迁移表")
@JoyadataIndexs({
        @JoyadataIndex(
                type = "UNIQUE INDEX",
                name = "uk_dsc_data_migration_te_pr_da_de",
                columns = {"project,tenant_code,name,del_flag"},
                comment = "导入导出名称",
                errorMsg = "导入导出名称不能重复"),
        @JoyadataIndex(
                type = "UNIQUE INDEX",
                name = "uk_dsc_data_migration_te_pr_id",
                columns = {"project,tenant_code,id,del_flag"},
                comment = "数据源迁移id唯一",
                errorMsg = "数据源迁移id不能重复")
})
public class DataMigration extends BaseBean {
    /**
     * 数据源迁移导入导出名称
     */
    @JoyadataColumn( label = "name", comment = "数据源迁移导入导出名称")
    private String name;
    /**
     * 数据源迁移导入导出类型 import or export
     */
    @JoyadataColumn( label = "type", comment = "数据源迁移导入导出类型")
    private String type;
    /**
     * 数据源迁移任务导入导出状态
     */
    @JoyadataColumn( label = "status", comment = "数据源迁移任务导入导出状态")
    private Integer status;

    /**
     * 数据源迁移导入导出开始时间
     */
    @JoyadataColumn( label = "start_time", comment = "数据源迁移导入导出开始时间")
    private Date startTime;
    /**
     * 数据源迁移导入导出结束时间
     */
    @JoyadataColumn( label = "end_time", comment = "数据源迁移导入导出结束时间")
    private Date endTime;
    /**
     * 数据源迁移导入导出成功数
     */
    @JoyadataColumn( label = "success_count", comment = "数据源迁移导入导出成功数")
    private Integer successCount;
    /**
     * 数据源迁移导入导出失败数
     */
    @JoyadataColumn( label = "fail_count", comment = "数据源迁移导入导出失败数")
    private Integer failCount;

    /**
     * 数据源迁移导入导出文件路径
     */
    @JoyadataColumn( label = "file_path", comment = "数据源迁移导入导出文件路径")
    private String filePath;

    /**
     * 数据源迁移导入是模式：跳过还是覆盖
     * 0 跳过 1 覆盖
     */
    @JoyadataColumn( label = "skip_or_cover", comment = "数据源迁移导入是模式：跳过还是覆盖")
    private Integer skipOrCover;

    /**
     * 数据源迁移导入业务系统成功数
     *
     */
    @JoyadataColumn( label = "business_success_count", comment = "数据源迁移导入业务系统成功数")
    private Integer businessSuccessCount;
    /**
     * 数据源迁移导入业务系统失败数
     */
    @JoyadataColumn( label = "business_fail_count", comment = "数据源迁移导入业务系统失败数")
    private Integer businessFailCount;




}
