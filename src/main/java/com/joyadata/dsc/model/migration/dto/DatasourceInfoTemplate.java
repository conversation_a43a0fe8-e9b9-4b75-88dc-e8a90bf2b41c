package com.joyadata.dsc.model.migration.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * sheet 2：数据源登记
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/24 16:01
 */
@Data
public class DatasourceInfoTemplate {
    @ExcelProperty("数据源类型")
    private String datasourceTypeName;

    @ExcelProperty("数据源名称")
    private String datasourceName;

    @ExcelProperty("数据源ID：当导入的数据源名称平台不存在时，自动生成此ID")
    private String datasourceId;

    @ExcelProperty("驱动版本")
    private String driverVersion;

    @ExcelProperty("业务系统")
    private String businessName;

    @ExcelProperty("负责人")
    private String owner;

    @ExcelProperty("JDBC URL (JDBC URL 和服务器地址只填其中一个）")
    private String jdbcUrl;

    @ExcelProperty("服务器地址（填写多个时英文逗号隔开：IP:端口,IP:端口）")
    private String hostPort;

    @ExcelProperty("节点名")
    private String nodeName;

    @ExcelProperty("生效节点")
    private String effectiveNode;

    @ExcelProperty("数据库：非必填")
    private String dbName;

    @ExcelProperty("Schema")
    private String schema;

    @ExcelProperty("用户名")
    private String username;

    @ExcelProperty("密码")
    private String password;

    @ExcelProperty("备注：非必填")
    private String remark;

    @ExcelProperty("高级配置：key/value的json")
    private String advancedConfig;
}
