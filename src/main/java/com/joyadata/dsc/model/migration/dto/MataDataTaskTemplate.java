package com.joyadata.dsc.model.migration.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * sheet1:采集配置
 * <AUTHOR>
 */
@Data
public class MataDataTaskTemplate {

    /**
     * 数据源名称
     */
    @ExcelProperty(value = "数据源名称")
    private String datasourceName;

    /**
     * 采集范围:一个数据源名称只有一个范围类型
     */
    @ExcelProperty(value = "采集范围:一个数据源名称只有一个范围类型")
    private String acquisitionScope;

    /**
     * 库名
     */
    @ExcelProperty(value = "库名")
    private String dbName;
    /**
     * 模式
     */
    @ExcelProperty(value = "模式")
    private String schemaName;

    /**
     * 表名
     */
    @ExcelProperty(value = "表名")
    private String tableName;

    /**
     * 索引表名
     */
    @ExcelProperty(value = "索引表名")
    private String indexTableName;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 采集对象（主要对采集范围是库选择，黑白名单，快速选表配置生效）
     */
    @ExcelProperty(value = "采集对象（主要对采集范围是库选择，黑白名单，快速选表配置生效）")
    private String taskObject;

    /**
     *
     * 采集方式
     */
    @ExcelProperty(value = "采集方式：1全量 2表增量 3字段增量")
    private String taskWay;

    /**
     * 黑名单
     */
    @ExcelProperty(value = "黑名单")
    private String blacklist;

    /**
     * 白名单
     */
    @ExcelProperty(value = "白名单")
    private String whitelist;

    /**
     * 是否递归采集：文件使用 1 是 0否
     */
    @ExcelProperty(value = "是否递归采集：文件使用 1 是 0否")
    private Integer isRecursion;

    /**
     * 文件采集路径
     */
    @ExcelProperty(value = "文件采集路径")
    private String filePath;

    /**
     * 文件采集指定路径
     */
    @ExcelProperty(value = "文件采集指定路径")
    private String filePathSpecified;

    /**
     * 采集层级
     */
    @ExcelProperty(value = "采集层级")
    private  Integer maxLevel;

}
