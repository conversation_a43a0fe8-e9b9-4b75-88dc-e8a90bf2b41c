package com.joyadata.dsc.model.migration.vo;


import com.joyadata.dsc.model.migration.DataMigrationDetail;
import com.joyadata.dsc.model.migration.dto.*;
import lombok.Data;

import java.util.List;

/**
 * 导出实体
 * <AUTHOR>
 */
@Data
public class ExportDataResultVO {
    /**
     * 业务系统
     */
    private BusinessSystemTemplate systemTemplate;
    /**
     * 数据源信息
     */
    private List<DatasourceInfoTemplate> datasourceInfoTemplates;
    /**
     * 状态 1：失败 0：成功
     */
    private Integer status;

    /**
     * 数据迁移详情
     */
    private DataMigrationDetail dataMigrationDetail;

    /**
     * 采集配置信息
     */
    private List<MataDataTaskTemplate> mataDataTaskTemplates;

    /**
     * 采集定时配置
     */
    private MataDataTaskJobTemplate mataDataTaskJobTemplate;

    /**
     * 数据源告警配置
     */
    private DatasourceAlertTemplate datasourceAlertTemplate;
}
