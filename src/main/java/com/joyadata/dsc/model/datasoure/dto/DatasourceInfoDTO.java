package com.joyadata.dsc.model.datasoure.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * datasourceInfo DTO
 */
@Data
public class DatasourceInfoDTO {
    private Long id;
    /**
     * 数据源信息业务主键（UUID）
     * 注意：为了适配带kerberos的数据源新增之前需要先连接测试，所以【新增时】使用到的datasourceInfoId是执行了连接测试之后返回的
     */
    private String datasourceId;

    /**
     * 外键：数据源分类目录id
     */
    private Long catalogueId;

    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    private String dataType;

    /**
     * 数据源版本 如1.x, 0.9, 创建下的实例可能会没有版本号
     */
    private String dataVersion;

    /**
     * 数据源名称
     */
    private String dataName;

    /**
     * 数据源描述
     */
    private String dataDesc;

    /**
     * 数据源连接信息, 不同数据源展示连接信息不同, 保存为json
     */
    private String linkJson;

    /**
     * 数据源填写的表单信息, 保存为json, key键要与表单的name相同
     */
    private String dataJson;

    /**
     * 连接状态 0-连接失败, 1-正常
     */
    private Integer status;

    /**
     * 是否有meta标志 0-否 1-是
     */
    private Integer isMeta;

    /**
     * 数据源类型编码
     */
    private Integer dataTypeCode;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 是否删除,1删除，0未删除
     */
    private Integer isDeleted;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime firstAuthorizationTime;

    private LocalDateTime newAuthorizationTime;

    /**
     * 数据库版本
     */
    private String dbVersion;

    /**
     * 业务系统uuid
     */
    private String businessUuid;

    /**
     * 字符集
     */
    private String characterSet;

    /**
     * 时区
     */
    private String dsTimeZone;

    /**
     * ip
     */
    private String ip;

    private String port;

    private String advancedConfiguration;

}
