package com.joyadata.dsc.model.datasoure;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.*;

/**
 * 数据源配置数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JoyadataTable(name = "dsc_datasource_field_data", label = "dsc_datasource_field_data", comment = "数据源配置数据")
public class DatasourceFieldData extends BaseBean {

    @JoyadataColumn(label = "数据源节点的id")
    private String datasourceNodeId;

    @JoyadataColumn(label = "属性的 label")
    private String label;

    @JoyadataColumn(label = "属性的 value",length = 2048)
    private String value;
}
