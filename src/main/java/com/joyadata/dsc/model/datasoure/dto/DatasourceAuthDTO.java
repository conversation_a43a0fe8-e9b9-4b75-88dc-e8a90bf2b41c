package com.joyadata.dsc.model.datasoure.dto;

import com.joyadata.dsc.model.datasoure.vo.DatasourceAuthVO;
import com.joyadata.model.BaseBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 数据源授权
 * <AUTHOR>
 * @date 2024/11/7
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DatasourceAuthDTO extends BaseBean {

    /**
     * 数据源信息业务主键（UUID）
     */
    private String datasourceInfoId;

    /**
     * 数据源信息业务主键（UUID）集合
     */
    private List<String> datasourceInfoIds;

    /**
     * 项目id集合
     */
    private List<String> projectIds;

    /**
     * 库名
     */
    private String dbName;

    /**
     * 模式
     */
    private String schemaName;

    /**
     * 表名
     */
    private String metadataTableName;

    /**
     * 表类型
     */
    private String metadataTableType;

    /**
     * 授权类型
     */
    private String authType;

    /**
     * 授权库表对象
     */
    private List<DatasourceAuthVO> datasourceAuths;

    /**
     * 快速选表
     */
    private List<String> tableNames;

}
