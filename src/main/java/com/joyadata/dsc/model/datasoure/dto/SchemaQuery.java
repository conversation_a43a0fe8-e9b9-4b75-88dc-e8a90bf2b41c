package com.joyadata.dsc.model.datasoure.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class SchemaQuery extends DatasourceInfoSaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 数据源id(已经保存过数据源后获取schema使用)
     */
    private String datasourceId;

    /**
     * 模式名称
     */
    private String schemaName;

    /**
     * 分页大小
     */
    private Integer pager;

    /**
     * 当前页数
     */
    private Integer page;
}
