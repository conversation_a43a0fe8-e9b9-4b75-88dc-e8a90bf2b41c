package com.joyadata.dsc.model.datasoure.vo;

import com.joyadata.cms.model.Role;
import com.joyadata.cms.model.User;
import com.joyadata.csc.model.AlarmRecord;
import com.joyadata.tms.model.Project;
import lombok.Data;

import java.util.List;

/**
 * @ClassName AlarmRecordVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/14 13:40
 * @Version 1.0
 **/
@Data
public class AlarmRecordVO extends AlarmRecord {
    private String datasourceInfoId;
    private String datasourceInfoName;
    private String eventName;
    private String noticeWay;
    private String noticeWayName;
    private List<User> users;
    private List<Role> roles;
    private List<Project> projects;
}
