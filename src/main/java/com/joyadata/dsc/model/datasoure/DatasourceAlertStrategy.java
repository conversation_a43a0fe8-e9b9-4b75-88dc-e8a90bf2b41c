package com.joyadata.dsc.model.datasoure;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;

/**
 * @ClassName DatasourceAlertStrategy
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/11 16:51
 * @Version 1.0
 **/
@Data
@JoyadataTable(name = "dsc_datasource_alert_strategy", label = "dsc_datasource_alert_strategy", comment = "数据源告警策略")
public class DatasourceAlertStrategy extends BaseBean {
    @JoyadataColumn(label = "告警配置id", nullable = false)
    private String datasourceAlertId;
    @JoyadataColumn(label = "告警策略id", nullable = false)
    private String alarmStrategyId;

    @JoyadataColumn(label = "通知范围", comment = "通知范围：user、role、project、http", nullable = false)
    private String noticeType;
}
