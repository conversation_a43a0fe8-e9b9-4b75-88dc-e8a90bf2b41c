package com.joyadata.dsc.model.datasoure;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import com.joyadata.util.sql.enums.AGG;
import com.joyadata.util.sql.enums.ConditionType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 数据源类型表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_datasource_type", label = "dsc_datasource_type", comment = "数据源类型表", isPublic = true, isTenant = false)
public class DatasourceType extends BaseBean {

    @JoyadataColumn(label = "数据源类型", comment = "MySQL/ORACLE")
    private String dataType;

    @JoyadataColumn(label = "数据源权重")
    private BigDecimal weight;

    @JoyadataColumn(label = "数据源logo图片地址", length = 255)
    private String imgUrl;

    @JoyadataColumn(label = "datatype唯一的标识符")
    private Integer dataTypeCode;

    @JoyadataColumn(label = "数据源分类栏主键id")
    private String datasourceClassifyId;

    @JoyadataJoin(label = "数据源一级目录名称", targetBean = DatasourceClassify.class, targetColumn = "id", selfColumn = "datasourceClassifyId", valueColumn = "classifyName")
    private String datasourceClassifyName;

    @JoyadataAggJoin(label = "该类型下的条数", targetBean = DatasourceInfo.class, targetColumn = "datasourceTypeId", selfColumn = "id", agg = AGG.COUNT, valueColumn = "datasourceTypeId",
            conditions = "del_flag=0", conditionType = ConditionType.EQ)
    private Integer datasourceInfoSize;

    @JoyadataAggJoin(label = "该类型下成功的条数", targetBean = DatasourceInfo.class, targetColumn = "datasourceTypeId", selfColumn = "id", agg = AGG.COUNT, valueColumn = "datasourceTypeId",
            conditions = {"del_flag=0","status=1"}, conditionType = ConditionType.EQ)
    private Integer datasourceInfoSuccessSize;

    @JoyadataOne2Many(comment = "数据源信息", targetBean = DatasourceInfo.class, targetClounm = "datasourceTypeId", selfColumn = "id", conditions = {"del_flag=0"}, withs = "metadataDbNames")
    private List<DatasourceInfo> datasourceInfos;

    @JoyadataOne2Many(comment = "驱动信息", targetBean = DatasourceDriver.class, targetClounm = "datasourceTypeId", selfColumn = "id", conditions = {"del_flag=0"})
    private List<DatasourceDriver> datasourceDrivers;

}
