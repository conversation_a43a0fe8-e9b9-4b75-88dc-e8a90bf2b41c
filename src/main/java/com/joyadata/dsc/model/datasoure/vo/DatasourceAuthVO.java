package com.joyadata.dsc.model.datasoure.vo;

import com.joyadata.dsc.model.datasoure.DatasourceAuth;
import com.joyadata.model.BaseBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 数据源授权
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DatasourceAuthVO extends BaseBean {

    /**
     * 数据源信息业务主键（UUID）
     */
    private String datasourceInfoId;

    /**
     * 数据源类型授权类型：db、table
     */
    private String authType;


    /**
     * 库名
     */
    private String dbName;

    /**
     * 模式
     */
    private String schemaName;

    /**
     * 表id
     */
    private String metadataTableCommonId;
    /**
     * 表名称
     */
    private String metadataTableName;
    /**
     * 表类型
     */
    private String metadataTableType;


    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目id集合
     */
    private List<DatasourceAuth> datasourceAuths;
    /**
     * 是否被授权标识
     */
    private Boolean isAuth;

    /**
     * 项目id集合
     */
    private List<String> projectIds;

    /**
     * 是否快速选中
     */
    private Boolean isSelect;


    /**
     * uuid 前端使用
     */
    private String uuid;
}
