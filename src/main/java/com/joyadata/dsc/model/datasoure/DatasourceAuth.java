package com.joyadata.dsc.model.datasoure;

import com.joyadata.dsc.model.metadata.MetadataTableCommon;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataJoin;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源授权
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_datasource_auth", label = "dsc_datasource_auth", comment = "数据源授权表")
public class DatasourceAuth extends BaseBean {

    /**
     * 数据源信息业务主键（UUID）
     */
    @JoyadataColumn(label = "数据源类型id", nullable = false)
    private String datasourceInfoId;

    /**
     * 数据源类型授权类型：db、table
     */
    @JoyadataColumn(label = "数据源类型授权类型")
    private String authType;


    /**
     * 库名
     */
    @JoyadataColumn(label = "库名")
    private String dbName;

    /**
     * 模式
     */
    @JoyadataColumn(label = "模式")
    private String schemaName;

    /**
     * 表id
     */
    @JoyadataColumn(label = "表id")
    private String metadataTableCommonId;

    /**
     * 项目id
     */
    @JoyadataColumn(label = "项目id")
    private String projectId;


    @JoyadataJoin(label = "表名称", targetBean = MetadataTableCommon.class, targetColumn = "id", selfColumn = "metadataTableCommonId", valueColumn = "name")
    private String metadataTableName;

    @JoyadataColumn(label = "表类型")
    private String metadataTableType;
}
