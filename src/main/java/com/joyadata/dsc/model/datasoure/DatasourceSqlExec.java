package com.joyadata.dsc.model.datasoure;


import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源sql查询
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_datasource_sql_exec", label = "dsc_datasource_sql_exec", comment = "数据源sql执行历史")
public class DatasourceSqlExec extends BaseBean {

    /**
     * 数据源信息业务主键（UUID）
     */
    @JoyadataColumn(label = "数据源类型id", nullable = false)
    private String datasourceInfoId;

    /**
     * sql查询id
     */
    @JoyadataColumn(label = "sql查询id", nullable = false)
    private String sqlId;


    /**
     * sql执行状态
     */
    @JoyadataColumn(label = "sql执行状态", nullable = false)
    private int sqlStatus;

    /**
     * sql执行结果时长
     */
    @JoyadataColumn(label = "sql执行结果时长", nullable = false)
    private String sqlExecTime;

    /**
     * sql执行结果日志
     */
    @JoyadataColumn(label = "sql执行结果日志")
    private String sqlExecLog;

    /**
     * sql
     */
    @JoyadataColumn(label = "sql", nullable = false)
    private String sql;
    /**
     * 库名
     */
    @JoyadataColumn(label = "库名")
    private String dbName;

    /**
     * 模式名称
     */
    @JoyadataColumn(label = "模式名称")
    private String schemaName;

}
