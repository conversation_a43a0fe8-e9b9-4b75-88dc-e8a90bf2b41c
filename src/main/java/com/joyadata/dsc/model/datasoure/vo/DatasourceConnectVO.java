package com.joyadata.dsc.model.datasoure.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * 连接信息 vo
 * 1. 主机网络连接
 * 2. 用户名密码
 * 3. 获取最大连接数
 * 4. 获取元数据权限
 * 5. 获取字段权限
 */
@Data
public class DatasourceConnectVO {

    public static final String HOST_NETWORK = "hostNetwork";

    public static final String USERNAME_PASSWORD = "usernamePassword";

    public static final String MAX_CONNECTION = "maxConnection";

    public static final String METADATA_PERMISSION = "metadataPermission";

    public static final String FIELD_PERMISSION = "fieldPermission";

    public static final String SUCCESS = "success";

    public static final String FAIL = "error";

    public static final String SKIP = "skip";

    public DatasourceConnectVO() {

    }

    public DatasourceConnectVO(String type) {
        this.type = type;
        this.checkStatus = SKIP;
    }

    /**
     * 连接类型
     */
    private String type;

    /**
     * 连接状态：success连接正常，error连接失败
     */
    private String checkStatus;

    /**
     * 连接失败信息
     */
    private String msg;

    /**
     * 数据源信息业务主键（UUID）
     */
    private String datasourceInfoId;

    /**
     * 构建一个完整的返回
     */
    public static List<DatasourceConnectVO> buildDatasourceConnectVO() {
        List<DatasourceConnectVO> result = new ArrayList<>(5);
        DatasourceConnectVO hostNetwork = new DatasourceConnectVO(DatasourceConnectVO.HOST_NETWORK);
        DatasourceConnectVO usernamePassword = new DatasourceConnectVO(DatasourceConnectVO.USERNAME_PASSWORD);
        DatasourceConnectVO maxConnection = new DatasourceConnectVO(DatasourceConnectVO.MAX_CONNECTION);
        DatasourceConnectVO metadataPermission = new DatasourceConnectVO(DatasourceConnectVO.METADATA_PERMISSION);
        DatasourceConnectVO fieldPermission = new DatasourceConnectVO(DatasourceConnectVO.FIELD_PERMISSION);
        result.add(hostNetwork);
        result.add(usernamePassword);
        result.add(maxConnection);
        result.add(metadataPermission);
        result.add(fieldPermission);
        return result;
    }
}
