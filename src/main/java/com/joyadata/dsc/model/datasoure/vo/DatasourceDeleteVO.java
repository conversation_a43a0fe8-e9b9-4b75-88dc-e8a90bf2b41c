package com.joyadata.dsc.model.datasoure.vo;

import com.joyadata.model.BaseBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DatasourceDeleteVO extends BaseBean {

    /**
     * 数据源 id
     */
    private String datasourceInfo;

    /**
     * 数据源名称
     */
    private String datasourceName;

    /**
     * 数据源类型名称
     */
    private String datasourceTypeName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 产品名称
     */
    private String productName;

}
