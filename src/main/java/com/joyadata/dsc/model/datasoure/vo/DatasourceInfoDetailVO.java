package com.joyadata.dsc.model.datasoure.vo;

import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class DatasourceInfoDetailVO {

    /**
     * 数据库数量
     */
    private Integer dbSize;

    /**
     * 数据库/模式 类型
     * 1 数据库 db
     * 2 模式 schema
     * 3 数据库和模式 dbAndSchema
     */
    private String dbAndSchemaType;

    private List<DatasourceInfoDBDetailVO> dbList;

    public Integer getDbSize() {
        if (dbList != null) {
            return dbList.isEmpty() ? 0 : dbList.size() - 1;
        }
        return 0;
    }

    public static class DbAndSchemaType {

        public static final String TYPE_DB = "db";

        public static final String TYPE_SCHEMA = "schema";

        public static final String TYPE_DB_AND_SCHEMA = "dbAndSchema";

        public static final String TYPE_NOT_RELATIONAL = "notRelational";

        private static final Map<Integer, String> typeMap = new HashMap<>();

        static {
            init();
        }

        public static String getType(Integer dataTypeCode) {
            return typeMap.get(dataTypeCode);
        }

        public static boolean isSqlDb(Integer dataTypeCode) {
            String s = typeMap.get(dataTypeCode);
            return s != null && (s.equals(TYPE_DB) || s.equals(TYPE_SCHEMA) || s.equals(TYPE_DB_AND_SCHEMA));
        }

        private static void init() {
            // db
            typeMap.put(DataSourceTypeEnum.MySQL.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.MySQL8.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.MySQL5_HIVE.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.MySQL8_HIVE.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.MySQLPXC.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.Polardb_For_MySQL.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.StarRocks.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.GoldenDB.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.TiDB.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.TDSQL_FOR_MySQL.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.DDM_FOR_MYSQL.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.ArgoDB.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.GaussDB_FOR_MySQL.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.DWS_MySQL.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.ClickHouse.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.ClickHouse_MRS.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.HIVE1X.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.HIVE2X.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.HIVE3X.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.HIVE3X_MRS.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.GaussDB_HIVE.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.ADS.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.Doris_JDBC.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.DORIS1.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.DORIS2.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.GBase_8a.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.Sequoiadb_FOR_MYSQL.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.SparkThrift2_1.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.Presto.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.OceanBase_FOR_MySQL.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.OceanBase_FOR_ORACLE.getVal(), TYPE_DB);
            typeMap.put(DataSourceTypeEnum.SAP_HANA.getVal(), TYPE_DB);
            // schema
            typeMap.put(DataSourceTypeEnum.Oracle.getVal(), TYPE_SCHEMA);
            typeMap.put(DataSourceTypeEnum.Oracle_9i.getVal(), TYPE_SCHEMA);
            typeMap.put(DataSourceTypeEnum.Oracle_19c.getVal(), TYPE_SCHEMA);
            typeMap.put(DataSourceTypeEnum.DMDB.getVal(), TYPE_SCHEMA);
            typeMap.put(DataSourceTypeEnum.DMDB_FOR_ORACLE.getVal(), TYPE_SCHEMA);
            typeMap.put(DataSourceTypeEnum.TDSQL_FOR_ORACLE.getVal(), TYPE_SCHEMA);
            typeMap.put(DataSourceTypeEnum.KunDB.getVal(), TYPE_SCHEMA);
            // dbAndSchema
            typeMap.put(DataSourceTypeEnum.PostgreSQL.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.TBase.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.DWS_PG.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.ADB_PostgreSQL.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.GREENPLUM6.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.GREENPLUM_PostgreSQL.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.KINGBASE8.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.TDSQL_FOR_PG.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.SQLServer.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.SQLSERVER_2017_LATER.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.DB2.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.DB2_AS400.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.LIBRA.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.GaussDB.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.Gauss_DB200.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.Informix.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.Sybase.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.VERTICA.getVal(), TYPE_DB_AND_SCHEMA);
            typeMap.put(DataSourceTypeEnum.MAXCOMPUTE.getVal(), TYPE_DB_AND_SCHEMA);
            // Not Relational type
            typeMap.put(DataSourceTypeEnum.REDIS.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.HDFS.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.HDFS3.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.HDFS_MRS.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.HDFS_TBDS.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.HBASE.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.HBASE2.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.HBASE_TBDS.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.HBASE_MRS.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.KAFKA.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.MONGODB.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.ES.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.OPENTSDB.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.SOLR.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.Kudu.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.CarbonData.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.FTP.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.SFTP.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.S3.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.OSS_ALI.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.AWS_S3.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.OSS_HUAWEI.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.OSS_LC.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.INSPUR_S3.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.RESTFUL.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.WEB_SOCKET.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.SOCKET.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.EMQ.getVal(), TYPE_NOT_RELATIONAL);
            typeMap.put(DataSourceTypeEnum.HOST.getVal(), TYPE_NOT_RELATIONAL);
        }
    }
}
