package com.joyadata.dsc.model.datasoure.dto;

import com.joyadata.dsc.utils.jdbc.JdbcUrlBuilderUtil;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class ReplaceDatasourceDTO {

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * schema名称
     */
    private String schemaName;

    /**
     * 服务器地址
     */
    private List<JdbcUrlBuilderUtil.HostPort> hostPorts;
}
