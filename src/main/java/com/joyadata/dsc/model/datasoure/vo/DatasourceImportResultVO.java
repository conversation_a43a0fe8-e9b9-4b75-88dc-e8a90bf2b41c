package com.joyadata.dsc.model.datasoure.vo;

import lombok.Data;

import java.util.List;

/**
 * 数据源导入结果VO
 */
@Data
public class DatasourceImportResultVO {
    /**
     * 总记录数
     */
    private Integer total;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 失败记录列表
     */
    private List<ImportFailRecord> failRecords;

    @Data
    public static class ImportFailRecord {
        /**
         * 行号
         */
        private Integer rowNum;

        /**
         * 数据源名称
         */
        private String datasourceName;

        /**
         * 失败原因
         */
        private String failReason;
    }
} 