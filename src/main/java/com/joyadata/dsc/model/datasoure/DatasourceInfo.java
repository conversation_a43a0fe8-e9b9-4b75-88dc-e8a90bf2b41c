package com.joyadata.dsc.model.datasoure;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.dsc.model.business.BusinessSystem;
import com.joyadata.dsc.model.metadata.MetadataTableCommon;
import com.joyadata.dsc.model.metadata.MetadataTask;
import com.joyadata.dsc.utils.jdbc.JdbcUrlBuilderUtil;
import com.joyadata.dsc.utils.jdbc.JdbcUrlParser;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import com.joyadata.util.sql.enums.AGG;
import com.joyadata.util.sql.enums.ConditionType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据源详情表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_datasource_info", label = "dsc_datasource_info", comment = "数据源详情表")
@JoyadataIndexs({
        @JoyadataIndex(
                type = "UNIQUE INDEX",
                name = "uk_dsc_datasource_info_te_pr_da_de",
                columns = {"project,tenant_code,datasource_name,del_flag"},
                comment = "数据源名称唯一",
                errorMsg = "数据源名称不能重复"),
        @JoyadataIndex(
                type = "UNIQUE INDEX",
                name = "uk_dsc_datasource_info_te_pr_id",
                columns = {"project,tenant_code,id,del_flag"},
                comment = "数据源id唯一",
                errorMsg = "数据源id不能重复")
})
public class DatasourceInfo extends BaseBean {

    @JoyadataColumn(label = "数据源名称", nullable = false, length = 100)
    private String datasourceName;

    @JoyadataColumn(label = "数据源类型id", nullable = false)
    private String datasourceTypeId;

    @JoyadataColumn(label = "业务系统id", length = 100, nullable = false)
    private String businessSystemId;

    @JoyadataColumn(label = "驱动表的id")
    private String datasourceDriverId;

    @JoyadataColumn(label = "数据源大类的id")
    private String datasourceClassifyId;

    @JoyadataColumn(label = "typeCode", length = 32, nullable = false, comment = "datasourceTypeId")
    private Integer dataTypeCode;

    @JoyadataColumn(label = "数据源版本", length = 100, comment = "数据源版本 如1.x, 0.9, 创建下的实例可能会没有版本号")
    private String dbVersion;

    @JoyadataColumn(label = "负责人")
    private String personCharge;

    @JoyadataColumn(label = "生效节点", comment = "生效节点Node序号")
    private String datasourceNodeSerialNumber;

    @JoyadataColumn(label = "连接状态", defaultValue = "0", comment = "连接状态 0失败 1成功")
    private Integer status;

    @JoyadataColumn(label = "备注", length = 2048)
    private String remark;

    @JoyadataColumn(label = "标签", length = 2048)
    private String dataLabel;

    @JoyadataColumn(label = "授权状态",defaultValue = "0", comment = "授权状态 0未授权 1已授权")
    private Integer authFlag;

    @JoyadataColumn(label = "授权类型", comment = "授权类型 DB、TABLE")
    private String authType;

    @JoyadataColumn(label = "授权操作人")
    private String authOperator;

    @JoyadataColumn(label = "授权时间")
    private Date authTime;

    @JoyadataColumn(label = "服务器地址")
    private String serverAddress;

    @JoyadataColumn(label = "最大连接数")
    private Long maxConnection;

    @JoyadataJoin(label = "采集类型",targetBean = MetadataTask.class,targetColumn = "datasourceInfoId",selfColumn = "id",valueColumn = "taskWay")
    private Integer taskWay;

    @JoyadataJoin(label = "业务系统名称", targetBean = BusinessSystem.class, targetColumn = "id", selfColumn = "businessSystemId", valueColumn = "businessName")
    private String businessName;

    @JoyadataJoin(label = "数据源类型名称",targetBean = DatasourceType.class,targetColumn = "id",selfColumn = "datasourceTypeId",valueColumn = "dataType")
    private String datasourceTypeName;

    @JoyadataJoin(label = "驱动名称",targetBean = DatasourceDriver.class,targetColumn = "id",selfColumn = "datasourceDriverId",valueColumn = "driverClassName")
    private String datasourceDriverName;

    @JoyadataJoin(label = "驱动版本",targetBean = DatasourceDriver.class,targetColumn = "id",selfColumn = "datasourceDriverId",valueColumn = "driverVersion")
    private String datasourceDriverVersion;

    @JoyadataJoin(label = "插件名称",targetBean = DatasourceDriver.class,targetColumn = "id",selfColumn = "datasourceDriverId",valueColumn = "dirName")
    private String pluginName;

    @JoyadataAggJoin(label = "元数据数据库", targetBean = MetadataTableCommon.class, targetColumn = "datasourceInfoId", selfColumn = "id", valueColumn = "dbName", agg = AGG.JSON_ARRAYAGG, conditionType = ConditionType.EQ, conditions = {"del_flag=0"})
    private JSONArray metadataDbNames;

    @JoyadataOne2Many(comment = "元数据信息", targetBean = MetadataTableCommon.class, targetClounm = "datasourceInfoId", selfColumn = "id")
    private List<MetadataTableCommon> metadataTableCommonList;

    @JoyadataOne2Many(comment = "数据源节点信息", targetBean = DatasourceNode.class, targetClounm = "datasourceInfoId", selfColumn = "id", withs = "username")
    private List<DatasourceNode> datasourceNodeList;

    @JoyadataTransient(comment = "服务器地址HostPort，使用时需要 lazy datasourceNodeList,datasourceFieldData")
    private List<JdbcUrlBuilderUtil.HostPort> hostPortList;

    @JoyadataTransient(comment = "jdbcUrl连接串")
    private String jdbcUrl;

    @JoyadataTransient(comment = "数据库数量，使用时需要 lazy metadataTableCommonList")
    private Integer countMetadataDb;

    @JoyadataTransient(comment = "用户名，使用时需要 lazy datasourceNodeList")
    private String username;

    public void setMetadataDbNames(JSONArray metadataDbNames) {
        if (CollUtil.isNotEmpty(metadataDbNames)) {
            // 去重
            ArrayList<String> distinct = CollUtil.distinct(metadataDbNames.toJavaList(String.class));
            this.metadataDbNames = new JSONArray(distinct);
            this.countMetadataDb = distinct.size();
            return;
        }
        this.metadataDbNames = metadataDbNames;
    }

    public Integer getCountMetadataDb() {
        return countMetadataDb == null ? 0 : countMetadataDb;
    }

    public List<MetadataTableCommon> getMetadataTableCommonList() {
        if (CollUtil.isNotEmpty(metadataTableCommonList)) {
            Map<String, Integer> dbKeyCountValue = new HashMap<>();
            for (MetadataTableCommon metadataTableCommon : metadataTableCommonList) {
                String dbName = metadataTableCommon.getDbName();
                if (dbKeyCountValue.containsKey(dbName)) {
                    dbKeyCountValue.put(dbName, dbKeyCountValue.get(dbName) + 1);
                } else {
                    dbKeyCountValue.put(dbName, 1);
                }
            }
            setCountMetadataDb(dbKeyCountValue.size());
        }
        return metadataTableCommonList;
    }

    public String getJdbcUrl() {
        String jdbcUrl = null;
        if (CollUtil.isNotEmpty(datasourceNodeList) && StringUtils.isNotEmpty(datasourceNodeSerialNumber)) {
            jdbcUrl = datasourceNodeList.stream()
                    .filter(datasourceNode -> datasourceNodeSerialNumber.equals(datasourceNode.getSerialNumber()))
                    .findFirst().map(DatasourceNode::getJdbcUrl).orElse("");
        }
        return jdbcUrl;
    }

    public String getServerAddress() {
        if (StringUtils.isNotEmpty(this.serverAddress)) {
            return this.serverAddress;
        }
        String result = null;
        String jdbcUrl = getJdbcUrl();
        if (StringUtils.isNotEmpty(jdbcUrl)) {
            List<JdbcUrlBuilderUtil.HostPort> hostPortList = JdbcUrlParser.extractHostPorts(jdbcUrl);
            result = hostPortList.stream().map(JdbcUrlBuilderUtil.HostPort::toString).collect(Collectors.joining(","));
        } else {
            List<JdbcUrlBuilderUtil.HostPort> hostPortList = getHostPortList();
            if (CollUtil.isNotEmpty(hostPortList)) {
                result = hostPortList.stream().map(JdbcUrlBuilderUtil.HostPort::toString).collect(Collectors.joining(","));
            }
        }
        return result;
    }

    public List<JdbcUrlBuilderUtil.HostPort> getHostPortList() {
        if (hostPortList != null) {
            return hostPortList;
        }
        if (CollUtil.isNotEmpty(datasourceNodeList) && StringUtils.isNotEmpty(datasourceNodeSerialNumber)) {
            for (DatasourceNode datasourceNode : datasourceNodeList) {
                try {
                    if (datasourceNodeSerialNumber.equals(datasourceNode.getSerialNumber())) {
                        if (CollUtil.isNotEmpty(datasourceNode.getDatasourceFieldData())) {
                            if (StringUtils.isNotEmpty(datasourceNode.getJdbcUrl())) {
                                List<JdbcUrlBuilderUtil.HostPort> hostPortList = JdbcUrlParser.extractHostPorts(datasourceNode.getJdbcUrl());
                                if (CollUtil.isNotEmpty(hostPortList)) {
                                    setHostPortList(hostPortList);
                                    break;
                                }
                            }
                            for (DatasourceFieldData datasourceFieldDatum : datasourceNode.getDatasourceFieldData()) {
                                if (DatasourceFormField.PropsKeys.HOST_PORT.equals(datasourceFieldDatum.getLabel())) {
                                    JSONArray hostPortArr = JSONArray.parseArray(datasourceFieldDatum.getValue());
                                    List<JdbcUrlBuilderUtil.HostPort> hostPortList = new ArrayList<>();
                                    for (int i = 0; i < hostPortArr.size(); i++) {
                                        JSONObject hostPort = hostPortArr.getJSONObject(i);
                                        JdbcUrlBuilderUtil.HostPort hostPortObject = new JdbcUrlBuilderUtil.HostPort(hostPort.getString(DatasourceFormField.PropsKeys.HOST_PORT_HOST_KEY), hostPort.getInteger(DatasourceFormField.PropsKeys.HOST_PORT_PORT_KEY));
                                        hostPortList.add(hostPortObject);
                                    }
                                    setHostPortList(hostPortList);
                                    break;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    // ignore
                }
            }
        }
        return hostPortList;
    }

    public String getUsername() {
        if (username != null) {
            return username;
        }
        if (CollUtil.isNotEmpty(datasourceNodeList) && StringUtils.isNotEmpty(datasourceNodeSerialNumber)) {
            for (DatasourceNode datasourceNode : datasourceNodeList) {
                if (datasourceNodeSerialNumber.equals(datasourceNode.getSerialNumber())) {
                    if (StringUtils.isNotEmpty(datasourceNode.getUsername())) {
                        setUsername(datasourceNode.getUsername());
                    }
                }
            }
        }
        return username;
    }

    public static class AuthFlagStatus {

        /**
         * 未授权
         */
        public static final Integer UNAUTHORIZED = 0;

        /**
         * 已授权
         */
        public static final Integer AUTHORIZED = 1;
    }

    public static class DataJSONKeys {

        /**
         * String
         * 业务系统ID
         */
        public static final String BUSINESS_UUID = "businessUuid";

        /**
         * String
         * 数据源名称
         */
        public static final String DATA_NAME = "dataName";

        /**
         * String
         * JDBC URL
         */
        public static final String JDBC_URL = "jdbcUrl";

        /**
         * String
         * 用户名
         */
        public static final String USERNAME = "username";

        /**
         * String
         * 密码
         */
        public static final String PASSWORD = "password";

        /**
         * String
         * 数据库模式
         */
        public static final String SCHEMA = "schema";

        /**
         * String
         * 数据库类型
         * MySQL 、 Oracle 、 SQLServer 、 PostgreSQL
         */
        public static final String DATA_TYPE = "dataType";

        /**
         * Integer
         * 数据库类型code
         */
        public static final String DATA_TYPE_CODE = "dataTypeCode";

        /**
         * String
         * 数据库驱动类名
         * com.mysql.jdbc.Driver
         */
        public static final String DRIVER_CLASS_NAME = "driverClassName";

        /**
         * String
         * 数据库驱动版本
         * 5.X 、8.X
         */
        public static final String DATA_VERSION = "dataVersion";

        /**
         * String
         * 数据库显示类型
         */
        public static final String SHOW_DATA_TYPE = "showDataType";

        /**
         * String
         * 数据源时区
         */
        public static final String DS_TIME_ZONE = "dsTimeZone";

        /**
         * String
         * 数据库版本
         */
        public static final String DB_VERSION = "dbVersion";

        /**
         * String
         * 字符集
         */
        public static final String CHARACTER_SET = "characterSet";

        /**
         * 是否开启 Kerberos
         */
        public static final String OPEN_KERBEROS = "openKerberos";

        /**
         * 开启 kerberos
         */
        public static final String OPEN_KERBEROS_TRUE = "open";

        /**
         * 关闭 kerberos
         */
        public static final String OPEN_KERBEROS_FALSE = "close";

        /**
         * Kerberos principal
         */
        public static final String KERBEROS_PRINCIPAL = "kerberos_principal";

        /**
         * Kerberos keytab
         */
        public static final String KERBEROS_KEYTAB = "keytab";

        /**
         * Kerberos krb5.conf
         */
        public static final String KRB5CONF = "krb5conf";

        /**
         * 是否开启 Hive 配置
         */
        public static final String OPEN_HIVE_CONF = "openHiveConf";

        /**
         * Hadoop hive-site.xml
         */
        public static final String HIVE_CONF = "hiveConf";

        /**
         * Hadoop core-site.xml
         */
        public static final String CORE_CONF = "coreSite";

        /**
         * Hadoop hdfs-site.xml
         */
        public static final String HDFS_CONF = "hdfsSite";

        /**
         * file id
         */
        public static final String FILE_ID = "id";

        /**
         * file name
         */
        public static final String FILE_NAME = "filename";
    }
}
