package com.joyadata.dsc.model.datasoure.dto;

import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName AlarmMsg
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/17 19:14
 * @Version 1.0
 **/
@Data
public class AlarmMsg {

    private String eventCode;
    private String tenantCode;
    private String projectId;
    private String productId;
    private Date date;
    private String businessId;
    private String businessName;
    private String errorMsg;
    private String userId;
    private String token;
    private Map<String, Object> data;

    public void addAttr(String key, Object value) {
        if (this.data == null) {
            this.data = new HashMap<>();
        }
        data.put(key, value);
    }
}
