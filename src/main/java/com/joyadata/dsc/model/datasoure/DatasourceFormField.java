package com.joyadata.dsc.model.datasoure;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源配置表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_datasource_form_field", label = "dsc_datasource_form_field", comment = "数据源配置表", isPublic = true, isTenant = false)
public class DatasourceFormField extends BaseBean {

    @JoyadataColumn(label = "表单属性名称，同一模版表单中不重复")
    private String props;

    @JoyadataColumn(label = "label",comment = "表单属性名称，同一模版表单中不重复")
    private String label;

    @JoyadataColumn(label = "属性格式 如Input, Radio等")
    private String widget;

    @JoyadataColumn(label = "是否必填")
    private Integer required;

    @JoyadataColumn(label = "表单属性中默认值",length = 2048)
    private String defaultValue;

    @JoyadataColumn(label = "输入框placeHold, 默认为空",length = 2048)
    private String placeHold;

    @JoyadataColumn(label = "请求数据Api接口地址，一般用于关联下拉框类型，如果不需要请求则为空")
    private String requestApi;

    @JoyadataColumn(label = "是否为数据源需要展示的连接信息字段。0-否; 1-是")
    private Integer isLink;

    @JoyadataColumn(label = "校验返回信息文案",length = 2048)
    private String validInfo;

    @JoyadataColumn(label = "tooltip",length = 2048)
    private String tooltip;

    @JoyadataColumn(label = "前端表单属性style参数")
    private String style;

    @JoyadataColumn(label = "正则校验表达式",length = 2048)
    private String regex;

    @JoyadataColumn(label = "select组件的下拉内容",length = 2048)
    private String options;

    @JoyadataColumn(label = "显示占比")
    private String width;

    @JoyadataColumn(label = "属性显示语言:en-us英文,zh-cn中文")
    private String language;

    @JoyadataColumn(label = "数据源驱动id")
    private String datasourceDriverId;

    /**
     * 示例 选择的value为jdbc时 hostPort 则直接排除 excludeProps 的value以,分割
     [{"value":"jdbc","excludeProps":"hostPort,dbName"},{"value":"hostPort","excludeProps":"jdbc"}]
     */
    @JoyadataColumn(label = "关联属性", comment = "后端判断value后确定需要校验必填项")
    private String associationProps;

    public static class RequiredStatus {

        /**
         * 非必填
         */
        public static final Integer NOT_REQUIRED = 0;

        /**
         * 必填
         */
        public static final Integer REQUIRED = 1;
    }

    public static class AssociationPropsKeys {

        /**
         * value
         */
        public static final String VALUE_KEY = "value";

        /**
         * excludeProps
         */
        public static final String EXCLUDE_KEY = "excludeProps";
    }

    public static class PropsKeys {

        /**
         * 因为有些配置是只填写 host 和 port 所以这里需要一个组装完成后的jdbcUrl
         * props 数据不可占用 jdbcUrl
         * 在 DatasourceFieldData 中 label 为 jdbcUrl 不进行回显
         */
        public static final String JDBC_URL = "jdbcUrl";

        /**
         * 客户自己填写的jdbc
         */
        public static final String JDBC = "jdbc";

        /**
         * host:port []
         * [{"host": "**************", "port": "3306"}]
         */
        public static final String HOST_PORT = "hostPort";

        public static final String HOST_PORT_HOST_KEY = "host";

        public static final String HOST_PORT_PORT_KEY = "port";

        /**
         * 数据库名
         */
        public static final String DB_NAME = "dbName";

        /**
         * 数据库模式名
         */
        public static final String SCHEMA = "schema";

        /**
         * 用户名
         */
        public static final String USER_NAME = "username";

        /**
         * 密码
         */
        public static final String PASSWORD = "password";

        /**
         * 协议
         */
        public static final String PROTOCOL = "protocol";

        /**
         * 配置类型
         * mysql: {jdbc,hostPort}
         */
        public static final String CONFIG_TYPE = "configType";

        /**
         * 高级配置
         * {key:value}
         */
        public static final String ADVANCED = "advanced";

        /**
         * 备注
         */
        public static final String REMARK = "remark";
    }
}
