package com.joyadata.dsc.model.datasoure.vo;

import com.joyadata.dsc.model.metadata.MetadataTableCommon;
import lombok.Data;

import java.util.List;

@Data
public class DatasourceInfoBaseDetailVO {

    /**
     * 表数量
     */
    private Integer tableSize;

    /**
     * 视图数量
     */
    private Integer viewSize;

    /**
     * 函数数量
     */
    private Integer functionSize;

    /**
     * 存储过程数量
     */
    private Integer procedureSize;

    /**
     * 索引数量
     */
    private Integer indexSize;

    /**
     * 表集合
     */
    private List<MetadataTableCommon> tableList;

    /**
     * 视图集合
     */
    private List<MetadataTableCommon> viewList;

    /**
     * 函数集合
     */
    private List<MetadataTableCommon> functionList;

    /**
     * 存储过程集合
     */
    private List<MetadataTableCommon> procedureList;

    /**
     * 索引集合
     */
    private List<MetadataTableCommon> indexList;


    public Integer getTableSize() {
        if (tableList != null) {
            return tableList.size();
        }
        return 0;
    }

    public Integer getViewSize() {
        if (viewList != null) {
            return viewList.size();
        }
        return 0;
    }

    public Integer getFunctionSize() {
        if (functionList != null) {
            return functionList.size();
        }
        return 0;
    }

    public Integer getProcedureSize() {
        if (procedureList != null) {
            return procedureList.size();
        }
        return 0;
    }

    public Integer getIndexSize() {
        if (indexList != null) {
            return indexList.size();
        }
        return 0;
    }
}