package com.joyadata.dsc.model.datasoure;


import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据源sql查询
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_datasource_sql", label = "dsc_datasource_sql", comment = "数据源sql查询")
public class DatasourceSql extends BaseBean {

    /**
     * 数据源信息业务主键（UUID）
     */
    @JoyadataColumn(label = "数据源类型id", nullable = false)
    private String datasourceInfoId;


    /**
     * sql
     */
    @JoyadataColumn(label = "sql", nullable = false)
    private String sql;

    /**
     * sql名称
     */
    @JoyadataColumn(label = "sql名称", nullable = false)
    private String sqlName;

    /**
     * 库名
     */
    @JoyadataColumn(label = "库名")
    private String dbName;

    /**
     * 模式名称
     */
    @JoyadataColumn(label = "模式名称")
    private String schemaName;

}
