package com.joyadata.dsc.model.datasoure;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import com.joyadata.util.sql.enums.AGG;
import com.joyadata.util.sql.enums.ConditionType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 数据源分组表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_datasource_classify", label = "dsc_datasource_classify", comment = "数据源分组表", isPublic = true, isTenant = false)
public class DatasourceClassify extends BaseBean {

    @JoyadataColumn(label = "类型栏编码")
    private String classifyCode;

    @JoyadataColumn(label = "类型栏名称")
    private String classifyName;

    @JoyadataOne2Many(comment = "数据源类型",targetBean = DatasourceType.class,targetClounm = "datasourceClassifyId",selfColumn = "id",withs = "datasourceInfoSize")
    private List<DatasourceType> datasourceTypes;

    @JoyadataAggJoin(label = "该类型下的条数",targetBean = DatasourceInfo.class,targetColumn = "datasourceClassifyId",selfColumn = "id",
            agg = AGG.COUNT,valueColumn = "datasourceClassifyId",
            conditions = {"del_flag=0"},conditionType = ConditionType.EQ)
    private Integer datasourceInfoSize;

    @JoyadataAggJoin(label = "该类型下的条数",targetBean = DatasourceInfo.class,targetColumn = "datasourceClassifyId",selfColumn = "id",
            agg = AGG.COUNT,valueColumn = "datasourceClassifyId",
            conditions = {"del_flag=0","status=1"},conditionType = ConditionType.EQ)
    private Integer datasourceInfoSuccessSize;
}
