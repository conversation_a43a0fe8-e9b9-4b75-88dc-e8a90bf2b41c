package com.joyadata.dsc.model.datasoure.dto;


import lombok.Data;


/**
 * 数据源sql查询
 * <AUTHOR>
 */
@Data
public class DatasourceSqlDTO {

    /**
     * 数据源信息业务主键（UUID）
     */
    private String datasourceInfoId;

    /**
     * sqlId
     */
    private String sqlId;


    /**
     * dbName
     */
    private String dbName;

    /**
     * 模式名
     */
    private String schemaName;

    /**
     * sql
     */
    private String sql;


    /**
     * 预览条数，默认1000（如果传的是-1，代表查询全部）
     */
    private Integer previewNum;

    /**
     * 是否是执行计划
     */
    private Boolean isExecutionPlan;
    /**
     * 数据源类型
     */
    private String dbType;
}
