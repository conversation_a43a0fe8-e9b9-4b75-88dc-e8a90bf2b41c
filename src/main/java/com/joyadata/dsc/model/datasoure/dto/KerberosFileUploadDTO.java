package com.joyadata.dsc.model.datasoure.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Kerberos文件上传DTO
 * 用于异步上传文件时返回文件信息
 */
@Data
public class KerberosFileUploadDTO {
    
    /**
     * 文件ID（统一文件存储中的ID）
     */
    private String fileId;
    
    /**
     * 文件类型（keytab, krb5Conf, jaasConf）
     */
    private String fileType;
    
    /**
     * 原始文件名
     */
    private String originalFileName;
    
    /**
     * 标准化文件名
     */
    private String standardFileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * 上传是否成功
     */
    private Boolean success;
    
    /**
     * 错误信息（如果上传失败）
     */
    private String errorMessage;
    
    /**
     * 创建成功的文件上传结果
     * 
     * @param fileId 文件ID
     * @param fileType 文件类型
     * @param originalFileName 原始文件名
     * @param standardFileName 标准化文件名
     * @param fileSize 文件大小
     * @return 成功结果
     */
    public static KerberosFileUploadDTO success(String fileId, String fileType, 
                                               String originalFileName, String standardFileName, 
                                               Long fileSize) {
        KerberosFileUploadDTO dto = new KerberosFileUploadDTO();
        dto.setFileId(fileId);
        dto.setFileType(fileType);
        dto.setOriginalFileName(originalFileName);
        dto.setStandardFileName(standardFileName);
        dto.setFileSize(fileSize);
        dto.setUploadTime(LocalDateTime.now());
        dto.setSuccess(true);
        return dto;
    }
    
    /**
     * 创建失败的文件上传结果
     * 
     * @param fileType 文件类型
     * @param originalFileName 原始文件名
     * @param errorMessage 错误信息
     * @return 失败结果
     */
    public static KerberosFileUploadDTO failure(String fileType, String originalFileName, String errorMessage) {
        KerberosFileUploadDTO dto = new KerberosFileUploadDTO();
        dto.setFileType(fileType);
        dto.setOriginalFileName(originalFileName);
        dto.setErrorMessage(errorMessage);
        dto.setUploadTime(LocalDateTime.now());
        dto.setSuccess(false);
        return dto;
    }
}
