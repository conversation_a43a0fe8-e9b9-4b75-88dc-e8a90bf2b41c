package com.joyadata.dsc.model.datasoure.vo;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.util.List;

@Data
public class DatasourceConnectProgressVO {

    public static final int FAIL = 0;

    public static final int SUCCESS = 1;

    private String datasourceInfoId;

    private List<DatasourceConnectVO> datasourceConnectVOList;

    private Integer progress;

    public Integer getProgress() {
        int result = 0;
        if (CollUtil.isEmpty(datasourceConnectVOList)) {
            return result;
        }
        for (DatasourceConnectVO datasourceConnectVO : datasourceConnectVOList) {
            if (DatasourceConnectVO.SUCCESS.equals(datasourceConnectVO.getCheckStatus())) {
                result += 20;
            }
        }
        return result;
    }

    public Integer getStatus() {
        if (CollUtil.isEmpty(datasourceConnectVOList)) {
            return FAIL;
        }
        for (DatasourceConnectVO datasourceConnectVO : datasourceConnectVOList) {
            if (DatasourceConnectVO.USERNAME_PASSWORD.equals(datasourceConnectVO.getType())) {
                if (!DatasourceConnectVO.SUCCESS.equals(datasourceConnectVO.getCheckStatus())) {
                    return FAIL;
                }
            }
        }
        return SUCCESS;
    }

    /**
     * 获取数据源连接错误信息
     */
    public String getMsg(){
        if (CollUtil.isEmpty(datasourceConnectVOList)) {
            return "";
        }
        for (DatasourceConnectVO datasourceConnectVO : datasourceConnectVOList) {
            if (DatasourceConnectVO.USERNAME_PASSWORD.equals(datasourceConnectVO.getType())) {
                if (!DatasourceConnectVO.SUCCESS.equals(datasourceConnectVO.getCheckStatus())) {
                    return datasourceConnectVO.getMsg();
                }
            }
        }
        return "";
    }

}
