package com.joyadata.dsc.model.datasoure;

import cn.hutool.core.collection.CollUtil;
import com.joyadata.dsc.utils.jdbc.JdbcUrlParser;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@JoyadataTable(name = "dsc_datasource_node", label = "数据源节点配置")
@JoyadataIndexs({
        @JoyadataIndex(
                type = "UNIQUE INDEX",
                name = "uk_dsc_datasource_node_te_pr_da_de",
                columns = {"project,tenant_code,datasourceInfoId,nodeName,del_flag"},
                comment = "节点名称名称唯一",
                errorMsg = "节点名称不能重复"),
        @JoyadataIndex(
                type = "UNIQUE INDEX",
                name = "dsc_datasource_node_unique",
                columns = {"project,tenant_code,id"},
                comment = "id唯一",
                errorMsg = "id不能重复")})
public class DatasourceNode extends BaseBean {

    @JoyadataColumn(label = "节点名称")
    private String nodeName;

    /**
     * 不要问我为啥这里不用Integer
     * 前端： serialNumber  我这个不是number 是一个字符串 因为我把这个当id在用 牵扯态度了太多了 很难改了 求求你
     */
    @JoyadataColumn(label = "节点序号", comment = "数据源中判断生效节点也根据这个字段。从1开始。")
    private String serialNumber;

    @JoyadataColumn(label = "数据源ID")
    private String datasourceInfoId;

    @JoyadataColumn(label = "时区")
    private String timeZone;

    @JoyadataColumn(label = "备注")
    private String remark;

    @JoyadataColumn(label = "高级配置", length = 2048, comment = "JSONString")
    private String advancedConfiguration;

    @JoyadataOne2Many(comment = "属性的值", targetBean = DatasourceFieldData.class, targetClounm = "datasourceNodeId", selfColumn = "id")
    private List<DatasourceFieldData> datasourceFieldData;

    @JoyadataJoin(label = "主机", targetBean = DatasourceFieldData.class, targetColumn = "datasourceNodeId", selfColumn = "id", valueColumn = "value", conditions = {"label=host"})
    private String host;

    @JoyadataJoin(label = "端口", targetBean = DatasourceFieldData.class, targetColumn = "datasourceNodeId", selfColumn = "id", valueColumn = "value", conditions = {"label=port"})
    private Integer port;

    @JoyadataJoin(label = "用户名", targetBean = DatasourceFieldData.class, targetColumn = "datasourceNodeId", selfColumn = "id", valueColumn = "value", conditions = {"label=username"})
    private String username;

    @JoyadataTransient
    private boolean getJdbcUrl = false;

    @JoyadataTransient
    private String jdbcUrl;

    @JoyadataTransient
    private String serverAddress;

//    先注释，未来如果加入权重策略可使用线性规划算法进行资源分配
//    @JoyadataColumn(label = "权重")
//    private Integer weight;


    public List<DatasourceFieldData> getDatasourceFieldData() {
        if (CollUtil.isNotEmpty(datasourceFieldData) && !getJdbcUrl) {
            DatasourceFieldData remove = null;
            for (DatasourceFieldData fieldData : datasourceFieldData) {
                if (DatasourceFormField.PropsKeys.JDBC_URL.equals(fieldData.getLabel())) {
                    setJdbcUrl(fieldData.getValue());
                    remove = fieldData;
                }
            }
            if (remove != null) {
                datasourceFieldData.remove(remove);
            }
        }
        return datasourceFieldData;
    }

    public void openGetJdbcUrl() {
        this.setGetJdbcUrl(true);
    }

    public String getDbName() {
        // 仅使用登记的库名
//        if (StringUtils.isNotBlank(jdbcUrl)) {
//            return JdbcUrlParser.extractDatabase(jdbcUrl);
//        }
        if (CollUtil.isNotEmpty(datasourceFieldData)) {
            for (DatasourceFieldData fieldData : datasourceFieldData) {
                if (DatasourceFormField.PropsKeys.JDBC.equals(fieldData.getLabel())) {
                    return JdbcUrlParser.extractDatabase(fieldData.getValue());
                }
                if (DatasourceFormField.PropsKeys.DB_NAME.equals(fieldData.getLabel())) {
                    return fieldData.getValue();
                }
            }
        }
        return null;
    }
    public String getSchema() {
        if (CollUtil.isNotEmpty(datasourceFieldData)) {
            for (DatasourceFieldData fieldData : datasourceFieldData) {
                if (DatasourceFormField.PropsKeys.SCHEMA.equals(fieldData.getLabel())) {
                    return JdbcUrlParser.extractDatabase(fieldData.getValue());
                }
            }
        }
        return null;
    }
}