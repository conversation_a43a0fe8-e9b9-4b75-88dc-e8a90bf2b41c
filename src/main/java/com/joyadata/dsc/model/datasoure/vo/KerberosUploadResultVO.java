package com.joyadata.dsc.model.datasoure.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Kerberos文件上传结果VO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class KerberosUploadResultVO {
    
    /**
     * 上传是否成功
     */
    private Boolean success;
    
    /**
     * 数据源ID
     */
    private String datasourceId;
    
    /**
     * 上传的文件信息
     */
    private List<FileInfo> uploadedFiles;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * Kerberos配置目录路径
     */
    private String kerberosPath;
    
    /**
     * 文件信息内部类
     */
    @Data
    public static class FileInfo {
        /**
         * 文件名
         */
        private String fileName;
        
        /**
         * 文件大小（字节）
         */
        private Long fileSize;
        
        /**
         * 文件类型
         */
        private String fileType;
        
        /**
         * 文件哈希值
         */
        private String fileHash;
        
        /**
         * 文件完整路径
         */
        private String filePath;
        
        /**
         * 是否上传成功
         */
        private Boolean uploaded;
        
        /**
         * 错误信息（如果上传失败）
         */
        private String error;
    }
    
    /**
     * 创建成功结果
     * 
     * @param datasourceId 数据源ID
     * @param uploadedFiles 上传的文件列表
     * @param kerberosPath Kerberos路径
     * @return 成功结果VO
     */
    public static KerberosUploadResultVO success(String datasourceId, 
                                                List<FileInfo> uploadedFiles, 
                                                String kerberosPath) {
        KerberosUploadResultVO result = new KerberosUploadResultVO();
        result.setSuccess(true);
        result.setDatasourceId(datasourceId);
        result.setUploadedFiles(uploadedFiles);
        result.setKerberosPath(kerberosPath);
        result.setUploadTime(LocalDateTime.now());
        return result;
    }
    
    /**
     * 创建失败结果
     * 
     * @param datasourceId 数据源ID
     * @param errorMessage 错误信息
     * @return 失败结果VO
     */
    public static KerberosUploadResultVO failure(String datasourceId, String errorMessage) {
        KerberosUploadResultVO result = new KerberosUploadResultVO();
        result.setSuccess(false);
        result.setDatasourceId(datasourceId);
        result.setErrorMessage(errorMessage);
        result.setUploadTime(LocalDateTime.now());
        return result;
    }
}
