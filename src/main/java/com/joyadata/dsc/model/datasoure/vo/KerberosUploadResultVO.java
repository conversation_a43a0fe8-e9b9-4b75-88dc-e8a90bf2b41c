package com.joyadata.dsc.model.datasoure.vo;

import com.joyadata.dsc.model.datasoure.dto.KerberosFileUploadDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Kerberos文件上传结果VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class KerberosUploadResultVO {

    /**
     * 上传是否成功
     */
    private Boolean success;

    /**
     * 上传的文件信息列表
     */
    private List<KerberosFileUploadDTO> uploadedFiles;
    
    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * 创建成功结果
     *
     * @param uploadedFiles 上传的文件列表
     * @return 成功结果VO
     */
    public static KerberosUploadResultVO success(List<KerberosFileUploadDTO> uploadedFiles) {
        KerberosUploadResultVO result = new KerberosUploadResultVO();
        result.setSuccess(true);
        result.setUploadedFiles(uploadedFiles);
        result.setUploadTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param errorMessage 错误信息
     * @return 失败结果VO
     */
    public static KerberosUploadResultVO failure(String errorMessage) {
        KerberosUploadResultVO result = new KerberosUploadResultVO();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setUploadTime(LocalDateTime.now());
        return result;
    }
}
