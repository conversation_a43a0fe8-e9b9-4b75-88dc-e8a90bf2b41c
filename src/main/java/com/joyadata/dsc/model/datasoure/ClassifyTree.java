package com.joyadata.dsc.model.datasoure;

import com.joyadata.model.TreeBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 分类树模型
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ClassifyTree extends TreeBean {

    /**
     * 数据源数量
     */
    private Integer count;

    /**
     * 连接成功的数据源数量
     */
    private Integer successCount;

    /**
     * 子节点列表
     */
    private List<ClassifyTree> children;

    /**
     * 分类编码
     */
    private String classifyCode;
    
    /**
     * 图标URL
     */
    private String icon;
    
    /**
     * 数据类型代码
     */
    private Integer dataTypeCode;
}
