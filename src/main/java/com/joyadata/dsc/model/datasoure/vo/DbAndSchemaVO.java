package com.joyadata.dsc.model.datasoure.vo;

import lombok.Data;

import java.util.List;

/**
 * 数据库和schema
 */
@Data
public class DbAndSchemaVO {

    /**
     * 数据库
     */
    public static final String TYPE_DB = "db";

    /**
     * schema
     */
    public static final String TYPE_SCHEMA = "schema";

    /**
     * 数据库/Schema
     */
    private String type;

    /**
     * 数据库或schema列表
     */
    private List<String> dbOrSchema;
}
