package com.joyadata.dsc.model.datasoure.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 数据源导入VO
 */
@Data
public class DatasourceImportVO {

    @ExcelProperty("业务系统名称")
    private String businessName;

    @ExcelProperty("负责人用户名")
    private String personCharge;

    @ExcelProperty("数据源名称")
    private String datasourceName;

    @ExcelProperty("数据库类型")
    private String datasourceTypeName;

    @ExcelProperty("驱动版本")
    private String dbVersion;

    @ExcelProperty("服务器地址(多个以,分隔)")
    private String serverAddress;

    @ExcelProperty("数据库名称")
    private String dbName;

    @ExcelProperty("数据库模式")
    private String schema;

    @ExcelProperty("用户名")
    private String username;

    @ExcelProperty("密码")
    private String password;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("jdbc连接串")
    private String jdbcUrl;
} 