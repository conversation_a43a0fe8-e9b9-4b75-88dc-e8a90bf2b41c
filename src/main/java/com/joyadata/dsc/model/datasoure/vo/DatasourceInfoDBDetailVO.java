package com.joyadata.dsc.model.datasoure.vo;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.joyadata.dsc.model.datasoure.DatasourceSql;
import com.joyadata.dsc.model.metadata.MetadataTableCommon;
import lombok.Data;

import java.util.List;

@Data
public class DatasourceInfoDBDetailVO extends DatasourceInfoBaseDetailVO {

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 模式名称
     */
    private String schemaName;

    /**
     * 区分是数据库
     * 还是SQL查询面板
     * 1. 数据库db
     * 2. SQL查询sql
     */
    private String type;

    /**
     * 是否存在模式
     */
    private boolean existSchemas;

    /**
     * 模式数量
     */
    private Integer schemaSize;

    /**
     * 模式列表
     */
    private List<DatasourceInfoSchemasDetailVO> schemas;

    /**
     * sql查询列表
     */
    private List<DatasourceSql> datasourceSqlList;

    /**
     * sql面板数量
     */
    private Integer sqlSize;

    public Integer getSchemaSize() {
        if (schemas != null) {
            return schemas.size();
        }
        return 0;
    }

    public String getType() {
        return StringUtils.isNotEmpty(type) ? type : "db";
    }

    public Integer getSqlSize() {
        if (datasourceSqlList != null) {
            return datasourceSqlList.size();
        }
        return 0;
    }
}
