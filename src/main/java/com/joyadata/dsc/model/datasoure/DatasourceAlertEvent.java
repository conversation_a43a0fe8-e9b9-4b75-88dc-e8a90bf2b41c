package com.joyadata.dsc.model.datasoure;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import com.joyadata.util.sql.annotation.JoyadataTransient;
import lombok.Data;

/**
 * @ClassName DatasourceAlertEvent
 * @Description 数据源告警事件
 * <AUTHOR>
 * @Date 2025/3/7 10:16
 * @Version 1.0
 **/
@Data
@JoyadataTable(name = "dsc_datasource_alert_event", label = "dsc_datasource_alert_event", comment = "数据源告警事件")
public class DatasourceAlertEvent extends BaseBean {
    @JoyadataColumn(label = "告警事件id", comment = "告警事件id")
    private String eventId;
    @JoyadataColumn(label = "告警事件code", comment = "告警事件code")
    private String eventCode;
    @JoyadataColumn(label = "告警状态", comment = "告警状态")
    private Boolean alertStatus;
    @JoyadataColumn(label = "告警级别", comment = "告警级别：一般、重要、严重、紧急")
    private String alertLevel;
    @Deprecated
    @JoyadataColumn(label = "告警策略id", comment = "告警策略id，删除时，要远程调用接口删除告警策略信息")
    private String alarmStrategyId;
    @JoyadataColumn(label = "数据源告警id", comment = "数据源告警id")
    private String datasourceAlertId;
    @JoyadataTransient(label = "触发频率")
    private String triggerFrequency;

    @JoyadataTransient(label = "告警事件名称")
    private String eventName;

    @Override
    public void afterDbInit() {
        switch (getEventCode()) {
            case "B401001": // 数据源主机网络连接失败
            case "B401004": // 数据源连接数超过90%
                setTriggerFrequency("1小时一次");
                break;
            case "B401002": // 数据源用户名密码错误
                setTriggerFrequency("6小时一次");
                break;
            case "B401003": // 数据源采集任务运行异常
            case "B402005": // 元数据表结构变更通知
                setTriggerFrequency("即时触发");
                break;
            default:
                break;
        }
    }
}
