package com.joyadata.dsc.model.datasoure.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Kerberos配置DTO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class KerberosConfigDTO {

    /**
     * 是否启用Kerberos认证
     */
    private Boolean enabled = false;

    /**
     * Kerberos主体名称
     * 例如：hive/<EMAIL>
     */
    private String principal;

    /**
     * Kerberos文件信息映射
     * key: 文件类型(keytab, krb5Conf, jaasConf), value: 文件信息
     */
    private Map<String, KerberosFileInfo> files;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * Kerberos文件信息
     */
    @Data
    public static class KerberosFileInfo {
        /**
         * 统一文件存储中的文件ID
         */
        private String fileId;

        /**
         * 原始文件名
         */
        private String fileName;

        /**
         * 文件上传时间
         */
        private LocalDateTime uploadTime;

        /**
         * 文件大小
         */
        private Long fileSize;
    }
    
    /**
     * 检查Kerberos配置是否完整
     *
     * @return true if configuration is complete
     */
    public boolean isConfigComplete() {
        return enabled != null && enabled
               && principal != null && !principal.trim().isEmpty()
               && files != null
               && files.containsKey("keytab")
               && files.containsKey("krb5Conf");
    }

    /**
     * 获取必需的文件类型列表
     *
     * @return 必需文件类型数组
     */
    public String[] getRequiredFileTypes() {
        if (!enabled || files == null) {
            return new String[0];
        }

        return files.keySet().toArray(new String[0]);
    }

    /**
     * 获取指定类型的文件信息
     *
     * @param fileType 文件类型
     * @return 文件信息
     */
    public KerberosFileInfo getFileInfo(String fileType) {
        if (files == null) {
            return null;
        }
        return files.get(fileType);
    }

    /**
     * 添加文件信息
     *
     * @param fileType 文件类型
     * @param fileInfo 文件信息
     */
    public void addFileInfo(String fileType, KerberosFileInfo fileInfo) {
        if (files == null) {
            files = new java.util.HashMap<>();
        }
        files.put(fileType, fileInfo);
    }
}
