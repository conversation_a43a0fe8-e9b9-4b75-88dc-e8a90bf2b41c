package com.joyadata.dsc.model.datasoure.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Kerberos配置DTO
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class KerberosConfigDTO {
    
    /**
     * 是否启用Kerberos认证
     */
    private Boolean enabled = false;
    
    /**
     * Kerberos主体名称
     * 例如：hive/<EMAIL>
     */
    private String principal;
    
    /**
     * keytab文件名
     */
    private String keytabFile;
    
    /**
     * krb5.conf文件名
     */
    private String krb5ConfFile;
    
    /**
     * jaas.conf文件名（可选）
     */
    private String jaasConfFile;
    
    /**
     * 文件上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * 文件哈希值映射，用于校验文件完整性
     * key: 文件名, value: SHA256哈希值
     */
    private Map<String, String> fileHashes;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 检查Kerberos配置是否完整
     * 
     * @return true if configuration is complete
     */
    public boolean isConfigComplete() {
        return enabled != null && enabled 
               && principal != null && !principal.trim().isEmpty()
               && keytabFile != null && !keytabFile.trim().isEmpty()
               && krb5ConfFile != null && !krb5ConfFile.trim().isEmpty();
    }
    
    /**
     * 获取必需的文件列表
     * 
     * @return 必需文件名数组
     */
    public String[] getRequiredFiles() {
        if (!enabled) {
            return new String[0];
        }
        
        if (jaasConfFile != null && !jaasConfFile.trim().isEmpty()) {
            return new String[]{keytabFile, krb5ConfFile, jaasConfFile};
        } else {
            return new String[]{keytabFile, krb5ConfFile};
        }
    }
}
