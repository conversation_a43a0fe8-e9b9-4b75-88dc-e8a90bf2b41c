package com.joyadata.dsc.model.business;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 业务系统查询对象
 *
 * <AUTHOR>
 * @date 2024-01-16
 */
@Data
public class BusinessSystemQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
    * 自主加需要查询的字段和传值
    */

    /**
     *创建开始时间
     */
    private String createStartTime;
    /**
     *创建结束时间
     */
    private String createEndTime;

    /**
     * 业务系统名称
     */
    private String businessName;

    /**
     * 业务系统选择的id集合
     */
    private List<String> businessIds;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 业务系统状态
     *
     */
    private String businessStatus;

    /**
     * 业务系统类型
     *
     */
    private String businessType;

    /**
     * 所属部门id
     */
    private String departId;
}
