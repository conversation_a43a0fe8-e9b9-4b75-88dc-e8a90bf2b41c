package com.joyadata.dsc.model.business;

import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import com.joyadata.util.sql.enums.AGG;
import com.joyadata.util.sql.enums.ConditionType;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 数据源--业务系统
 */
@Data
@JoyadataTable(name = "dsc_business_system", label = "业务系统表")
public class BusinessSystem extends BaseBean {
    /**
     * 业务系统名称
     */
    @JoyadataColumn(label = "业务系统名称", length = 100,nullable = false)
    private String businessName;

    /**
     * 简称
     */
    @JoyadataColumn(label = "简称",length = 100)
    private String simpleName;

    /**
     * 所属部门
     */
    @JoyadataColumn(label = "所属部门", length = 100)
    private String departName;

    /**
     * 目前状况 0 再用 1停用
     */
    @JoyadataColumn(label = "系统状态", comment = "0停用 1在建 2在用 3拟停用 4其他",length = 2,nullable = false)
    private String businessStatus;

    /**
     * 系统类型
     */
    @JoyadataColumn(label = "系统类型", comment = "0业务系统 1分析系统 2安全系统 3数据系统 4其他",length = 2,nullable = false)
    private String businessType;

    /**
     * 访问地址
     */
    @JoyadataColumn(label = "访问地址", length = 100)
    private String interviewAddress;

    


    @JoyadataColumn(label = "业务简述", length = 1000)
    private String remark;


    @JoyadataColumn(label = "是否默认业务分类")
    private Boolean isDefault;

    @JoyadataColumn(label = "负责人", length = 100)
    private String manager;

    @JoyadataColumn(label = "业务系统安全等级",comment = "L1,L2,L3,L4,L5")
    private String securityLevel;

    @JoyadataTransient(label = "负责人名称")
    private String managerName;

    @JoyadataAggJoin(label = "数据源数量", agg = AGG.COUNT,targetBean = DatasourceInfo.class,targetColumn = "businessSystemId",selfColumn = "id",valueColumn = "businessSystemId", conditionType = ConditionType.EQ, conditions = {"del_flag=0"})
    private Integer datasourceSize =0;

    @JoyadataOne2Many(comment = "数据源信息", targetBean = DatasourceInfo.class, targetClounm = "businessSystemId", selfColumn = "id", conditions = {"del_flag=0"}, withs = "metadataDbNames")
    private List<DatasourceInfo> datasourceInfos;



}
