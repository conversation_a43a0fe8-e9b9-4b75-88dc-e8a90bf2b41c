package com.joyadata.dsc.model.business;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 业务系统错误数据对象
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@ExcelIgnoreUnannotated
public class BusinessSystemImportErrorVO extends BusinessSystemImportVO {

     /**
     * 错误原因
     */
    @ExcelProperty(value = "错误原因")
    protected String errReason;

}
