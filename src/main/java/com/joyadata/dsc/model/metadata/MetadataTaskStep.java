package com.joyadata.dsc.model.metadata;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 采集步骤表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_metadata_task_step", label = "dsc_metadata_task_step", comment = "元数据采集任务执行步骤")
@JoyadataIndex(
        type = "UNIQUE INDEX",
        name = "uk_dsc_metadata_task_step_te_pr_da_de",
        columns = {"project,tenant_code,del_flag"},
        comment = "id,",
        errorMsg = "id")
public class MetadataTaskStep extends BaseBean {

    /**
     * 采集记录id
     */
    @JoyadataColumn(label = "采集记录id")
    private String metadataTaskRecordId;

    @JoyadataColumn(label = "步骤名称")
    private String stepName;

    @JoyadataColumn(label = "步骤内容")
    private String stepContent;


    @JoyadataColumn(label = "步骤状态")
    private Integer stepStatus;

    @JoyadataColumn(label = "步骤错误信息")
    private String stepErrorMsg;

    @JoyadataColumn(label = "步骤开始时间")
    private Date stepStartTime;

    @JoyadataColumn(label = "步骤结束时间")
    private Date stepEndTime;













}
