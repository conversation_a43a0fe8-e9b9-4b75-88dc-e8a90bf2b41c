package com.joyadata.dsc.model.metadata;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import com.joyadata.util.sql.enums.AGG;
import com.joyadata.util.sql.enums.ConditionType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_metadata_task_record", label = "dsc_metadata_task_record", comment = "元数据采集配置-采集记录表")
@JoyadataIndex(
        type = "UNIQUE INDEX",
        name = "uk_dsc_metadata_task_record_te_pr_id",
        columns = {"project,tenant_code,id"},
        comment = "任务",
        errorMsg = "数据源ID不能重复")
public class MetadataTaskRecord extends BaseBean {

    @JoyadataColumn(label = "任务id-采集配置id")
    private String metadataTaskId;


    @JoyadataColumn(label = "数据源id")
    private String datasourceInfoId;

    @JoyadataColumn(label = "数据源类型")
    private String datasourceType;

    /**
     * 采集日志
     */
    @JoyadataColumn(label = "采集日志",length = 3000)
    private String errorLog;

    /**
     * 0: 未运行
     * 1: 运行中
     * 2: 运行失败
     * 3: 运行成功
     * {@link com.joyadata.dsc.enums.MetadataCollectionStatusEnum}
     */
    @JoyadataColumn(label = "记录运行状态：默认运行中")
    private Integer status=1;

    /**
     * 采集动作：
     *      0: 手动采集
     *      1: 定时采集
     *{@link com.joyadata.dsc.enums.SwitchStatus}
     */
    @JoyadataColumn(label = "采集动作")
    private Integer taskType;

    /**
     * 采集配置数
     */
    @JoyadataColumn(label = "采集配置数")
    private Integer recordTotal;

    /**
     * 平台已采集的数据量
     */
    @JoyadataColumn(label = "平台已采集的数据量")
    private Integer qfRecordTotal;

    /**
     * 删除平台采集过但在 源库中不存在的数据量
     */
    @JoyadataColumn(label = "删除平台采集过但在 源库中不存在的数据量")
    private Integer deleteRecordTotal;

    /**
     * 采集操作人
     */
    @JoyadataColumn(label = "采集操作人")
    private String collectionOperator;



    @JoyadataOne2One(comment = "元数据采集执行一次任务配置信息", targetBean = MetadataTask.class, targetClounm = "metadataTaskRecordId", selfColumn = "id")
    private MetadataTask metadataTask;



    /**
     * 新增表数量
     */
    @JoyadataColumn(label = "新增表数量")
    private Integer addTableCount;

    /**
     * 新增视图
     */
    @JoyadataColumn(label = "新增视图")
    private Integer addViewCount;

    /**
     * 新增存储过程
     */
    @JoyadataColumn(label = "新增存储过程")
    private Integer addProcedureCount;

    /**
     * 新增函数
     */
    @JoyadataColumn(label = "新增函数")
    private Integer addFunctionCount;

    /**
     * 新增索引
     */
    @JoyadataColumn(label = "新增索引")
    private Integer addIndexCount;

    /**
     * 新增列数量
     */
    @JoyadataColumn(label = "新增列数量")
    private Integer addColumnCount;

    /**
     * 更新表数量
     */
    @JoyadataColumn(label = "更新表数量")
    private Integer updateTableCount;

    /**
     * 更新视图
     */
    @JoyadataColumn(label = "更新视图")
    private Integer updateViewCount;

    /**
    * 更新存储过程
     * */
    @JoyadataColumn(label = "更新存储过程")
    private Integer updateProcedureCount;

    /**
     * 更新函数
     */
    @JoyadataColumn(label = "更新函数")
    private Integer updateFunctionCount;
    /**
     * 更新索引
     */
    @JoyadataColumn(label = "更新索引")
    private Integer updateIndexCount;

    /**
     * 更新列数量
     */
    @JoyadataColumn(label = "更新列数量")
    private Integer updateColumnCount;

    /**
     * 删除表数量
     */
    @JoyadataColumn(label = "删除表数量")
    private Integer deleteTableCount;

    /**
     * 删除视图
     */
    @JoyadataColumn(label = "删除视图")
    private Integer deleteViewCount;

    /**
     * 删除存储过程
     */
    @JoyadataColumn(label = "删除存储过程")
    private Integer deleteProcedureCount;
    /**
     * 删除函数
     */
    @JoyadataColumn(label = "删除函数")
    private Integer deleteFunctionCount;
    /**
     * 删除索引
     */
    @JoyadataColumn(label = "删除索引")
    private Integer deleteIndexCount;

    /**
     * 删除列数量
     */
    @JoyadataColumn(label = "删除列数量")
    private Integer deleteColumnCount;

    /**
     * 开始时间
     */
    @JoyadataColumn(label = "开始时间")
    private Date startTime;

    @JoyadataColumn(label = "结束时间")
    private Date endTime;

    /**
     * 采集记录内容最后状态
     */
    @JoyadataColumn(label = "采集记录内容最后状态")
    private Integer contentStatus;

}
