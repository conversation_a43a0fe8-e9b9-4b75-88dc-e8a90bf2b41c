package com.joyadata.dsc.model.metadata;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 元数据字段 对象 metadata_column_common
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_metadata_column_common", label = "dsc_metadata_column_common", comment = "元数据管理-字段公共属性表")
public class MetadataColumnCommon extends BaseBean {

    /*
    每种类型公共属性
    ==============================================================================================================
     */
    /**
     * 表id
     */
    @JoyadataColumn(label = "表id")
    private String metadataTableCommonId;

    /**
     * 字段名称
     */
    @JoyadataColumn(label = "字段名称")
    private String columnName;

    /**
     * 字段类型
     */
    @JoyadataColumn(label = "字段类型")
    private String columnType;
    /**
     * 字段中文名
     */
    @JoyadataColumn(label = "字段中文名")
    private String columnCnName;

    /**
     * 字段注释
     */
    @JoyadataColumn(label = "字段注释")
    private String columnComment;

    /**
     * 是否唯一 0是 1不是
     */
    @JoyadataColumn(label = "是否唯一")
    private Integer columnUnique;

    /**
     * 是否增量字段 0是 1不是
     */
    @JoyadataColumn(label = "是否增量字段")
    private Integer columnIncrement;

    /**
     * 类型 区分是表、视图
     */
    @JoyadataColumn(label = "类型")
    private String resourceType;

    /**
     * 是否为空 0是 1不是
     */
    @JoyadataColumn(label = "是否为空")
    private Integer columnNull;
    /**
     * 表和视图使用得字段属性
     * =========================================================================================================================
     */
    /**
     * 字段长度
     */
    @JoyadataColumn(label = "字段长度")
    private String columnLength="0";

    /**
     * 是否是主键字段 0是 1不是
     */
    @JoyadataColumn(label = "是否是主键字段")
    private Integer columnPrimaryKey;

    /**
     * 是否是外键 0是 1不是
     */
    @JoyadataColumn(label = "是否是外键")
    private Integer columnForeignKey;

    /**
     * 数据类型
     */
    @JoyadataColumn(label = "数据类型")
    private String columnDataType;

    /**
     * SQL类型
     */
    @JoyadataColumn(label = "SQL类型")
    private String sqlType;
    /**
     * 数据精度
     */
    @JoyadataColumn(label = "数据精度")
    private Integer columnScale;

    /**
     * 是否分区
     */
    @JoyadataColumn(label = "是否分区")
    private Integer isPart;
    /*
    索引过程得参数
    --------------------------------------------------------------------------------------------------------------
     */

    /**
     * 排序规则
     */
    @JoyadataColumn(label = "排序规则")
    private String  collation;


    /*
    函数或者存储过程得参数
    --------------------------------------------------------------------------------------------------------------
     */
    /**
     * 参数方向（in, out, in/out）
     */
    @JoyadataColumn(label = "参数方向")
    private String inOut;

    /**
     * 表名
     */
    @JoyadataColumn(label = "表名")
    private String tableName;



}
