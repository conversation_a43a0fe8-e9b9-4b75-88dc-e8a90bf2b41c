package com.joyadata.dsc.model.metadata;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 元数据字段 MetadataColumnPrivate 私有属性
 *
 * <AUTHOR>
 * @date 2022-07-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
//@JoyadataTable(name = "dsc_metadata_column_private", label = "dsc_metadata_column_private", comment = "元数据管理-字段级私有属性表")
public class MetadataColumnPrivate extends BaseBean {


    /**
     * 字段公共属性ID
     */
    @JoyadataColumn(label = "字段公共属性ID")
    private String metadataColumnCommonId;

    /**
     * 表id
     */
    @JoyadataColumn(label = "表id")
    private String metadataTableCommonId;

    /**
     * 私有属性名称
     */
    @JoyadataColumn(label = "私有属性名称")
    private String propertyName;

    /**
     * 私有属性值
     */
    @JoyadataColumn(label = "私有属性值")
    private String propertyValue;











}
