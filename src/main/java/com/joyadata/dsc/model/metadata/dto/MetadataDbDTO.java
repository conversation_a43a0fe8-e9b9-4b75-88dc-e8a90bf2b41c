package com.joyadata.dsc.model.metadata.dto;

import lombok.Data;

/**
 * 采集配置入参对象
 * @author: sh
 **/
@Data
public class MetadataDbDTO {

    /**
     * 数据源名称
     */
    private String datasourceInfoId;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 模式名称
     */
    private String schemaName;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型  view-table-pro
     */
    private String type;

    /**
     * 表名称搜索
     */
    private String tableNamePattern;

    /**
     * 索引 函数、存储过程对应表名
     */
    private String tableName;

}
