package com.joyadata.dsc.model.metadata.dto;

import lombok.Data;

/**
 * 采集对象
 * @author: sh
 **/
@Data
public class GatherMetadataDTO {

    /**
     * 字段更新标识
     */
    private Boolean updateColumnFlag;


    /**
     * 字段更新详细信息字符串
     */
    private String updateColumnDetail;

    /**
     * 新增表数量
     */
    private Integer addTableCount=0;

    /**
     * 新增视图
     */
    private Integer addViewCount=0;

    /**
     * 新增存储过程
     */
    private Integer addProcedureCount=0;

    /**
     * 新增函数
     */
    private Integer addFunctionCount=0;

    /**
     * 新增索引
     */
    private Integer addIndexCount=0;

    /**
     * 新增列数量
     */
    private Integer addColumnCount=0;

    /**
     * 更新表数量
     */
    private Integer updateTableCount=0;

    private Integer updateViewCount=0;

    private Integer updateProcedureCount=0;

    private Integer updateFunctionCount=0;

    private Integer updateIndexCount=0;

    private Integer updateColumnCount=0;

    private Integer deleteTableCount=0;

    private Integer deleteViewCount=0;

    private Integer deleteProcedureCount=0;

    private Integer deleteFunctionCount=0;

    private Integer deleteIndexCount=0;

    private Integer deleteColumnCount=0;

    private Integer deleteTotal=0;

    /**
     * 配置总数
     */
    private Integer configTotal=0;

}
