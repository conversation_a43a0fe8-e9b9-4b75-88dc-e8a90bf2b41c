package com.joyadata.dsc.model.metadata.dto;

import lombok.Data;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;


@Data
public class MetadataTaskDTO {



    private final String metadataTaskRecordId;
    private final AtomicBoolean isRunning;
    private final AtomicInteger progress;

    public MetadataTaskDTO(String metadataTaskRecordId) {
        this.metadataTaskRecordId =metadataTaskRecordId;
        this.isRunning = new AtomicBoolean(false);
        this.progress = new AtomicInteger(0);
    }

    public String getMetadataTaskRecordId() {
        return metadataTaskRecordId;
    }
    public boolean isRunning() {
        return isRunning.get();
    }

    public void setRunning(boolean running) {
        isRunning.set(running);
    }

    public int getProgress() {
        return progress.get();
    }

    public void setProgress(int progress) {
        this.progress.set(progress);
    }
}
