package com.joyadata.dsc.model.metadata.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采集任务返回目录实体
 *
 * <AUTHOR>
 * date：Created in 下午12:25 2022/3/21
 * company: www.dtstack.com
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetadataTaskRecordResultVO {


    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 模式名称
     */
    private String schemaName;

    /**
     * 表统计
     */
    private Integer tableCount=0;

    /**
     * 视图统计
     */
    private Integer viewCount=0;

    /**
     * 存储过程统计
     */
    private Integer procedureCount=0;

    /**
     * 函数统计
     */
    private Integer functionCount=0;

    /**
     * 索引统计
     */
    private Integer indexCount=0;

    /**
     * 字段统计
     */
    private Integer columnCount=0;

    /**
     * 目录数
     */
    private Integer folderCount=0;

    /**
     * 文件数
     */
    private Integer fileCount=0;


    /**
     * 合计
     */
    private Integer total;

    public Integer getTotal() {
        return tableCount+viewCount+indexCount+functionCount+procedureCount+columnCount;
    }
}
