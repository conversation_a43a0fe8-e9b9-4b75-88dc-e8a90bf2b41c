package com.joyadata.dsc.model.metadata;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 采集配置表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_metadata_task", label = "dsc_metadata_task", comment = "元数据采集配置")
@JoyadataIndex(
        type = "UNIQUE INDEX",
        name = "uk_dsc_metadata_task_te_pr_da_de",
        columns = {"project,tenant_code,datasource_in_id,del_flag"},
        comment = "数据源id对应任务唯一,采集",
        errorMsg = "数据源ID不能重复")
public class MetadataTask extends BaseBean {

    @JoyadataColumn(label = "数据源id")
    private String datasourceInfoId;

    /**
     * 采集方式：
     *{@link com.joyadata.dsc.enums.MetadataTaskWayEnum}
     *
     */
    @JoyadataColumn(label = "采集方式")
    private Integer taskWay;


    /**
     *
     * 定时采集的频率
     */
    @JoyadataColumn(label = "定时频率")
    private String executionFrequency;


    /**
     * 是否开启定时采集：0开启 1：关闭
     *{@link com.joyadata.dsc.enums.SwitchStatus}
     *
     */
    @JoyadataColumn(label = "是否开启定时采集 默认关闭 1")
    private Integer isTiming=1;

    /**
     * 采集范围：
     * {@link com.joyadata.dsc.enums.MetadataCollectionRangeEnum}
     */
    @JoyadataColumn(label = "采集范围")
    private Integer acquisitionScope;


    /**
     * 采集范围为 "黑白名单" 时存储白名单的字段
     * 文件：文件名匹配正则
     */
    @JoyadataColumn(label = "白名单",length = 2048)
    private String whitelist;

    /**
     * 采集范围为 "黑白名单" 时存储黑名单的字段
     * 文件：路径排除正则
     */
    @JoyadataColumn(label = "黑名单",length = 2048)
    private String blacklist;

    /**
     * 采集范围为 "快速选表" 时存储的字段,以逗号分隔
     */
    @JoyadataColumn(label = "快速选表",length = 2048)
    private String selectTable;

    /**
     * 采集动作：
     *      0: 手动采集
     *      1: 定时采集
     *{@link com.joyadata.dsc.enums.SwitchStatus}
     */
    @JoyadataColumn(label = "采集动作")
    private Integer taskType;

    /**
     * 采集对象:
     * {@link com.joyadata.dsc.enums.MetadataTaskObjectEnum}
     */
    @JoyadataColumn(label = "采集对象")
    private String taskObject;

    /**
     * 采集类型
     * 1: 定时采集使用
     */
    @JoyadataColumn(label = "生效开始时间")
    private Date effectiveStartDate;

    /**
     * 采集类型
     * 1: 定时采集使用
     */
    @JoyadataColumn(label = "生效结束时间")
    private Date effectiveEndDate;

    /**
     * 是否是采集默认配置
     * {@link com.joyadata.dsc.enums.SwitchStatus}
     */
    @JoyadataColumn(label = "是否是采集默认配置")
    private Integer isDefault;

    /**
     * 采集任务配置 记录执行id
     */
    @JoyadataColumn(label = "采集任务配置 记录执行id")
    private String metadataTaskRecordId;


    /**
     * 频率类型
     */
    @JoyadataColumn(label = "频率类型：推荐0 自定义1")
    private Integer cronType;

    /**
     * 选择表
     */
    @JoyadataOne2Many(comment = "选择得表",targetBean = MetadataSelect.class,
            targetClounm = "metadataTaskId",selfColumn = "id")
    private List<MetadataSelect> metadataSelect;


    /**
     * 是否递归采集：文件使用 1 是 0否
     */
    @JoyadataColumn(label = "是否递归采集")
    private Integer isRecursion;

    /**
     * 文件采集路径
     */
    @JoyadataColumn(label = "文件采集路径")
    private String filePath;

    /**
     * 文件采集指定路径
     */
    @JoyadataColumn(label = "文件采集指定路径")
    private String filePathSpecified;

    /**
     * 采集层级
     */
    @JoyadataColumn(label = "采集层级")
    private  Integer maxLevel;

    /**
     * 采集文件类型:采集表记录
     */
    @JoyadataTransient(comment = "采集文件类型:采集表")
    private List<MetadataTableCommonDetail> metadataTableCommonDetails;

}
