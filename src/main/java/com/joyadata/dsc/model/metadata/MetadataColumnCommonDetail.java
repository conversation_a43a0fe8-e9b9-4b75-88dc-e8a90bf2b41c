package com.joyadata.dsc.model.metadata;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 元数据字段 对象 metadata_column_common
 *
 * <AUTHOR>
 * @date 2022-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_metadata_column_common_detail", label = "dsc_metadata_column_common_detail", comment = "元数据管理-字段公共属性记录表")
public class MetadataColumnCommonDetail extends BaseBean {

    /**
     * 采集记录id
     */
    @JoyadataColumn(label = "采集记录id")
    private String metadataTaskRecordId;

    /**
     * 表公共属性记录id
     */
    @JoyadataColumn(label = "表公共属性记录id")
    private String metadataTableCommonDetailId;

    /**
     * 表id
     */
    @JoyadataColumn(label = "表id")
    private String metadataTableCommonId;

    /**
     * 字段名称
     */
    @JoyadataColumn(label = "字段名称")
    private String columnName;

    /**
     * 字段中文名
     */
    @JoyadataColumn(label = "字段中文名")
    private String columnCnName;

    /**
     * 字段注释
     */
    @JoyadataColumn(label = "字段注释")
    private String columnComment;

    /**
     * 字段长度
     */
    @JoyadataColumn(label = "字段长度")
    private String columnLength="0";

    /**
     * 字段类型
     */
    @JoyadataColumn(label = "字段类型")
    private String columnType;

    /**
     * 是否是主键字段 0是 1不是
     */
    @JoyadataColumn(label = "是否是主键字段")
    private Integer columnPrimaryKey;

    /**
     * 是否是外键 0是 1不是
     */
    @JoyadataColumn(label = "是否是外键")
    private Integer columnForeignKey;

    /**
     * 数据类型
     */
    @JoyadataColumn(label = "数据类型")
    private String columnDataType;
    /**
     * 是否为空 0是 1不是
     */
    @JoyadataColumn(label = "是否为空")
    private Integer columnNull;

    /**
     * SQL类型
     */
    @JoyadataColumn(label = "SQL类型")
    private String sqlType;
    /**
     * 数据精度
     */
    @JoyadataColumn(label = "数据精度")
    private Integer columnScale;

    /**
     * 是否分区
     */
    @JoyadataColumn(label = "是否分区")
    private Integer isPart;

    /**
     * 是否唯一 0是 1不是
     */
    @JoyadataColumn(label = "是否唯一")
    private Integer columnUnique;

    /**
     * 是否增量字段 0是 1不是
     */
    @JoyadataColumn(label = "是否增量字段")
    private Integer columnIncrement;

    /**
     * 类型 区分是表、视图
     */
    @JoyadataColumn(label = "类型")
    private String resourceType;


    /**
     *元数据操作状态 insert update delete
     */
    @JoyadataColumn(label = "元数据操作状态")
    private String opt;

    /**
     * 库名
     */
    @JoyadataColumn(label = "库名")
    private String dbName;

    /**
     * 模式名称
     */
    @JoyadataColumn(label = "模式名称")
    private String schemaName;

    /**
     * 表名
     */
    private String tableName;
}
