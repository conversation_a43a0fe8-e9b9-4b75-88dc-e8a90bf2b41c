package com.joyadata.dsc.model.metadata;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * 元数据 对象 MetadataTableCommonDetail 公共属性
 *
 * <AUTHOR>
 * @date 2022-07-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_metadata_table_common_detail", label = "dsc_metadata_table_common_detail", comment = "元数据管理-采集记录表")
public class MetadataTableCommonDetail extends BaseBean {

    /**
     * 采集记录id
     */
    @JoyadataColumn(label = "采集记录id")
    private String metadataTaskRecordId;

    /**
     * 表id
     */
    @JoyadataColumn(label = "表id")
    private String metadataTableCommonId;
    /**
     * 数据源id
     */
    @JoyadataColumn(label = "数据源id")
    private String datasourceInfoId;

    /**
     * 数据源类型
     */
    @JoyadataColumn(label = "数据源类型")
    private String datasourceType;

    /**
     * 元数据名称
     */
    @JoyadataColumn(label = "表名")
    private String name;

    /**
     * 库名
     */
    @JoyadataColumn(label = "库名")
    private String dbName;

    /**
     * 模式名称
     */
    @JoyadataColumn(label = "模式名称")
    private String schemaName;
    /**
     * 元数据类型
     */
    @JoyadataColumn(label = "元数据类型")
    private String type;

    /**
     * 元数据注释
     */
    @JoyadataColumn(label = "元数据注释")
    private String comment;

    /**
     * 元数据中文名：最初由注释填充
     */
    @JoyadataColumn(label = "元数据中文名")
    private String cnName;

    /**
     * 字段个数
     */
    @JoyadataColumn(label = "字段个数")
    private Integer metadataColumnTotal;

    @JoyadataColumn(label = "负责人", length = 100)
    private String manager;
    /**
     * 0: 未运行
     * 1: 运行中
     * 2: 运行失败
     * 3: 运行成功
     * {@link com.joyadata.dsc.enums.MetadataCollectionStatusEnum}
     */
    @JoyadataColumn(label = "记录运行状态：默认未运行")
    private Integer status=0;

    /**
     * 采集日志
     */
    @JoyadataColumn(label = "表错误日志",length = 3000)
    private String errorLog;

    /**
     *元数据操作状态 insert update delete
     */
    @JoyadataColumn(label = "元数据操作状态")
    private String opt;

    /**
     * 索引 函数、存储过程对应表id
     */
    @JoyadataColumn(label = "索引 函数、存储过程对应表id", length = 100)
    private String tableName;

    /**
     * 索引 函数、存储过程对应表id
     */
    @JoyadataColumn(label = "索引 函数、存储过程对应表id", length = 100)
    private String indexTableId;

    /**
     * 建表语句
     */
    @JoyadataColumn(label = "建表语句", length = 100)
    private String ddl;
    /**
     *排序字符集
     */
    @JoyadataColumn(label = "排序字符集")
    private String collation;

    /**
     *字符集
     */
    @JoyadataColumn(label = "字符集")
    private String charset;
    /**
     * 类型  索引类型
     */
    @JoyadataColumn(label = "索引类型")
    private String indexType;

    /**
     * 是否唯一索引 0 是 1 不是
     */
    @JoyadataColumn(label = "是否唯一索引")
    private Integer unique;

    /**
     * -----------------------------------------------------------文件类型元数据-----------------------------------------------------------
     */

    /**
     * 文件路径
     */
    @JoyadataColumn(label = "文件路径")
    private String filePath;
    /**
     * 文件类型
     */
    @JoyadataColumn(label = "文件类型")
    private String fileType;
    /**
     * 文件大小
     */
    @JoyadataColumn(label = "文件大小")
    private String fileSize;
    /**
     * 修改时间
     */
    @JoyadataColumn(label = "修改时间")
    private String fileModifiedTime;
    /**
     * 权限
     */
    @JoyadataColumn(label = "权限")
    private String accessPermission;
    /**
     * 文件所属目录
     */
    @JoyadataColumn(label = "文件所属目录")
    private String fileParentPath;
    /**
     * 文件所属用户组
     */
    @JoyadataColumn(label = "文件所属用户组")
    private String fileGroup;
    /**
     * 文件所属用户
     */
    @JoyadataColumn(label = "文件所属用户")
    private String fileOwner;
    /**
     * 当前层级
     */
    @JoyadataColumn(label = "当前层级")
    private String fileLevel;
    /**
     * -----------------------------------------------------------文件类型元数据-----------------------------------------------------------
     */

    // 自定义 equals 方法，用于比较 dbname、schemaName、name、type
    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MetadataTableCommonDetail that = (MetadataTableCommonDetail) o;
        return Objects.equals(dbName, that.dbName) &&
                (Objects.equals(schemaName, that.schemaName) || (isEmpty(this.schemaName) && isEmpty(that.schemaName))) &&
                Objects.equals(name, that.name) &&
                Objects.equals(type, that.type);
    }

    // 自定义 hashCode 方法
    @Override
    public int hashCode() {
        return Objects.hash(dbName, schemaName, name, type);
    }

    // 判断字符串是否为空或为 null
    private static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

}
