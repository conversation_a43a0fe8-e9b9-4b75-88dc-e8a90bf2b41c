package com.joyadata.dsc.model.metadata;

import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import com.joyadata.util.sql.annotation.JoyadataTransient;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * 元数据 MetadataTablePrivate 私有属性
 *
 * <AUTHOR>
 * @date 2022-07-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_metadata_table_private", label = "dsc_metadata_table_private", comment = "元数据管理-表级私有属性表")
public class MetadataTablePrivate extends BaseBean {


    /**
     * 数据源id
     */
    @JoyadataColumn(label = "数据源id")
    private String datasourceInfoId;

    /**
     * 表id
     */
    @JoyadataColumn(label = "表id")
    private String metadataTableCommonId;

    /**
     * 库名
     */
    @JoyadataColumn(label = "库名")
    private String dbName;

    /**
     * 模式名称
     */
    @JoyadataColumn(label = "模式名称")
    private String schemaName;
    /**
     * 元数据类型
     */
    @JoyadataColumn(label = "元数据类型")
    private String type;
    /**
     * 私有属性名称
     */
    @JoyadataColumn(label = "私有属性名称")
    private String propertyName;

    /**
     * 私有属性值
     */
    @JoyadataColumn(label = "私有属性值")
    private String propertyValue;


}
