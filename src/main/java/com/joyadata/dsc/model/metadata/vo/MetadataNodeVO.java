package com.joyadata.dsc.model.metadata.vo;

import com.joyadata.dsc.enums.MetadataTaskObjectEnum;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 采集任务返回目录实体
 *
 * <AUTHOR>
 * date：Created in 下午12:25 2022/3/21
 * company: www.dtstack.com
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetadataNodeVO {

    /**
     * dbName_schema_name_type
     */
    private String uuid;

    /**
     * 数据源id
     */
    private String datasourceInfoId;
    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 模式名称
     */
    private String schemaName;

    /**
     * 名称
     */
    private String name;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 类型  view-table-pro
     */
    private String type;
    /**
     * 注释
     */
    private String comment;

    /**
     * 类型  索引类型
     */
    private String indexType;

    /**
     * 是否唯一索引 0 是 1 不是
     */
    private Integer unique;

    /**
     * 子集
     */
    private List<MetadataNodeVO> childrens;

    /**
     * 前端使用
     */
    private boolean isLeaf=false;

    /**
     * 数据源授权使用:项目id，以逗号分隔
     */
    private String projectId;

    /**
     * 索引 函数、存储过程对应表名
     */
    private String tableName;
    /**
     * 父级id
     */
    private String parentId;
    /**
     * 层级标识:默认3级 表级
     */
    private String level;



    /**
     * 是否存在模式
     */
    private boolean hasSchema;

}
