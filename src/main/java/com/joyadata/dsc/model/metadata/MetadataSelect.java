package com.joyadata.dsc.model.metadata;

import com.joyadata.dsc.utils.MD5Util;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.JoyadataColumn;
import com.joyadata.util.sql.annotation.JoyadataTable;
import com.joyadata.util.sql.annotation.JoyadataTransient;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@JoyadataTable(name = "dsc_metadata_select", label = "dsc_metadata_select", comment = "元数据采集配置-选择资源")
public class MetadataSelect extends BaseBean {

    /**
     * 执行一次的配置id,若是采集配置保存0
     */
    @JoyadataColumn(label = "任务id")
    private String metadataTaskId;


    @JoyadataColumn(label = "数据源id")
    private String datasourceInfoId;

    /**
     * 数据库名称
     */
    @JoyadataColumn(label = "数据库名称")
    private String dbName;

    /**
     * 模式名称
     */
    @JoyadataColumn(label = "模式名称")
    private String schemaName;

    /**
     * 资源名称名称：table 、view、index
     */
    @JoyadataColumn(label = "名称")
    private String name;

    /**
     * 类型:
     * {@link com.joyadata.dsc.enums.MetadataTaskObjectEnum}
     */
    @JoyadataColumn(label = "类型")
    private String type;
    /**
     * 注释
     */
    @JoyadataColumn(label = "注释")
    private String comment;
    /**
     * 索引 函数、存储过程对应表id
     */
    @JoyadataColumn(label = "索引 函数、存储过程对应表id", length = 100)
    private String tableName;


    /**
     * 采集任务配置 记录执行id
     */
    @JoyadataColumn(label = "采集任务配置 记录执行id")
    private String metadataTaskRecordId;

    /**
     * 类型  索引类型
     */
    @JoyadataColumn(label = "索引类型")
    private String indexType;

    /**
     * 是否唯一索引 0 是 1 不是
     */
    @JoyadataColumn(label = "是否唯一索引")
    private Integer unique;


    /**
     *
     * 是否是最终子集
     */
    @JoyadataTransient
    private boolean leaf;
    public boolean getLeaf() {
        return true;
    }

    /**
     *
     * 唯一标识
     */
    @JoyadataTransient
    private String uuid;
    public String getUuid() {
        String uuid = MD5Util.resourceToMD5(datasourceInfoId,dbName,schemaName,name,type,tableName);
        return uuid;
    }


}
