package com.joyadata.dsc.model.record;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.joyadata.model.BaseBean;
import com.joyadata.util.sql.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper=false)
@Data
@JoyadataTable(name = "dsc_records_operation", label = "dsc_records_operation", comment = "操作记录详情表")
@JoyadataIndexs({
        @JoyadataIndex(
                type = "UNIQUE INDEX",
                name = "uk_dsc_records_operations_te_pr_id",
                columns = {"project,tenant_code,id,del_flag"},
                comment = "操作记录id唯一",
                errorMsg = "操作记录id不能重复")
})
public class RecordsOperation extends BaseBean {

    @JoyadataColumn(label = "数据源id")
    private String datasourceId;

    @JoyadataColumn(label = "操作对象")
    private String type;

    @JoyadataColumn(label = "相关对象", comment = "jsonString")
    private String operatorBeans;

    @JoyadataColumn(label = "操作内容")
    private String operatorDetails;

    @JoyadataTransient(comment = "操作人")
    private String operator;

    @JoyadataTransient(comment = "相关对象")
    private List<? extends BaseBean> beanClass;

    @Override
    public void beforeDbInsert() {
        setOperatorBeans();
        super.beforeDbInsert();
    }

    @Override
    public void beforeDbUpdate() {
        setOperatorBeans();
        super.beforeDbUpdate();
    }

    private void setOperatorBeans() {
        if (CollUtil.isNotEmpty(beanClass)) {
            JSONObject jsonObject = new JSONObject();
            for (BaseBean aClass : beanClass) {
                String classSimpleName = aClass.getClass().getSimpleName();
                if (jsonObject.containsKey(classSimpleName)) {
                    jsonObject.put(classSimpleName, jsonObject.getString(classSimpleName) + "," + aClass.getId());
                } else {
                    jsonObject.put(classSimpleName, aClass.getId());
                }
            }
            this.operatorBeans = jsonObject.toJSONString();
        }
    }

}
