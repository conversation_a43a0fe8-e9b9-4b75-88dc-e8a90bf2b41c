package com.joyadata.dsc;

import com.joyadata.AutoApp;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 启动类
 */
@SpringBootApplication
@ComponentScan(basePackageClasses = {AutoApp.class, DscApp.class})
@EnableAsync
public class DscApp {
    public static void main(String[] args) {
        SpringApplication.run(DscApp.class, args);
        System.out.println("数据源中心服务启动成功");
    }
}
