package com.joyadata.dsc.factory.datasource;

import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.joyadata.dsc.processor.DatasourceProcessor;
import com.joyadata.dsc.processor.datasource.DatabaseProcessor;
import com.joyadata.dsc.processor.datasource.FileProcessor;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 数据源策略工厂
 */
public class ProcessorFactory {

    private static final Map<DataSourceTypeEnum, DatasourceProcessor> processors = new HashMap<>();

    static {
        // 数据库类型
        processors.put(DataSourceTypeEnum.MySQL, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.MySQL8, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.PostgreSQL, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.SQLServer, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.SQLSERVER_2017_LATER, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.Oracle, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.Oracle_9i, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.Oracle_19c, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.DB2, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.TiDB, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.TDSQL_FOR_MySQL, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.GoldenDB, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.StarRocks, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.ADS, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.DWS_MySQL, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.GaussDB_FOR_MySQL, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.Sequoiadb_FOR_MYSQL, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.MariaDB, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.GaussDB, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.DWS_PG, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.GREENPLUM6, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.GREENPLUM_PostgreSQL, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.TBase, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.TDSQL_FOR_PG, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.Informix, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.KINGBASE8, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.DMDB, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.ClickHouse, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.ADB_PostgreSQL, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.OceanBase_FOR_MySQL, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.OceanBase_FOR_ORACLE, new DatabaseProcessor());
        processors.put(DataSourceTypeEnum.SAP_HANA, new DatabaseProcessor());

        // 文件型
        processors.put(DataSourceTypeEnum.FTP, new FileProcessor());
        processors.put(DataSourceTypeEnum.SFTP, new FileProcessor());
    }

    public static DatasourceProcessor getProcessor(Integer type) {
        return Optional.ofNullable(processors.get(DataSourceTypeEnum.valOf(type)))
                .orElseThrow(() -> new IllegalArgumentException("Unknown data source"));
    }
}