package com.joyadata.dsc.enums;

/**
 * @ClassName AlertStatusEnum
 * @Description 告警事件
 * <AUTHOR>
 * @Date 2025/3/24 14:38
 * @Version 1.0
 **/
public enum AlertStatusEnum {
    B401001("紧急", "数据源主机网络连接失败"),
    B401002("紧急", "数据源用户名密码错误"),
    B401003("严重", "数据源采集任务运行异常"),
    B401004("严重", "数据源连接数超过90%"),
    B402005("一般", "元数据表结构变更通知");

    AlertStatusEnum(String name, String des) {
        this.name = name;
        this.des = des;
    }

    private String name;
    private String des;

    public static String getDes(String code) {
        for (AlertStatusEnum item : AlertStatusEnum.values()) {
            if (item.name().equals(code)) {
                return item.des;
            }
        }
        return null;
    }

    public static String getName(String code) {
        for (AlertStatusEnum item : AlertStatusEnum.values()) {
            if (item.name().equals(code)) {
                return item.name;
            }
        }
        return null;
    }

    public static String getCode(String name) {
        for (AlertStatusEnum item : AlertStatusEnum.values()) {
            if (item.name.equals(name)) {
                return item.name();
            }
        }
        return null;
    }
}
