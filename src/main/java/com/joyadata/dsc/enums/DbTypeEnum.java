package com.joyadata.dsc.enums;
import com.alibaba.druid.DbType;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/2/21 17:57
 */
@AllArgsConstructor
public enum DbTypeEnum {
    Mysql(DataSourceTypeEnum.MySQL.getDataType(), DbType.valueOf("mysql")),
    <PERSON>(DataSourceTypeEnum.DORIS2.getDataType(), DbType.valueOf("mysql")),
    <PERSON>(DataSourceTypeEnum.Oracle.getDataType(),DbType.valueOf("oracle")),
    SQLServer(DataSourceTypeEnum.SQLServer.getDataType(),DbType.valueOf("sqlserver")),
    PostgreSQL(DataSourceTypeEnum.PostgreSQL.getDataType(),DbType.valueOf("postgresql")),
    TBase(DataSourceTypeEnum.TBase.getDataType(),DbType.valueOf("postgresql")),
    ADB_PostgreSQL(DataSourceTypeEnum.TBase.getDataType(),DbType.valueOf("postgresql")),
    TDSQL_FOR_PG(DataSourceTypeEnum.TDSQL_FOR_PG.getDataType(),DbType.valueOf("postgresql")),
    DB2(DataSourceTypeEnum.DB2.getDataType(),DbType.valueOf("db2") ),
    DMDB(DataSourceTypeEnum.DMDB.getDataType(),DbType.valueOf("dm")),
    KINGBASE8(DataSourceTypeEnum.KINGBASE8.getDataType(),DbType.valueOf("kingbase")),
    HIVE2X(DataSourceTypeEnum.HIVE2X.getDataType(),DbType.valueOf("hive")),
    HIVE1X(DataSourceTypeEnum.HIVE1X.getDataType(),DbType.valueOf("hive")),
    HIVE3X(DataSourceTypeEnum.HIVE3X.getDataType(),DbType.valueOf("hive")),
    CLICKHOUSE(DataSourceTypeEnum.ClickHouse.getDataType(),DbType.valueOf("clickhouse")),
    HBASE(DataSourceTypeEnum.HBASE.getDataType(),DbType.valueOf("hbase")),
    HBASE2(DataSourceTypeEnum.HBASE2.getDataType(),DbType.valueOf("hbase")),
    PHOENIX4(DataSourceTypeEnum.Phoenix4.getDataType(),DbType.valueOf("phoenix")),
    PHOENIX5(DataSourceTypeEnum.Phoenix5.getDataType(),DbType.valueOf("phoenix")),
    OceanBase_FOR_MySQL(DataSourceTypeEnum.OceanBase_FOR_MySQL.getDataType(),DbType.valueOf("oceanbase")),
    OceanBase_FOR_ORACLE(DataSourceTypeEnum.OceanBase_FOR_ORACLE.getDataType(),DbType.valueOf("oceanbase")),
    MARIADB(DataSourceTypeEnum.MariaDB.getDataType(),DbType.valueOf("mariadb")),
    TDSQL_FOR_MySQL(DataSourceTypeEnum.TDSQL_FOR_MySQL.getDataType(),DbType.valueOf("mysql")),
    DWS_PG(DataSourceTypeEnum.DWS_PG.getDataType(),DbType.valueOf("postgresql")),
    INFORMIX(DataSourceTypeEnum.Informix.getDataType(),DbType.valueOf("informix")),
    SAP_HANA(DataSourceTypeEnum.SAP_HANA.getDataType(),DbType.valueOf("sapdb"));

    @Getter
    public String name;
    @Getter
    public DbType dbType;

    public static DbType getDbTypeByName(String name){
        for (DbTypeEnum value : DbTypeEnum.values()) {
            if(value.getName().equalsIgnoreCase(name)){
                return value.getDbType();
            }
        }
        return Mysql.getDbType();
    }

}