package com.joyadata.dsc.enums;



import java.util.ArrayList;
import java.util.List;

/**
 * 业务系统枚举
 */
public enum BusinessStatusEnum {

    // 停用
    STOP("4", "停用", "4"),
    // 在建
    CREATE("1", "在建", "1"),
    // 在用
    RUNNING("2", "在用", "2"),

    //拟停用
    STOPING("3", "拟停用", "3"),
    // 其他
    OTHER("5", "其他", "5"),
    ;

    private final String type;
    private final String chineseName;
    private final String englishName;

    BusinessStatusEnum(String type, String chineseName, String englishName) {
        this.type = type;
        this.chineseName = chineseName;
        this.englishName = englishName;
    }

    public String getType() {
        return type;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    @Override
    public String toString() {
        return String.format("BusinessEnum{type='%s', chineseName='%s', englishName='%s'}", type, chineseName, englishName);
    }

    //获取目前状况
    public static List<String> getBusinessStatusList() {
        return new ArrayList<String>() {{
            add(STOP.getChineseName());
            add(OTHER.getChineseName());
            add(CREATE.getChineseName());
            add(RUNNING.getChineseName());
            add(STOPING.getChineseName());
        }};
    }
    //根据中文名字获取枚举 并且区分系统和状态
    public static BusinessStatusEnum getByChineseName(String chineseName) {
        for (BusinessStatusEnum businessEnum : BusinessStatusEnum.values()) {
            if (businessEnum.getChineseName().equals(chineseName)) {
                return businessEnum;
            }
        }
        return null;
    }
    //根据类型获取枚举
    public static BusinessStatusEnum getByType(String type) {
        for (BusinessStatusEnum businessEnum : BusinessStatusEnum.values()) {
            if (businessEnum.getType().equals(type)) {
                return businessEnum;
            }
        }
        return null;
    }
}

