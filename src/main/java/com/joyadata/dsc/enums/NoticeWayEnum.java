package com.joyadata.dsc.enums;

/**
 * 告警通知方式枚举
 * <AUTHOR>
 */

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum NoticeWayEnum {
    EMAIL("1", "邮箱"),
    SMS("2", "短信"),
    HTTP("3", "Http"),
    WECHAT_WORK("4", "企业微信");

    private final String code;
    private final String description;

    NoticeWayEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据 code 获取中文描述
    public static String getDescriptionByCode(String code) {
        return Arrays.stream(values())
                .filter(e -> e.code.equals(code))
                .map(NoticeWayEnum::getDescription)
                .findFirst()
                .orElse(null);
    }

    // 根据中文描述获取 code
    public static String getCodeByDescription(String description) {
        return Arrays.stream(values())
                .filter(e -> e.description.equals(description))
                .map(NoticeWayEnum::getCode)
                .findFirst()
                .orElse(null);
    }

    // 判断 code 是否在枚举中
    public static boolean isValidCode(String code) {
        return Arrays.stream(values()).anyMatch(e -> e.code.equals(code));
    }

    // 获取所有合法 code -> 描述 映射
    public static Map<String, String> codeDescMap() {
        return Arrays.stream(values())
                .collect(Collectors.toMap(NoticeWayEnum::getCode, NoticeWayEnum::getDescription));
    }
}
