package com.joyadata.dsc.enums;

/**
 * @author: <PERSON><PERSON>
 * @date: 2025/3/3 16:05
 */
public enum OperationType {

    INSERT("Insert Operation"),
    UPDATE("Update Operation"),
    DELETE("Delete Operation"),
    //未处理
    UNHANDLED("Unhandled Operation")
    ;

    private final String description;

    OperationType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

}
