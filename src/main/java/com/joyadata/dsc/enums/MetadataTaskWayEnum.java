package com.joyadata.dsc.enums;


/**
 * 采集配置采集方式枚举
 */
public enum MetadataTaskWayEnum {

    // 全量采集
    ALL(1, "全量采集", "all"),
    // 表增量采集
    TABLE_INCREMENTAL(2, "表增量采集", "tableIncremental"),
    // 字段增量采集
    FIELD_INCREMENTAL(3, "字段增量采集", "fieldIncremental"),
    ;

    private final int code;
    private final String chineseName;
    private final String englishName;

    MetadataTaskWayEnum(int code, String chineseName, String englishName) {
        this.code = code;
        this.chineseName = chineseName;
        this.englishName = englishName;
    }

    public int getCode() {
        return code;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }


    //根据中文名字获取枚举 并且区分系统和状态

    public static MetadataTaskWayEnum getByChineseName(String chineseName) {
        for (MetadataTaskWayEnum businessEnum : MetadataTaskWayEnum.values()) {
            if (businessEnum.getChineseName().equals(chineseName)) {
                return businessEnum;
            }
        }
        return null;
    }
    //根据类型获取枚举
    public static MetadataTaskWayEnum getByType(int type) {
        for (MetadataTaskWayEnum businessEnum : MetadataTaskWayEnum.values()) {
            if (businessEnum.getCode()==type) {
                return businessEnum;
            }
        }
        return null;
    }
}

