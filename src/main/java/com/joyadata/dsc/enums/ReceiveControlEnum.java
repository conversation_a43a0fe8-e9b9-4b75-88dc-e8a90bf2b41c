package com.joyadata.dsc.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * 告警频率控制
 * <AUTHOR>
 */
public enum ReceiveControlEnum {
    EACH_TIME(1, "每次触发告警时候发送"),
    FIRST_TODAY(2, "当日首次触发告警时候发送"),
    FIRST_EACH_HOUR(3, "每小时首次触发发送"),
    FIXED_PERIOD(4, "指定时段发送");

    private final int code;
    private final String description;

    ReceiveControlEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据 code 获取描述
    public static String getDescriptionByCode(int code) {
        return Arrays.stream(values())
                .filter(e -> e.code == code)
                .map(ReceiveControlEnum::getDescription)
                .findFirst()
                .orElse(null);
    }

    // 根据描述获取 code
    public static Integer getCodeByDescription(String description) {
        return Arrays.stream(values())
                .filter(e -> e.description.equals(description))
                .map(ReceiveControlEnum::getCode)
                .findFirst()
                .orElse(null);
    }

    // 判断 code 是否有效
    public static boolean isValidCode(String description) {
        return Arrays.stream(values()).anyMatch(e -> e.getDescription().equalsIgnoreCase( description));
    }

    // 获取所有 code → 描述 映射
    public static Map<Integer, String> codeDescMap() {
        return Arrays.stream(values())
                .collect(Collectors.toMap(ReceiveControlEnum::getCode, ReceiveControlEnum::getDescription));
    }
}

