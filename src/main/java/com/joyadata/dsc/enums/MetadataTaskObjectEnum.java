package com.joyadata.dsc.enums;


import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import com.joyadata.dsc.utils.MD5Util;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * 元数据枚举类，用于定义数据库对象的分类
 * 枚举了数据库中不同类型的对象，包括表、视图、存储过程、文件和文件夹
 * 每个枚举值都包括英文名称、中文名称和描述
 * <AUTHOR>
 */
public enum MetadataTaskObjectEnum {

    // 库枚举值
    DB("DB", "库", "Db"),
    SCHEMA("SCHEMA", "模式名", "Schema"),
    // 表枚举值
    TABLE("TABLE", "表", "Table"),
    // 视图枚举值
    VIEW("VIEW", "视图", "View"),
    // 存储过程枚举值
    PROCEDURE("PROCEDURE", "存储过程", "Procedure"),
    //索引
    INDEX("INDEX", "索引", "Index"),
    //函数
    FUNCTION("FUNCTION", "函数", "Function"),
    // 文件枚举值
    FILE("FILE", "文件", "File"),
    // 文件夹枚举值
    FOLDER("FOLDER", "文件夹", "Folder");


    private final String type;
    private final String chineseName;
    private final String englishName;

    MetadataTaskObjectEnum(String type, String chineseName, String englishName) {
        this.type = type;
        this.chineseName = chineseName;
        this.englishName = englishName;
    }

    public String getType() {
        return type;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public static MetadataTaskObjectEnum getMetadataTaskObjectEnum(String type) {
        for (MetadataTaskObjectEnum metadataTaskObjectEnum : MetadataTaskObjectEnum.values()) {
            if (metadataTaskObjectEnum.getType().equals(type)) {
                return metadataTaskObjectEnum;
            }
        }
        return null;
    }


    public static MetadataNodeVO getNode(String name, String type, List<MetadataNodeVO> catalogList,String level,String dbName, String schemaName) {
        // 构建表、视图、存储过程的 MetadataCatalogVO
        MetadataNodeVO metadataNodeVO = new MetadataNodeVO();
        metadataNodeVO.setCnName(name);
        metadataNodeVO.setType(type);
        metadataNodeVO.setName(name);
        metadataNodeVO.setChildrens(catalogList);
        String uuid = MD5Util.tableIdToMD5(name, type, "");
        metadataNodeVO.setUuid(uuid);
        metadataNodeVO.setLeaf(true);
        metadataNodeVO.setLevel(level);
        metadataNodeVO.setDbName(dbName);
        metadataNodeVO.setSchemaName(schemaName);
        return metadataNodeVO;
    }
    /**
     * 表 视图、索引、函数、存储过程得枚举类型集合
     */
    public static List<String> getTableOrViewOrIndexOrFunctionOrProcedure() {
        return Lists.list(TABLE.getType(), VIEW.getType(), INDEX.getType(), FUNCTION.getType(), PROCEDURE.getType());
    }

}

