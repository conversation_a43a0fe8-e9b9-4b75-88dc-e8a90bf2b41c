package com.joyadata.dsc.enums;

public enum MetadataCollectionRangeEnum {
    DATABASE_SELECTION(1, "库选择"),
    TABLE_SELECTION(2, "表选择"),
    QUICK_SELECT_TABLE(3, "快速选表"),
    BLACK_WHITE_LIST(4, "黑白名单");

    private final Integer code;
    private final String description;

    // 构造方法
    MetadataCollectionRangeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    // 获取 code 值
    public int getCode() {
        return code;
    }

    // 获取描述信息
    public String getDescription() {
        return description;
    }

    // 根据 code 获取对应的枚举项
    public static MetadataCollectionRangeEnum fromCode(int code) {
        for (MetadataCollectionRangeEnum range : MetadataCollectionRangeEnum.values()) {
            if (range.getCode() == code) {
                return range;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }

    @Override
    public String toString() {
        return this.description;
    }

    // 工具方法：根据描述查找是否合法
    public static boolean isValidDesc(String description) {
        if (description == null) return false;
        for (MetadataCollectionRangeEnum value : values()) {
            if (description.equals(value.getDescription())) {
                return true;
            }
        }
        return false;
    }

    //根据中文描述获取枚举
    // 根据 code 获取对应的枚举项
    public static MetadataCollectionRangeEnum fromDesc(String desc) {
        for (MetadataCollectionRangeEnum range : MetadataCollectionRangeEnum.values()) {
            if (range.getDescription().equals(desc)) {
                return range;
            }
        }
        return null;
    }


}

