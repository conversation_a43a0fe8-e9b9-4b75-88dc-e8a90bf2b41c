package com.joyadata.dsc.enums;

/**
 * 开关类
 *  <AUTHOR>
 */
public enum SwitchStatus {
    ON(0, "开启"),
    OFF(1, "关闭");

    private final int code;
    private final String description;

    // 构造方法
    SwitchStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // 获取 code 值
    public int getCode() {
        return code;
    }

    // 获取描述信息
    public String getDescription() {
        return description;
    }

    // 根据 code 获取对应的枚举项
    public static SwitchStatus fromCode(int code) {
        for (SwitchStatus status : SwitchStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }

}

