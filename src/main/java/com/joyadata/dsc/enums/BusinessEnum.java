package com.joyadata.dsc.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 业务系统枚举
 * <AUTHOR>
 */
public enum BusinessEnum {

    // 业务系统
    BUSINESSSYSTEM("0", "业务系统", "businessSystem"),
    //其他
    OTHERSYSTEM("4", "其他", "other"),
    // 分析系统
    ANALYSISSYSTEM("1", "分析系统", "analysisSystem"),
    //安全系统
    SECURITYSYSTEM("2", "安全系统", "securitySystem"),
    // 数据系统
    DATASYSTEM("3", "数据系统", "dataSystem"),
    ;
    private final String type;
    private final String chineseName;
    private final String englishName;

    BusinessEnum(String type, String chineseName, String englishName) {
        this.type = type;
        this.chineseName = chineseName;
        this.englishName = englishName;
    }

    public String getType() {
        return type;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    @Override
    public String toString() {
        return String.format("BusinessEnum{type='%s', chineseName='%s', englishName='%s'}", type, chineseName, englishName);
    }

    //获取业务类型
    public static List<String> getBusinessTypeList() {
        return new ArrayList<String>() {{
            add(BUSINESSSYSTEM.getChineseName());
            add(OTHERSYSTEM.getChineseName());
            add(ANALYSISSYSTEM.getChineseName());
            add(SECURITYSYSTEM.getChineseName());
            add(DATASYSTEM.getChineseName());
        }};
    }
    //根据中文名字获取枚举 并且区分系统和状态

    public static BusinessEnum getByChineseName(String chineseName) {
        for (BusinessEnum businessEnum : BusinessEnum.values()) {
            if (businessEnum.getChineseName().equals(chineseName)) {
                return businessEnum;
            }
        }
        return null;
    }
    //根据类型获取枚举
    public static BusinessEnum getByType(String type) {
        for (BusinessEnum businessEnum : BusinessEnum.values()) {
            if (businessEnum.getType().equals(type)) {
                return businessEnum;
            }
        }
        return null;
    }
}

