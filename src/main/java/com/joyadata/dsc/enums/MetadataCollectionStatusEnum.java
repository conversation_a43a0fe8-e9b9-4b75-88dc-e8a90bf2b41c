package com.joyadata.dsc.enums;

public enum MetadataCollectionStatusEnum {
    NOT_STARTED(0, "未运行"),
    RUNNING(1, "运行中"),
    SUCCESS(2, "成功"),
    FAILURE(3, "失败"),
    INTERRUPTED(4, "中断"),
    PARTIAL_SUCCESS(5, "部分成功");

    private final int code;
    private final String description;

    // 构造方法
    MetadataCollectionStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // 获取状态的 code 值
    public int getCode() {
        return code;
    }

    // 获取状态的描述信息
    public String getDescription() {
        return description;
    }

    // 根据 code 获取对应的枚举项
    public static MetadataCollectionStatusEnum fromCode(int code) {
        for (MetadataCollectionStatusEnum status : MetadataCollectionStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }

    @Override
    public String toString() {
        return this.description;
    }
}

