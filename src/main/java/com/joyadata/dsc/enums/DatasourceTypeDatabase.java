package com.joyadata.dsc.enums;

import com.dsg.database.datasource.enums.DataSourceTypeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据源类型定义
 */
public class DatasourceTypeDatabase {

    /**
     * 获取 有schema 的数据源类型的枚举  生成集合
     */
    private static final List<String> schemaDataTypeList = new ArrayList<String>() {{
        add(DataSourceTypeEnum.Oracle.getDataType().toLowerCase());
        add(DataSourceTypeEnum.PostgreSQL.getDataType().toLowerCase());
        add(DataSourceTypeEnum.SQLSERVER_2017_LATER.getDataType().toLowerCase());
        add(DataSourceTypeEnum.GaussDB.getDataType().toLowerCase());
        add(DataSourceTypeEnum.DWS_PG.getDataType().toLowerCase());
        add(DataSourceTypeEnum.TBase.getDataType().toLowerCase());
        add(DataSourceTypeEnum.TDSQL_FOR_PG.getDataType().toLowerCase());
        add(DataSourceTypeEnum.KINGBASE8.getDataType().toLowerCase());
        add(DataSourceTypeEnum.GREENPLUM6.getDataType().toLowerCase());
        add(DataSourceTypeEnum.GREENPLUM_PostgreSQL.getDataType().toLowerCase());
        add(DataSourceTypeEnum.DMDB.getDataType().toLowerCase());
        add(DataSourceTypeEnum.ADB_PostgreSQL.getDataType().toLowerCase());
        add(DataSourceTypeEnum.DB2.getDataType().toLowerCase());
        add(DataSourceTypeEnum.TDSQL_FOR_ORACLE.getDataType().toLowerCase());

    }};

    public static List<String> schemaDataTypeList() {
        return schemaDataTypeList;
    }
    /**
     * 获取需要切库 重置url得数据源集合
     */
    private static final List<String> pgDataTypeList = new ArrayList<String>() {{
        add(DataSourceTypeEnum.PostgreSQL.getDataType().toLowerCase());
        add(DataSourceTypeEnum.DWS_PG.getDataType().toLowerCase());
        add(DataSourceTypeEnum.SQLServer.getDataType().toLowerCase());
        add(DataSourceTypeEnum.GaussDB.getDataType().toLowerCase());
        add(DataSourceTypeEnum.TBase.getDataType().toLowerCase());
        add(DataSourceTypeEnum.TDSQL_FOR_PG.getDataType().toLowerCase());
        add(DataSourceTypeEnum.KINGBASE8.getDataType().toLowerCase());
        add(DataSourceTypeEnum.GREENPLUM6.getDataType().toLowerCase());
        add(DataSourceTypeEnum.GREENPLUM_PostgreSQL.getDataType().toLowerCase());
        add(DataSourceTypeEnum.DMDB.getDataType().toLowerCase());
        add(DataSourceTypeEnum.ADB_PostgreSQL.getDataType().toLowerCase());
        add(DataSourceTypeEnum.DB2.getDataType().toLowerCase());
        add(DataSourceTypeEnum.TDSQL_FOR_ORACLE.getDataType().toLowerCase());
    }};
    public static List<String> pgDataTypeList() {
        return pgDataTypeList;
    }
}
