package com.joyadata.dsc.enums;



import java.util.ArrayList;
import java.util.List;

/**
 * 业务系统枚举
 */
public enum BusinessImportEnum {



    // 一般
    INFO("0", "一般", "0"),

    // 核心
    CORE("1", "核心", "1"),
     // 重要
    IMPORTANT("2", "重要", "2"),
    ;

    private final String type;
    private final String chineseName;
    private final String englishName;

    BusinessImportEnum(String type, String chineseName, String englishName) {
        this.type = type;
        this.chineseName = chineseName;
        this.englishName = englishName;
    }

    public String getType() {
        return type;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    @Override
    public String toString() {
        return String.format("BusinessEnum{type='%s', chineseName='%s', englishName='%s'}", type, chineseName, englishName);
    }


    public static List<String> getImportanceDegreeList() {
        return new ArrayList<String>() {{
            add(INFO.getChineseName());
            add(IMPORTANT.getChineseName());
            add(CORE.getChineseName());
        }};
    }

    //根据中文名字获取枚举 并且区分系统和状态

    public static BusinessImportEnum getByChineseName(String chineseName) {
        for (BusinessImportEnum businessEnum : BusinessImportEnum.values()) {
            if (businessEnum.getChineseName().equals(chineseName)) {
                return businessEnum;
            }
        }
        return null;
    }
    //根据类型获取枚举
    public static BusinessImportEnum getByType(String type) {
        for (BusinessImportEnum businessEnum : BusinessImportEnum.values()) {
            if (businessEnum.getType().equals(type)) {
                return businessEnum;
            }
        }
        return null;
    }
}

