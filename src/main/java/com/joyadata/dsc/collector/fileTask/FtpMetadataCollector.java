package com.joyadata.dsc.collector.fileTask;


import com.dsg.database.datasource.dto.DatasourceDTO;
import com.joyadata.dsc.collector.FileCollector;
import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * MySQL元数据收集器类，用于实现元数据收集相关的功能
 * 该类通过实现MetadataCollector接口，定义了如何从MySQL数据源中收集元数据
 * <AUTHOR>
 */
@Slf4j
@Component
public class FtpMetadataCollector extends FileCollector  {



}
