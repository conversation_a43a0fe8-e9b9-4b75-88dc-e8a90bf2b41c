package com.joyadata.dsc.collector;

import com.dsg.database.datasource.dto.DatasourceDTO;
import com.joyadata.dsc.model.datasoure.DatasourceInfo;
import com.joyadata.dsc.model.datasoure.vo.DatasourceAuthVO;
import com.joyadata.dsc.model.metadata.MetadataTableCommon;
import com.joyadata.dsc.model.metadata.MetadataTask;
import com.joyadata.dsc.model.metadata.dto.MetadataDbDTO;
import com.joyadata.dsc.model.metadata.dto.MetadataTaskDTO;
import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import com.joyadata.dsc.model.metadata.vo.MetadataTaskRecordResultVO;

import java.util.List;

/**
 * 定义了一个元数据收集器接口，用于从不同的数据源中收集元数据信息
 * <AUTHOR>
 */
public interface MetadataCollector {

    /**
     * 1.获取数据源配置的所有库，构建节点
     */
    List<MetadataNodeVO> getAllDbsNode(String datasourceId,DatasourceDTO datasourceDTO);
    /**
     * 1.获取数据源配置的所有模式，构建节点
     */
    List<MetadataNodeVO> getAllSchemaNode(String datasourceId,DatasourceDTO datasourceDTO);


    /**
     * 2.获取表 、视图、索引、函数、存储过程等常量节点
     */
    List<MetadataNodeVO> getCommonNode(String parentId,String level,String dbName,String schemaName);

    /**
     * 3.获取表、视图---根据库名获取表、视图
     */
    List<MetadataNodeVO> getTableNodesByDb(String datasourceId, MetadataDbDTO dto,DatasourceDTO datasourceDTO);

    /**
     * 4获取索引--根据库名或者表名获取索引
     */
    List<MetadataNodeVO> getIndexNodesByDb(String datasourceId, MetadataDbDTO dto,DatasourceDTO datasourceDTO);

    /**
     * 5获取函数或者存储过程--根据库名函数或者存储过程
     */
    List<MetadataNodeVO> getFunctionNodesByDb(String datasourceId, MetadataDbDTO dto,DatasourceDTO datasourceDTO);

    /**
     * 6从给定的数据源信息ID中获取需要采集的数据库
     *
     * @param datasourceDTO 数据源信息对像
     */
    List<String> getAllDbs(DatasourceDTO datasourceDTO, MetadataTask task);


    /**
     * 7从数据源收集元数据
     * <p>
     * 本方法的主要作用是从给定的数据源信息中收集元数据，并执行相关的元数据任务
     * 它通过利用DatasourceInfo对象，以及DatasourceInfoUtil工具类来完成这一过程
     *
     * @param datasourceId 包含数据源连接信息的对象，例如数据库连接细节
     * @param task           要执行的元数据任务，定义了收集和处理元数据的具体逻辑
     */
    String collectMetadata(String datasourceId, MetadataTask task, String metadataTaskRecord, MetadataTaskDTO taskDTO);


    /**
     * 获取数据库名称列表:数据源授权db或者schema Node
     *
     * @param datasourceinfoId
     * @param dbName
     * @return
     */
     List<MetadataNodeVO> getDbNameOrSchemaNode(String datasourceinfoId, String dbName, String datasourceTypeName);
    /**
     * 获取数据库名称列表:数据源授权列表
     *
     * @param datasourceinfoId
     * @param dbName
     * @return
     */
     List<DatasourceAuthVO> getDbNameOrSchemaList(String datasourceinfoId, String dbName, String datasourceTypeName);
    /**
     * 获取表列表：数据源授权
     *
     * @param table
     * @param page
     * @param pager
     * @param tableNames
     * @return
     */
     List<DatasourceAuthVO> getTableList(MetadataTableCommon table, Integer page, Integer pager, List<String> tableNames);

    /**
     * 采集记录统计结果
     */
     List<MetadataTaskRecordResultVO> getResultByTaskRecordId(String id, String opt);

    /**
     * 判断数据源是否采集过
     * @param datasourceinfoId
     * @return
     */
    Boolean isCollection(String datasourceinfoId);
}
