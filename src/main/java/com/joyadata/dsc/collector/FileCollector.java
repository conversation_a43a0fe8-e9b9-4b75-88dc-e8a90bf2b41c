package com.joyadata.dsc.collector;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.dsg.database.datasource.vo.DbTableVO;
import com.joyadata.dsc.constants.Constants;
import com.joyadata.dsc.enums.AlertStatusEnum;
import com.joyadata.dsc.enums.MetadataCollectionStatusEnum;
import com.joyadata.dsc.enums.MetadataTaskObjectEnum;
import com.joyadata.dsc.enums.OperationType;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.dto.AlarmMsg;
import com.joyadata.dsc.model.datasoure.vo.DatasourceAuthVO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectProgressVO;
import com.joyadata.dsc.model.metadata.*;
import com.joyadata.dsc.model.metadata.dto.GatherMetadataDTO;
import com.joyadata.dsc.model.metadata.dto.MetadataDbDTO;
import com.joyadata.dsc.model.metadata.dto.MetadataTaskDTO;
import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import com.joyadata.dsc.model.metadata.vo.MetadataTaskRecordResultVO;
import com.joyadata.dsc.service.*;
import com.joyadata.dsc.utils.DatasourceUtil;
import com.joyadata.dsc.utils.IdUtils;
import com.joyadata.dsc.utils.MD5Util;
import com.joyadata.exception.AppErrorException;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.WhereCondition;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件采集器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public abstract class FileCollector implements MetadataCollector {
    public String metadataTaskRecordId;

    public MetadataTaskDTO taskDTO;
    /**
     * 平台已采集的表集合
     */
    List<MetadataTableCommon> tablesToCollect = new ArrayList<>();

    /**
     * 此次采集需要判断源库是否删除得表
     */
    List<MetadataTableCommon> isTablesToDelete = new ArrayList<>();


    /**
     * 源库取的所有表 、视图、函数、存储过程的集合
     */
    List<MetadataTableCommon> datasourceMatchedTables = new ArrayList<>();


    /**
     * 平台已获取的表字段的map
     * key 为表id
     */
    Map<String, List<MetadataColumnCommon>> qfMetadataColumnCommonsMap = new HashMap<>();


    @Autowired
    public MetadataTaskRecordService metadataTaskRecordService;


    @Autowired
    public MetadataTableCommonService metadataTableCommonService;

    @Autowired
    public MetadataTableCommonDetailService metadataTableCommonDetailService;
    @Autowired
    public MetadataColumnCommonService metadataColumnCommonService;
    @Autowired
    public MetadataColumnCommonDetailService metadataColumnCommonDetailService;
    @Autowired
    DatasourceUtil datasourceUtil;

    @Autowired
    public DatasourceAlertService datasourceAlertService;
    @Autowired
    public SendAlertMsgService sendAlertMsgService;

    @Autowired
    public MetadataTaskStepService metadataTaskStepService;

    @Autowired
    public DatasourceInfoService datasourceInfoService;

    /**
     * 获取DatasourceDTO datasourceDTO 对象
     */
    public DatasourceDTO getDatasourceDTO(String datasourceId) {
        DatasourceDTO datasourceDTO = datasourceUtil.getDatasourceDTO(datasourceId);
        return datasourceDTO;
    }

    /**
     * 获取文件采集路径
     */
    @Override
    public List<MetadataNodeVO> getAllDbsNode(String datasourceId, DatasourceDTO datasourceDTO) {
        if (Objects.isNull(datasourceDTO)) {
            datasourceDTO = getDatasourceDTO(datasourceId);
        }
        //判断数据源对象是否为null
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", datasourceId);
        }
        List<MetadataNodeVO> metadataNodeVOS = new ArrayList<>();
        //先获取数据源有没有登记路径
        String path = datasourceDTO.getPath();
        if (StringUtils.isNotEmpty(path)) {
            MetadataNodeVO metadataCalalogVO = new MetadataNodeVO();
            metadataCalalogVO.setDatasourceInfoId(datasourceId);
            metadataCalalogVO.setName(path);
            metadataCalalogVO.setCnName(MetadataTaskObjectEnum.FOLDER.getChineseName());
            String uuid = MD5Util.resourceToMD5(datasourceId, path, "", path, MetadataTaskObjectEnum.FOLDER.getType(), null);
            metadataCalalogVO.setUuid(uuid);
            metadataCalalogVO.setType(MetadataTaskObjectEnum.FOLDER.getType());
            metadataCalalogVO.setLevel("1");
            metadataNodeVOS.add(metadataCalalogVO);
            return metadataNodeVOS;
        }
        //todo  获取文件目录
        List<String> allDbs = DatasourceUtils.getAllDbs(datasourceDTO);
        for (String allDb : allDbs) {
            MetadataNodeVO metadataCalalogVO = new MetadataNodeVO();
            metadataCalalogVO.setDatasourceInfoId(datasourceId);
            metadataCalalogVO.setName(allDb);
            metadataCalalogVO.setCnName(MetadataTaskObjectEnum.FOLDER.getChineseName());
            String uuid = MD5Util.resourceToMD5(datasourceId, allDb, "", allDb, MetadataTaskObjectEnum.FOLDER.getType(), null);
            metadataCalalogVO.setUuid(uuid);
            metadataCalalogVO.setType(MetadataTaskObjectEnum.FOLDER.getType());
            metadataCalalogVO.setLevel("1");
            metadataNodeVOS.add(metadataCalalogVO);
        }
        return metadataNodeVOS;
    }


    /**
     * 1.获取数据源配置的所有模式，构建节点
     */
    @Override
    public List<MetadataNodeVO> getAllSchemaNode(String datasourceId, DatasourceDTO datasourceDTO) {
        return new ArrayList<>();
    }


    /**
     * 2.获取表 、视图、索引、函数、存储过程等常量节点
     */
    @Override
    public List<MetadataNodeVO> getCommonNode(String parentId, String level, String dbName, String schemaName) {
        return new ArrayList<>();
    }

    /**
     * 3.获取表、视图---根据库名获取表、视图
     */
    @Override
    public List<MetadataNodeVO> getTableNodesByDb(String datasourceId, MetadataDbDTO dto, DatasourceDTO datasourceDTO) {
        return new ArrayList<>();
    }

    /**
     * 4获取索引--根据库名或者表名获取索引
     */
    @Override
    public List<MetadataNodeVO> getIndexNodesByDb(String datasourceId, MetadataDbDTO dto, DatasourceDTO datasourceDTO) {
        return new ArrayList<>();
    }

    /**
     * 5获取函数或者存储过程--根据库名函数或者存储过程
     */
    @Override
    public List<MetadataNodeVO> getFunctionNodesByDb(String datasourceId, MetadataDbDTO dto, DatasourceDTO datasourceDTO) {
        return new ArrayList<>();
    }

    /**
     * 6从给定的数据源信息ID中获取需要采集的数据库
     *
     * @param datasourceDTO 数据源信息对像
     */
    @Override
    public List<String> getAllDbs(DatasourceDTO datasourceDTO, MetadataTask task) {
        //需要采集的路径
        List<String> allDbs = new ArrayList<>();

        //不符合要求的路径
        List<String> notAllowPath = new ArrayList<>();

        //获取采集路径:根路径
        String filePath = task.getFilePath();
        //获取指定路径
        String filePathSpecified = task.getFilePathSpecified();
        if (StringUtils.isNotEmpty(filePathSpecified)) {
            //校验路径
            //filePathSpecified 以,分隔生成list
            List<String> filePathSpecifiedList = StrUtil.split(filePathSpecified, ",");
            //校验每个路径是否以filePath开头
            for (String file : filePathSpecifiedList) {
                boolean b = file.startsWith(filePath);
                if (!b) {
                    notAllowPath.add(file);
                } else {
                    allDbs.add(file);
                }
            }
        } else {
            allDbs.add(filePath);
        }
        //将dbs 去重
        if (!allDbs.isEmpty()) {
            allDbs = allDbs.stream().distinct().collect(Collectors.toList());
        }
        //将不满足要求的路径加入到采集失败的记录中
        if (!notAllowPath.isEmpty()) {
            List<MetadataTableCommonDetail> recordTableDetailsList = new ArrayList<>();
            for (String table : notAllowPath) {
                MetadataTableCommonDetail recordTableDetails = getTablesToCollectByEmptyPath(table, "采集路径不合法", datasourceDTO);
                recordTableDetailsList.add(recordTableDetails);
            }
            try {
                addInBatchesMetadataTableCommonDetailS(recordTableDetailsList, 100);
            } catch (Exception e) {
                log.error("保存不符合标准的路径采集失败记录失败", e);
            }
        }
        return allDbs;
    }

    /**
     * 获取数据库名称列表:数据源授权db或者schema Node
     *
     * @param datasourceinfoId
     * @param dbName
     * @return
     */
    @Override
    public List<MetadataNodeVO> getDbNameOrSchemaNode(String datasourceinfoId, String dbName, String datasourceTypeName) {
        return new ArrayList<>();
    }

    /**
     * 获取数据库名称列表:数据源授权列表
     *
     * @param datasourceId
     * @param dbName
     * @return
     */
    @Override
    public List<DatasourceAuthVO> getDbNameOrSchemaList(String datasourceId, String dbName, String datasourceTypeName) {
        List<DatasourceAuthVO> datasourceAuths = new ArrayList<>();
        //获取一级目录名称
        IQueryWrapper<MetadataTableCommon> eq = metadataTableCommonService.getQuery()
                .eq("datasourceInfoId", datasourceId)
                .eq("fileLevel", "1");
        //名称不为空时 搜索
        if (StringUtils.isNotEmpty(dbName)) {
            eq.like("name", dbName);
        }
        List<MetadataTableCommon> dbNames = eq.list();
        if(CollUtil.isEmpty(dbNames)){
            return datasourceAuths;
        }
        //dbNames 不为null或者空时，去重
        if (CollUtil.isNotEmpty(dbNames)){
            //去重
            dbNames =  dbNames.stream()
                    .collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MetadataTableCommon::getName))))
                    .stream()
                    .collect(Collectors.toList());
        }
        for (MetadataTableCommon metadataTable : dbNames) {
            DatasourceAuthVO datasourceAuth = new DatasourceAuthVO();
            datasourceAuth.setDbName(metadataTable.getName());
            datasourceAuth.setSchemaName(metadataTable.getDbName());
            datasourceAuth.setDatasourceInfoId(datasourceId);
            datasourceAuth.setIsAuth(false);
            datasourceAuth.setProjectIds(new ArrayList<>());
            String uuid = MD5Util.resourceToMD5(datasourceId, metadataTable.getDbName(), metadataTable.getSchemaName(),metadataTable.getName(), metadataTable.getType(), metadataTable.getTableName());
            datasourceAuth.setUuid(uuid);
            datasourceAuth.setMetadataTableType(metadataTable.getType());
            datasourceAuths.add(datasourceAuth);
        }

        return datasourceAuths;
    }

    /**
     * 获取表列表：数据源授权
     *
     * @param table
     * @param page
     * @param pager
     * @param tableNames
     * @return
     */
    @Override
    public List<DatasourceAuthVO> getTableList(MetadataTableCommon table, Integer page, Integer pager, List<String> tableNames) {
        return new ArrayList<>();
    }

    /**
     * 按层级采集
     *
     * @param datasourceDTO
     * @param startPath
     * @param currentLevel
     * @param task
     */
    private void collectFtpMetadataByLevel(
            DatasourceDTO datasourceDTO,
            String startPath,
            int currentLevel,
            MetadataTask task,
            MetadataTaskDTO metadataTaskDTO,
            GatherMetadataDTO gatherMetadataDTO
    ) {


        boolean recursive = false;
        Integer maxLevel = task.getMaxLevel();
        Integer isRecursion = task.getIsRecursion();
        if (1 == isRecursion) {
            recursive = true;
        }
        // 决定当前是否使用内部递归
        boolean innerRecursive = maxLevel == null || (maxLevel != null && currentLevel > maxLevel && recursive);

        // 获取当前层级元数据
        datasourceDTO.setPath(startPath);
        datasourceDTO.setIncludeDir(true);
        if (innerRecursive) {
            datasourceDTO.setRecursive(true);
        } else {
            datasourceDTO.setRecursive(false);
        }
        datasourceDTO.setRegexDir(task.getBlacklist());
        datasourceDTO.setCurrentLevel(currentLevel);
        boolean running = metadataTaskDTO.isRunning();
        datasourceDTO.setStopFlag(!running);

        List<MetadataTableCommon> dbTableToMetadataTables = new ArrayList<>();
        try {
            dbTableToMetadataTables = getDbTableToMetadataTables(task, datasourceDTO);
            if (CollUtil.isEmpty(dbTableToMetadataTables)) {
                log.info("数据源[{}]路径[{}],采集资源为空", datasourceDTO.getDataName(), startPath);
//                MetadataTableCommonDetail recordTableDetails = getTablesToCollectByEmptyPath(startPath, "采集资源为空", datasourceDTO);
                return;
            }
            //判断结束标识，是否结束采集
            if (!taskDTO.isRunning()) {
                log.info("数据源[{}]路径[{}],采集资源中断停止", datasourceDTO.getDataName(), startPath);
                //更新采集统计
                MetadataTaskRecord metadataTaskRecordCount = new MetadataTaskRecord();
                BeanUtil.copyProperties(gatherMetadataDTO, metadataTaskRecordCount);
                metadataTaskRecordCount.setStatus(MetadataCollectionStatusEnum.INTERRUPTED.getCode());
                metadataTaskRecordCount.setId(metadataTaskRecordId);
                metadataTaskRecordCount.setErrorLog("采集中断停止");
                metadataTaskRecordService.update(metadataTaskRecordId, metadataTaskRecordCount);
                return;
            }
            //处理每条元数据信息
            processTableMetadata(dbTableToMetadataTables, task, startPath, datasourceDTO, gatherMetadataDTO);
        } catch (Exception e) {
            List<MetadataTableCommonDetail> recordTableDetailsList = new ArrayList<>();
            MetadataTableCommonDetail recordTableDetails = getTablesToCollectByEmptyPath(startPath, e.getMessage(), datasourceDTO);
            recordTableDetailsList.add(recordTableDetails);
            log.error("获取元数据失败：{}", e.getMessage());
            try {
                addInBatchesMetadataTableCommonDetailS(recordTableDetailsList, 100);
            } catch (Exception e1) {
                log.error("保存采集失败记录失败", e1);
            }

            return;
        }

        // 如果使用了 getFtpMetaData 的内部递归，则不需要再递归了
        if (innerRecursive) {
            return;
        }
        // 否则手动递归子目录（模拟层级递归）
        for (MetadataTableCommon vo : dbTableToMetadataTables) {
            if (MetadataTaskObjectEnum.FOLDER.getType().equalsIgnoreCase(vo.getType())) {
                collectFtpMetadataByLevel(
                        datasourceDTO,
                        vo.getFilePath(),
                        currentLevel + 1,
                        task,
                        metadataTaskDTO,
                        gatherMetadataDTO
                );
            }
        }
    }


    /**
     * 路径采集为空，获取采集异常构建记录
     */
    public MetadataTableCommonDetail getTablesToCollectByEmptyPath(String filePath, String log, DatasourceDTO datasourceDTO ){
        MetadataTableCommonDetail recordTableDetails = new MetadataTableCommonDetail();
        recordTableDetails.setMetadataTaskRecordId(metadataTaskRecordId);
        recordTableDetails.setDatasourceInfoId(datasourceDTO.getDatasourceId());
        recordTableDetails.setType(MetadataTaskObjectEnum.FOLDER.getType());
        recordTableDetails.setStatus(MetadataCollectionStatusEnum.FAILURE.getCode());
        recordTableDetails.setName(filePath);
        recordTableDetails.setDatasourceType(datasourceDTO.getDataType());
        //详情id metadataTaskRecordId_tableId
        String uuid = IdUtils.simpleUUID();
        recordTableDetails.setId(uuid);
        recordTableDetails.setOpt(OperationType.UNHANDLED.name());
        recordTableDetails.setManager(datasourceDTO.getManager());
        //todo 文件类型
        recordTableDetails.setFilePath(filePath);
        recordTableDetails.setFileType(MetadataTaskObjectEnum.FOLDER.getType());
        recordTableDetails.setFileParentPath(filePath);
        recordTableDetails.setDbName(filePath);
        recordTableDetails.setErrorLog(log);
        return recordTableDetails;

    }

    /**
     * 处理表元数据
     *
     * @param task              任务
     * @param gatherMetadataDTO 统计对象
     * @throws AppErrorException 异常
     */
    public void processTableMetadata(List<MetadataTableCommon> datasourceMatchedTables,
                                     MetadataTask task,
                                     String startPath,
                                     DatasourceDTO datasourceDTO,
                                     GatherMetadataDTO gatherMetadataDTO) throws AppErrorException {
        //先处理删除数据
        //获取此路径下采集的表
        List<MetadataTableCommon> tablesToCollect = getTablesToCollect(task, 0, startPath, false);

        Integer configTotal = gatherMetadataDTO.getConfigTotal();
        gatherMetadataDTO.setConfigTotal(configTotal + datasourceMatchedTables.size());

        // todo 获取要删除得数据
        List<MetadataTableCommon> metadataTablesToDelete = getTablesToDelete(datasourceMatchedTables, tablesToCollect);
        if (CollUtil.isNotEmpty(metadataTablesToDelete)) {
            metadataTableCommonService.batchUpdate(metadataTablesToDelete);
            //todo  删除表记录
            List<MetadataTableCommonDetail> metadataTableCommonDetail = getMetadataTableCommonDetail(metadataTablesToDelete, OperationType.DELETE.name(),MetadataCollectionStatusEnum.SUCCESS.getCode());
            addInBatchesMetadataTableCommonDetailS(metadataTableCommonDetail, 1000);
            log.info("数据源[{}],此次{}采集删除资源数量[{}]", datasourceDTO.getDataName(), startPath, metadataTablesToDelete.size());

            //统计删除数量
            //按type 进行分组 生成map
            Map<String, List<MetadataTableCommon>> metadataTableCommonMap = metadataTablesToDelete.stream().collect(Collectors.groupingBy(MetadataTableCommon::getType));
            if (metadataTableCommonMap.containsKey(MetadataTaskObjectEnum.FOLDER.getType())) {
                //目录写到table里面 文件写到字段里面,得叠加数据
                Integer deleteTableCount = gatherMetadataDTO.getDeleteTableCount();
                gatherMetadataDTO.setDeleteTableCount(deleteTableCount + metadataTableCommonMap.get(MetadataTaskObjectEnum.FOLDER.getType()).size());
            }
            if (metadataTableCommonMap.containsKey(MetadataTaskObjectEnum.FILE.getType())) {
                //目录写到table里面 文件写到字段里面
                Integer deleteColumnCount = gatherMetadataDTO.getDeleteColumnCount();
                gatherMetadataDTO.setDeleteColumnCount(deleteColumnCount + metadataTableCommonMap.get(MetadataTaskObjectEnum.FILE.getType()).size());
            }
            //删除总数
            Integer deleteTotal = gatherMetadataDTO.getDeleteTotal();
            gatherMetadataDTO.setDeleteTotal(deleteTotal + metadataTablesToDelete.size());
            //todo  表变更告警信息
            //检查是否发送告警
            boolean b = checkAlarm(task.getDatasourceInfoId(), AlertStatusEnum.B402005);
            if (b) {
                //发送消息
                StringBuilder changeInfo = new StringBuilder();
                //删除表，将表名拼接成字符串
                String metadataColu = metadataTablesToDelete.stream().map(MetadataTableCommon::getName).collect(Collectors.joining(","));
                changeInfo.append("删除资源：").append(metadataColu).append("，");
                sendAlarm(datasourceDTO, AlertStatusEnum.B402005, task, null, changeInfo.toString());
            }
        }
        //获取平台已删除的表

        //平台已删除的表集合
        Map<String, MetadataTableCommon> metadataTableCommonMapToDelete = new HashMap<>();
        List<MetadataTableCommon> tablesToDelete = getTablesToCollect(task, 1, startPath,  true);
        if (CollUtil.isNotEmpty(tablesToDelete)) {
            //将部门列表转为map，id作为key，Dept 作为value 重复值取第一条
            metadataTableCommonMapToDelete = tablesToDelete.stream().collect(Collectors.toMap(MetadataTableCommon::getId, metadataTableCommon -> metadataTableCommon));
        }
        //todo 新增
        //保存文件元数据
        //获取新增
        List<MetadataTableCommon> metadataTablesToAdd = getTablesToAdd(datasourceMatchedTables, tablesToCollect);
        if (CollUtil.isNotEmpty(metadataTablesToAdd)) {

            List<MetadataTableCommonDetail> metadataTableCommonDetail = getMetadataTableCommonDetail(metadataTablesToAdd, OperationType.INSERT.name(),MetadataCollectionStatusEnum.SUCCESS.getCode());
            addInBatchesMetadataTableCommonDetailS(metadataTableCommonDetail, 1000);
            log.info("数据源[{}],此次{}采集新增资源数量[{}]", datasourceDTO.getDataName(), startPath, metadataTablesToAdd.size());
            for (MetadataTableCommon metadataTableCommon : metadataTablesToAdd) {
                if( metadataTableCommonMapToDelete.containsKey(metadataTableCommon.getId())){
                    metadataTableCommon.setDelFlag(false);
                    metadataTableCommonService.update(metadataTableCommon,true);
                }else{
                    metadataTableCommonService.add(metadataTableCommon);
                }
            }

            //统计 新增数量
            Map<String, List<MetadataTableCommon>> metadataTableCommonMap = metadataTablesToAdd.stream().collect(Collectors.groupingBy(MetadataTableCommon::getType));
            if (metadataTableCommonMap.containsKey(MetadataTaskObjectEnum.FOLDER.getType())) {
                //目录写到table里面 文件写到字段里面,得叠加数据
                Integer addTableCount = gatherMetadataDTO.getAddTableCount();
                gatherMetadataDTO.setAddTableCount(addTableCount + metadataTableCommonMap.get(MetadataTaskObjectEnum.FOLDER.getType()).size());
            }
            if (metadataTableCommonMap.containsKey(MetadataTaskObjectEnum.FILE.getType())) {
                //目录写到table里面 文件写到字段里面
                Integer addColumnCount = gatherMetadataDTO.getAddColumnCount();
                gatherMetadataDTO.setAddColumnCount(addColumnCount + metadataTableCommonMap.get(MetadataTaskObjectEnum.FILE.getType()).size());
            }
            //检查是否发送告警
            boolean b = checkAlarm(task.getDatasourceInfoId(), AlertStatusEnum.B402005);
            if (b) {
                //发送消息
                StringBuilder changeInfo = new StringBuilder();
                //删除表，将表名拼接成字符串
                String metadataColAdd = metadataTablesToAdd.stream().map(MetadataTableCommon::getName).collect(Collectors.joining(","));
                changeInfo.append("新增资源：").append(metadataColAdd).append("，");
                sendAlarm(datasourceDTO, AlertStatusEnum.B402005, task, null, changeInfo.toString());
            }
        }
        //todo 更新
        //获取修改
        List<MetadataTableCommon> metadataTablesToUpdate = getTablesToUpdate(datasourceMatchedTables, tablesToCollect);
        List<MetadataTableCommon> metadataTablesToUpdateNew = new ArrayList<>();
        if (CollUtil.isNotEmpty(metadataTablesToUpdate)) {

            List<MetadataTableCommonDetail> metadataTableCommonDetail = getMetadataTableCommonDetail(metadataTablesToUpdate, OperationType.UNHANDLED.name(),MetadataCollectionStatusEnum.SUCCESS.getCode());
            addInBatchesMetadataTableCommonDetailS(metadataTableCommonDetail, 1000);

            //todo  告警信息
            //检查是否发送告警
            boolean b = checkAlarm(task.getDatasourceInfoId(), AlertStatusEnum.B402005);
            //将tablesToCollect集合中的表 ，将id 作为key ，重复值取第一条 MetadataTableCommon为value 生成map
            Map<String, MetadataTableCommon> metadataTableCommonMap = tablesToCollect.stream().collect(Collectors.toMap(MetadataTableCommon::getId, metadataTableCommon -> metadataTableCommon));
            for (MetadataTableCommon metadataTableCommon : metadataTablesToUpdate) {
                MetadataTableCommon metadataTableCommon1 = metadataTableCommonMap.get(metadataTableCommon.getId());
                StringBuilder changeInfo = new StringBuilder();
                //比较表和以采集过的此表是否要更新:权限，大小，修改时间
                boolean isTableUpdate = updateTable(metadataTableCommon, metadataTableCommon1, changeInfo);
                if (isTableUpdate) {
                    metadataTableCommonService.update(metadataTableCommon);
                    updateMetadataTaskRecordTableDetails(metadataTableCommon.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), "", OperationType.UPDATE.name(), metadataTableCommon.getMetadataColumnTotal());
                    if (b) {
                        //发送消息
                        sendAlarm(datasourceDTO, AlertStatusEnum.B402005, task, metadataTableCommon, changeInfo.toString());
                    }
                    metadataTablesToUpdateNew.add(metadataTableCommon);
                }
            }

            if (CollUtil.isNotEmpty(metadataTablesToUpdateNew)) {
                //统计数据
                Map<String, List<MetadataTableCommon>> metadataTableCommonMapUpdate = metadataTablesToUpdateNew.stream().collect(Collectors.groupingBy(MetadataTableCommon::getType));
                if (metadataTableCommonMapUpdate.containsKey(MetadataTaskObjectEnum.FOLDER.getType())) {
                    //目录写到table里面 文件写到字段里面,得叠加数据
                    Integer updateTableCount = gatherMetadataDTO.getUpdateTableCount();
                    gatherMetadataDTO.setUpdateTableCount(updateTableCount + metadataTableCommonMapUpdate.get(MetadataTaskObjectEnum.FOLDER.getType()).size());
                }
                if (metadataTableCommonMapUpdate.containsKey(MetadataTaskObjectEnum.FILE.getType())) {
                    //目录写到table里面 文件写到字段里面
                    Integer updateColumnCount = gatherMetadataDTO.getUpdateColumnCount();
                    gatherMetadataDTO.setUpdateColumnCount(updateColumnCount + metadataTableCommonMapUpdate.get(MetadataTaskObjectEnum.FILE.getType()).size());
                }

            }

        }

    }


    /**
     * 7从数据源收集元数据
     * <p>
     * 本方法的主要作用是从给定的数据源信息中收集元数据，并执行相关的元数据任务
     * 它通过利用DatasourceInfo对象，以及DatasourceInfoUtil工具类来完成这一过程
     *
     * @param datasourceId 包含数据源连接信息的对象，例如数据库连接细节
     * @param task         要执行的元数据任务，定义了收集和处理元数据的具体逻辑
     */
    @Override
    public String collectMetadata(String datasourceId, MetadataTask task, String metadataTaskRecord, MetadataTaskDTO taskDTO) {
        metadataTaskRecordId = metadataTaskRecord;
        this.taskDTO = taskDTO;
        DatasourceDTO datasourceDTO = new DatasourceDTO();
        /**
         * 步骤1：获取数据源对象
         */
        String step1Id = IdUtils.simpleUUID();
        try {
            //1.获取数据源对象
            MetadataTaskStep save = metadataTaskStepService.save(step1Id,1, Constants.STEP_NAME_CONNECT, Constants.STEP_CONTENT_CONNECT, metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), null, new Date(), null);
            //测试连接
            DatasourceConnectProgressVO datasourceConnectProgressVO = datasourceInfoService.connectById(datasourceId);
            Integer status = datasourceConnectProgressVO.getStatus();
            if(0 ==status){
                updateMetadataTaskRecord(metadataTaskRecordId, "数据源连接失败:"+datasourceConnectProgressVO.getMsg(), MetadataCollectionStatusEnum.FAILURE.getCode(), 0, 0, 0);
                clearGlobalParam();
                //检查是否发送告警
                boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
                if (b) {
                    //构建发送消息实体
                    sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
                }
                metadataTaskStepService.update(step1Id, MetadataCollectionStatusEnum.FAILURE.getCode(), "数据源连接失败:"+datasourceConnectProgressVO.getMsg(), new Date());
                //更新操作步骤
                return metadataTaskRecordId;
            }
            datasourceDTO = getDatasourceDTO(datasourceId);
        } catch (AppErrorException e) {
            log.error("获取数据源对象失败{}", e.getMessage());
            updateMetadataTaskRecord(metadataTaskRecordId, e.getResult().toString().toString(), MetadataCollectionStatusEnum.FAILURE.getCode(), 0, 0, 0);
            clearGlobalParam();
            //检查是否发送告警
            boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
            if (b) {
                //构建发送消息实体
                sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
            }
            metadataTaskStepService.update(step1Id,MetadataCollectionStatusEnum.FAILURE.getCode(),e.getResult().toString(),new Date());
            return metadataTaskRecordId;

        }
        //获取要采集的路径
        List<String> allDbs = new ArrayList<>();
        /**
         * 步骤二名称：资源发现
         */
        String step2Id = IdUtils.simpleUUID();
        try {
            MetadataTaskStep save = metadataTaskStepService.save(step2Id,2, Constants.STEP_NAME_DISCOVERY, Constants.STEP_CONTENT_DISCOVERY, metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), null, new Date(), null);
            allDbs = getAllDbs(datasourceDTO, task);
        } catch (Exception e) {
            log.error("获取要采集的路径失败{}", e.getMessage());
            updateMetadataTaskRecord(metadataTaskRecordId, e.getMessage(), MetadataCollectionStatusEnum.FAILURE.getCode(), 0, 0, 0);
            clearGlobalParam();
            metadataTaskStepService.update(step2Id,MetadataCollectionStatusEnum.FAILURE.getCode(),e.getMessage(),new Date());
            return metadataTaskRecordId;
        }
        if (CollUtil.isEmpty(allDbs)) {
            updateMetadataTaskRecord(metadataTaskRecordId, "没有匹配到元数据", MetadataCollectionStatusEnum.SUCCESS.getCode(), 0, 0, 0);
            return metadataTaskRecordId;
        }

        GatherMetadataDTO gatherMetadataDTO = new GatherMetadataDTO();
        //获取采集
        /**
         * 步骤三名称：资源采集
         */
        String step3Id = IdUtils.simpleUUID();
        try {
            MetadataTaskStep save = metadataTaskStepService.save(step3Id,3, Constants.STEP_NAME_COLLECT, Constants.STEP_CONTENT_COLLECT, metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), null, new Date(), null);

            for (String allDb : allDbs) {
                log.info("采集开始-----将要采集的路径写入采集记录[{}]", allDb);
                //判断结束标识，是否结束采集
                if (!taskDTO.isRunning()) {
                    log.info("数据源[{}],采集资源中断停止", datasourceDTO.getDataName());
                    //更新采集统计
                    MetadataTaskRecord metadataTaskRecordCount = new MetadataTaskRecord();
                    BeanUtil.copyProperties(gatherMetadataDTO, metadataTaskRecordCount);
                    metadataTaskRecordCount.setStatus(MetadataCollectionStatusEnum.INTERRUPTED.getCode());
                    metadataTaskRecordCount.setId(metadataTaskRecordId);
                    metadataTaskRecordCount.setErrorLog("采集中断停止");
                    metadataTaskRecordService.update(metadataTaskRecordId, metadataTaskRecordCount);
                    metadataTaskStepService.update(step3Id,MetadataCollectionStatusEnum.INTERRUPTED.getCode(),"采集中断停止",new Date());
                    return metadataTaskRecordId;
                }
                //递归采集
                collectFtpMetadataByLevel(datasourceDTO, allDb, 1, task, taskDTO, gatherMetadataDTO);
            }


        } catch (AppErrorException e) {
            log.error("获取文件元数据失败{}", e.getMessage());
            updateMetadataTaskRecord(metadataTaskRecordId, e.getResult().toString().toString(), MetadataCollectionStatusEnum.FAILURE.getCode(), 0, 0, 0);
            clearGlobalParam();
            //检查是否发送告警
            boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
            if (b) {
                //构建发送消息实体
                sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
            }
            metadataTaskStepService.update(step3Id,MetadataCollectionStatusEnum.FAILURE.getCode(),e.getResult().toString(),new Date());
            return metadataTaskRecordId;
        }

        /**
         * 步骤四名称： 结果处理
         */
        //结果处理
        String step4Id = IdUtils.simpleUUID();
        try{
            metadataTaskStepService.save(step4Id,4, Constants.STEP_NAME_RESULT, Constants.STEP_CONTENT_RESULT, metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), null, new Date(), null);
            MetadataTaskRecord metadataTaskRecordCount = new MetadataTaskRecord();
            //获取平台采集数
            int platformCollectNum = getTablesToCollect(task, 1, "",false).size();
            metadataTaskRecordCount.setStatus(MetadataCollectionStatusEnum.SUCCESS.getCode());
            metadataTaskRecordCount.setId(metadataTaskRecordId);
            metadataTaskRecordCount.setQfRecordTotal(platformCollectNum);
            metadataTaskRecordCount.setRecordTotal(gatherMetadataDTO.getConfigTotal());
            metadataTaskRecordCount.setDeleteRecordTotal(gatherMetadataDTO.getDeleteTotal());
            metadataTaskRecordCount.setEndTime(new Date());
            metadataTaskRecordCount.setAddTableCount(gatherMetadataDTO.getAddTableCount());
            metadataTaskRecordCount.setAddColumnCount(gatherMetadataDTO.getAddColumnCount());
            metadataTaskRecordCount.setUpdateTableCount(gatherMetadataDTO.getUpdateTableCount());
            metadataTaskRecordCount.setUpdateColumnCount(gatherMetadataDTO.getUpdateColumnCount());
            metadataTaskRecordCount.setDeleteTableCount(gatherMetadataDTO.getDeleteTableCount());
            metadataTaskRecordCount.setDeleteColumnCount(gatherMetadataDTO.getDeleteColumnCount());
            //获取采集记录表里面是否有失败得表
            Integer total = metadataTableCommonDetailService.getService(MetadataTableCommonDetail.class).getQuery().eq("metadataTaskRecordId", metadataTaskRecordId)
                    .eq("status", MetadataCollectionStatusEnum.FAILURE.getCode()).total();
            if (total > 0) {
                metadataTaskRecordCount.setContentStatus(MetadataCollectionStatusEnum.FAILURE.getCode());
            }else{
                metadataTaskRecordCount.setContentStatus(MetadataCollectionStatusEnum.SUCCESS.getCode());
            }
            metadataTaskRecordService.update(metadataTaskRecordId, metadataTaskRecordCount);
        }catch (AppErrorException e) {
            log.error("获取文件元数据结果统计失败{}", e.getMessage());
            updateMetadataTaskRecord(metadataTaskRecordId, e.getResult().toString().toString(), MetadataCollectionStatusEnum.FAILURE.getCode(), 0, 0, 0);
            clearGlobalParam();
            //检查是否发送告警
            boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
            if (b) {
                //构建发送消息实体
                sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
            }
            metadataTaskStepService.update(step4Id,MetadataCollectionStatusEnum.FAILURE.getCode(),e.getResult().toString(),new Date());
            return metadataTaskRecordId;
        }
        return metadataTaskRecord;

    }

    //根据表id和采集记录id修改表详情状态为失败
    public void updateMetadataTaskRecordTableDetails(String tableId, String metadataTaskRecordId, Integer status, String log, String opt, Integer colCount) {
        MetadataTableCommonDetail recordTableDetails = new MetadataTableCommonDetail();
        recordTableDetails.setMetadataTableCommonId(tableId);
        recordTableDetails.setStatus(status);
        recordTableDetails.setMetadataTaskRecordId(metadataTaskRecordId);
        recordTableDetails.setErrorLog(log);
        recordTableDetails.setOpt(opt);
        //colCount 不为null时，更新colCount
        if (Objects.nonNull(colCount)) {
            recordTableDetails.setMetadataColumnTotal(colCount);
        }
        EqCondition update = new EqCondition("metadataTaskRecordId", metadataTaskRecordId);
        EqCondition metadataTableId = new EqCondition("metadataTableCommonId", tableId);
        List<WhereCondition> conditions = new ArrayList<>();
        conditions.add(update);
        conditions.add(metadataTableId);
        metadataTableCommonDetailService.updateBy(conditions, recordTableDetails, true);
    }

    /**
     * 比较表和以采集过的此表是否要更新:权限，大小，修改时间
     */
    public boolean updateTable(MetadataTableCommon metadataTableCommon, MetadataTableCommon collctedTable, StringBuilder changeInfo) {
        if (metadataTableCommon == null || collctedTable == null) {
            return false;
        }
        boolean isTableUpdate = false;
        //比较文件权限
        if (notEqualsTwoString(metadataTableCommon.getAccessPermission(), collctedTable.getAccessPermission())) {
            isTableUpdate = true;
            changeInfo.append(collctedTable.getName()).append("资源权限由 ").append(metadataTableCommon.getAccessPermission()).append("改为").append(metadataTableCommon.getAccessPermission()).append(",");

        }
        //大小
        if (notEqualsTwoString(metadataTableCommon.getFileSize(), collctedTable.getFileSize())) {
            isTableUpdate = true;
            changeInfo.append(collctedTable.getName()).append("资源大小由 ").append(metadataTableCommon.getFileSize()).append("改为").append(metadataTableCommon.getFileSize()).append(",");

        }
        //修改时间
        if (notEqualsTwoString(metadataTableCommon.getFileModifiedTime(), collctedTable.getFileModifiedTime())) {
            isTableUpdate = true;
            changeInfo.append(collctedTable.getName()).append("资源修改时间由 ").append(metadataTableCommon.getFileModifiedTime()).append("改为").append(metadataTableCommon.getFileModifiedTime()).append(",");
        }
        return isTableUpdate;

    }

    /**
     * 比较两个字符串是否相等，会将null和空字符串认为是同一种值
     *
     * @return 不相等返回true，相等返回false
     */
    public boolean notEqualsTwoString(String str1, String str2) {
        str1 = str1 == null ? "" : str1;
        str2 = str2 == null ? "" : str2;
        return !str1.equals(str2);
    }

    /**
     * 计算全量时需要更新的表
     *
     * @param
     * @return
     * @throws AppErrorException
     */
    public List<MetadataTableCommon> getTablesToUpdate(@NotNull List<MetadataTableCommon> matchedTables, List<MetadataTableCommon> collectedTables) throws AppErrorException {
        Set<MetadataTableCommon> collectedSet = new HashSet<>(collectedTables);
        return matchedTables.stream()
                .filter(collectedSet::contains)  // 过滤出采集的表
                .collect(Collectors.toList());
    }

    /**
     * 计算全量时增量采集的表
     *
     * @param
     * @return
     * @throws AppErrorException
     */
    public List<MetadataTableCommon> getTablesToAdd(@NotNull List<MetadataTableCommon> matchedTables, List<MetadataTableCommon> collectedTables) throws AppErrorException {
        Set<MetadataTableCommon> collectedSet = new HashSet<>(collectedTables);
        return matchedTables.stream()
                .filter(table -> !collectedSet.contains(table))  // 过滤出未采集的表
                .collect(Collectors.toList());
    }

    //4.计算应该删除的表 及采集过得表在源库中已不存在的集合
    public List<MetadataTableCommon> getTablesToDelete(List<MetadataTableCommon> matchedTables, List<MetadataTableCommon> collectedTables) throws AppErrorException {

        List<MetadataTableCommon> newList = new ArrayList<>();
        if (CollUtil.isEmpty(collectedTables)) {
            return newList;
        }
        if (CollUtil.isEmpty(matchedTables)) {
            return newList;
        }
        //获取matchedTables集合在collectedTables中不存在得表，生成新集合
        for (MetadataTableCommon table : collectedTables) {
            if (!matchedTables.contains(table)) {
                table.setDelFlag(true);
                newList.add(table);
            }
        }

        return newList;
    }

    /**
     * 获取已经采集过的表
     *
     * @param task
     * @return
     * @throws AppErrorException
     */
    public List<MetadataTableCommon> getTablesToCollect(MetadataTask task, Integer code, String path,  boolean isDelFlag) throws AppErrorException {
        // 模拟从采集记录表中获取已采集过的表
        //根据数据源id  查询
        String datasourceInfoId = task.getDatasourceInfoId();
        List<MetadataTableCommon> metadataTables = new ArrayList<>();
        IQueryWrapper<MetadataTableCommon> query = metadataTableCommonService.getService(MetadataTableCommon.class).getQuery().eq("datasourceInfoId", datasourceInfoId).eq("delFlag", isDelFlag);
        if (StringUtils.isNotEmpty(path)) {
            query.eq("dbName", path);
        }
        metadataTables = query.list();
        if (metadataTables == null) {
            return new ArrayList<>();
        }
        //采集前  还是采集后
        if (MetadataCollectionStatusEnum.NOT_STARTED.getCode() == code) {
            tablesToCollect = metadataTables;
        }
        return metadataTables;
    }

    /**
     * 构建采集记录表详情列表
     *
     * @param detailsList
     * @param batchSize
     * @throws AppErrorException
     */
    public void addInBatchesMetadataTableCommonDetailS(List<MetadataTableCommonDetail> detailsList, int batchSize) throws AppErrorException {
        int size = detailsList.size();
        for (int i = 0; i < size; i += batchSize) {
            int end = Math.min(i + batchSize, size);
            List<MetadataTableCommonDetail> batch = detailsList.subList(i, end);
            metadataTableCommonDetailService.add(batch);
        }
    }

    /**
     * 获取所有表
     *
     * @param task
     * @return
     * @throws AppErrorException
     */
    public List<MetadataTableCommon> getDbTableToMetadataTables(MetadataTask task, DatasourceDTO datasourceDTO) throws AppErrorException {
        List<MetadataTableCommon> metadataTables = new ArrayList<>();
        //根据数据源id获取数据源参数对象、TODO
        if (Objects.isNull(datasourceDTO)) {
            //todo  数据源对象
            datasourceDTO = getDatasourceDTO(task.getDatasourceInfoId());
        }
        List<DbTableVO> dbTableVOS = DatasourceUtils.getMedataDataTables(datasourceDTO);
        if (CollUtil.isNotEmpty(dbTableVOS)) {
            for (DbTableVO dbTableVO : dbTableVOS) {
                MetadataTableCommon metadataTable = new MetadataTableCommon();
                metadataTable.setId(MD5Util.resourceToMD5(task.getDatasourceInfoId(), dbTableVO.getFileType(), "", dbTableVO.getName(), dbTableVO.getType(), ""));
                metadataTable.setName(dbTableVO.getName());
                metadataTable.setDatasourceInfoId(task.getDatasourceInfoId());
                metadataTable.setDatasourceType(datasourceDTO.getDataType());
                metadataTable.setType(dbTableVO.getType());
                metadataTable.setManager(datasourceDTO.getManager());
                metadataTable.setMetadataColumnTotal(dbTableVO.getColumnCount());
                //todo 文件类型
                metadataTable.setFilePath(dbTableVO.getFilePath());
                metadataTable.setFileSize(dbTableVO.getFileSize());
                metadataTable.setFileType(dbTableVO.getFileType());
                metadataTable.setFileModifiedTime(dbTableVO.getFileModifiedTime());
                metadataTable.setFileParentPath(dbTableVO.getFileParentPath());
                metadataTable.setAccessPermission(dbTableVO.getAccessPermission());
                metadataTable.setDbName(datasourceDTO.getPath());
                metadataTable.setFileOwner(dbTableVO.getFileOwner());
                metadataTable.setFileGroup(dbTableVO.getFileGroup());
                metadataTable.setFileLevel(dbTableVO.getFileLevel());
                metadataTables.add(metadataTable);
            }
        }
        return metadataTables;
    }


    /**
     * 构建元数据采集记录表
     *
     * @param tables
     * @return
     */
    public List<MetadataTableCommonDetail> getMetadataTableCommonDetail(List<MetadataTableCommon> tables, String opt,int status) {
        List<MetadataTableCommonDetail> recordTableDetailsList = new ArrayList<>();
        for (MetadataTableCommon table : tables) {
            MetadataTableCommonDetail recordTableDetails = new MetadataTableCommonDetail();
            recordTableDetails.setMetadataTaskRecordId(metadataTaskRecordId);
            recordTableDetails.setDatasourceInfoId(table.getDatasourceInfoId());
            recordTableDetails.setType(table.getType());
            if (OperationType.DELETE.name().equals(opt)) {
                recordTableDetails.setStatus(MetadataCollectionStatusEnum.SUCCESS.getCode());
            } else {
                recordTableDetails.setStatus(status);
            }
            recordTableDetails.setName(table.getName());
            recordTableDetails.setDatasourceType(table.getDatasourceType());
            recordTableDetails.setMetadataTableCommonId(table.getId());
            recordTableDetails.setIndexTableId(table.getIndexTableId());
            recordTableDetails.setDatasourceType(table.getDatasourceType());
            //详情id metadataTaskRecordId_tableId
            String uuid = MD5Util.tableIdToMD5(metadataTaskRecordId, table.getId(), "");
            recordTableDetails.setId(uuid);
            recordTableDetails.setMetadataColumnTotal(table.getMetadataColumnTotal());
            recordTableDetails.setOpt(opt);
            recordTableDetails.setManager(table.getManager());
            //todo 文件类型
            recordTableDetails.setFilePath(table.getFilePath());
            recordTableDetails.setFileSize(table.getFileSize());
            recordTableDetails.setFileType(table.getFileType());
            recordTableDetails.setFileModifiedTime(table.getFileModifiedTime());
            recordTableDetails.setFileParentPath(table.getFileParentPath());
            recordTableDetails.setAccessPermission(table.getAccessPermission());
            recordTableDetails.setDbName(table.getDbName());
            recordTableDetails.setFileGroup(table.getFileGroup());
            recordTableDetails.setFileOwner(table.getFileOwner());
            recordTableDetails.setFileLevel(table.getFileLevel());
            recordTableDetailsList.add(recordTableDetails);
        }
        return recordTableDetailsList;
    }

    //清空全局参数
    public void clearGlobalParam() {
        tablesToCollect = new ArrayList<>();
        isTablesToDelete = new ArrayList<>();
        datasourceMatchedTables = new ArrayList<>();
        qfMetadataColumnCommonsMap = new HashMap<>();
    }

    /**
     * 此数据源有无配置任务失败或者元数据变更告警事件
     */
    public boolean checkAlarm(String datasourceId, AlertStatusEnum alertStatusEnum) {
        DatasourceAlert datasourceAlert = datasourceAlertService.getQuery()
                .lazys("datasourceAlertEvents")
                .eq("datasourceInfoId", datasourceId)
                .one();

        return datasourceAlert != null && datasourceAlert.getDatasourceAlertEvents().stream()
                .anyMatch(event -> alertStatusEnum.name().equals(event.getEventCode()));
    }

    /**
     * 发送告警
     */
    public void sendAlarm(DatasourceDTO datasourceDTO, AlertStatusEnum alertStatusEnum, MetadataTask taskDTO, MetadataTableCommon table, String changeInfo) {
        //构建发送消息实体
        AlarmMsg alarmMsg = sendAlertMsgService.convertAlarmMsg(datasourceDTO.getDatasourceId(), datasourceDTO.getDataName(), taskDTO.getTenantCode(), "", alertStatusEnum.name(), null);
        alarmMsg.setBusinessName(datasourceDTO.getDataName());
        alarmMsg.setBusinessId(datasourceDTO.getDataName());
        JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
        if (dataJsonMap != null && dataJsonMap.containsKey("jdbcUrl")) {
            alarmMsg.addAttr("jdbcUrl", dataJsonMap.getString("jdbcUrl"));
        }
        if (Objects.nonNull(table)) {
            alarmMsg.addAttr("fileName", table.getName());
            alarmMsg.addAttr("fileType", table.getType());

        } else {
            alarmMsg.setBusinessId(datasourceDTO.getDatasourceId());
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(changeInfo)) {
            alarmMsg.addAttr("changeInfo", changeInfo);
        }
        alarmMsg.addAttr("dataName", datasourceDTO.getDataName());
        alarmMsg.addAttr("datasourceId", datasourceDTO.getDatasourceId());
        log.info("数据源[{}],发送告警消息:{}", datasourceDTO.getDataName(), alarmMsg);
        sendAlertMsgService.sendMsg(alarmMsg, AlertStatusEnum.getDes(alertStatusEnum.name()));
    }

    /**
     * 根据id更新采集记录对象
     */
    public void updateMetadataTaskRecord(String metadataTaskRecordId,
                                         String log, Integer status,
                                         Integer total,
                                         Integer qfTotal,
                                         Integer delTotal
    ) {
        MetadataTaskRecord metadataTaskRecord = new MetadataTaskRecord();
        metadataTaskRecord.setId(metadataTaskRecordId);
        if (Objects.nonNull(status)) {
            metadataTaskRecord.setStatus(status);
            if (status == MetadataCollectionStatusEnum.SUCCESS.getCode() || status == MetadataCollectionStatusEnum.FAILURE.getCode()) {
                metadataTaskRecord.setEndTime(new Date());
            }
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(log)) {
            metadataTaskRecord.setErrorLog(log);
        }
        if (Objects.nonNull(total)) {
            metadataTaskRecord.setRecordTotal(total);
        }
        if (Objects.nonNull(qfTotal)) {
            metadataTaskRecord.setQfRecordTotal(qfTotal);
        }
        if (Objects.nonNull(delTotal)) {
            metadataTaskRecord.setDeleteRecordTotal(delTotal);
        }
        metadataTaskRecord.setLastModificationTime(new Date());
        metadataTaskRecordService.update(metadataTaskRecordId, metadataTaskRecord);
    }

    @Override
    public List<MetadataTaskRecordResultVO> getResultByTaskRecordId(String id, String opt) {
        List<MetadataTaskRecordResultVO> metadataTaskRecordResultList = new ArrayList<>();
        //统计结果
        //目录个数
        Integer total = metadataTaskRecordService.getService(MetadataTableCommonDetail.class)
                .getQuery()
                .eq("metadataTaskRecordId", id)
                .eq("opt", opt)
                .eq("type", MetadataTaskObjectEnum.FOLDER.getType())
                .total();

        //文件个数
        Integer fileTotal = metadataTaskRecordService.getService(MetadataTableCommonDetail.class)
                .getQuery()
                .eq("metadataTaskRecordId", id)
                .eq("opt", opt)
                .eq("type", MetadataTaskObjectEnum.FILE.getType())
                .total();
        MetadataTaskRecordResultVO metadataTaskRecordResult = MetadataTaskRecordResultVO.builder()
                .folderCount(total)
                .fileCount(fileTotal)
                .tableCount(0)
                .viewCount(0)
                .procedureCount(0)
                .indexCount(0)
                .functionCount(0)
                .columnCount(0)
                .build();
        metadataTaskRecordResultList.add(metadataTaskRecordResult);
        return metadataTaskRecordResultList;
    }
    /**
     * 是否采集
     * @param datasourceinfoId
     * @return
     */
    @Override
    public Boolean isCollection(String datasourceinfoId){
        boolean  isCollection = false;
        Integer datasourceInfoId = metadataTableCommonService.getQuery().eq("datasourceInfoId", datasourceinfoId).total();
        if(datasourceInfoId > 0){
            isCollection= true;
        }
        return isCollection;
    }

}
