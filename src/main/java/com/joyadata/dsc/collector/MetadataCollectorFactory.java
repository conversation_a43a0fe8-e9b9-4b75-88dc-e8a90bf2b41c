package com.joyadata.dsc.collector;

import cn.hutool.extra.spring.SpringUtil;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.joyadata.dsc.collector.collectorTask.*;
import com.joyadata.dsc.collector.fileTask.FtpMetadataCollector;

import java.util.HashMap;
import java.util.Map;

/**
 * 元数据采集器工厂类
 * 负责根据数据源类型创建相应的元数据采集器实例
 * <AUTHOR>
 */
public class MetadataCollectorFactory {

    /**
     * 使用数据源类型作为键，存储对应元数据采集器的映射
     */
    private static final Map<String, MetadataCollector> COLLECTOR_MAP = new HashMap<>();

    /**
     * 静态初始化块
     * 初始化元数据采集器映射，为支持的数据源类型创建对应的采集器实例
     */
    static {
        //jdbc类
        COLLECTOR_MAP.put(DataSourceTypeEnum.MySQL.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.DORIS1.getDataType(), SpringUtil.getBean(DorisMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.Oracle.getDataType(), SpringUtil.getBean(OracleMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.PostgreSQL.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.SQLServer.getDataType(), SpringUtil.getBean(SqlServerMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.TiDB.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.TDSQL_FOR_MySQL.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.GoldenDB.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.ADS.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.StarRocks.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.DWS_MySQL.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.GaussDB_FOR_MySQL.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.Sequoiadb_FOR_MYSQL.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.MariaDB.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));

        COLLECTOR_MAP.put(DataSourceTypeEnum.GaussDB.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.DWS_PG.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.GREENPLUM_PostgreSQL.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.GREENPLUM6.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.TBase.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.TDSQL_FOR_PG.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.KINGBASE8.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.DMDB.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.ClickHouse.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.ADB_PostgreSQL.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.DB2.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.OceanBase_FOR_MySQL.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.OceanBase_FOR_ORACLE.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.SAP_HANA.getDataType(), SpringUtil.getBean(MySQLMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.TDSQL_FOR_ORACLE.getDataType(), SpringUtil.getBean(PgMetadataCollector.class));

        //文件类
        COLLECTOR_MAP.put(DataSourceTypeEnum.FTP.getDataType(), SpringUtil.getBean(FtpMetadataCollector.class));
        COLLECTOR_MAP.put(DataSourceTypeEnum.SFTP.getDataType(), SpringUtil.getBean(FtpMetadataCollector.class));
        // 初始化其他采集器
    }
    /**
     * 根据数据源类型获取对应的元数据采集器 （元数据采集）
     *
     * @param type 数据源类型枚举
     * @return 对应的元数据采集器实例如果指定类型未注册，则返回null
     */
    public static MetadataCollector getCollector(DataSourceTypeEnum type) {
        return COLLECTOR_MAP.get(type.getDataType());
    }

}
