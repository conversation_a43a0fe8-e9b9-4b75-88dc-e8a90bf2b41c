package com.joyadata.dsc.collector.util;

import com.joyadata.dsc.collector.MetadataCollector;
import com.joyadata.dsc.enums.MetadataCollectionStatusEnum;
import com.joyadata.dsc.model.metadata.MetadataTask;
import com.joyadata.dsc.model.metadata.MetadataTaskRecord;
import com.joyadata.dsc.model.metadata.dto.MetadataTaskDTO;
import com.joyadata.dsc.service.MetadataTaskRecordService;
import com.joyadata.exception.AppErrorException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

import java.util.concurrent.CompletableFuture;

@Slf4j
public class MetadataCollectionManger {

    public String datasourceInfoId;
    public MetadataTask task;
    public String metadataTaskRecordId;
    public MetadataCollector collector;
    public MetadataTaskRecordService metadataTaskRecordService;
    public  MetadataTaskDTO taskDTO;

    public MetadataCollectionManger(MetadataCollector collector,
                                    String datasourceInfoId,
                                    MetadataTask task,
                                    String metadataTaskRecord,
                                    MetadataTaskRecordService metadataTaskRecordService,
                                    MetadataTaskDTO taskDTO) {
        this.datasourceInfoId = datasourceInfoId;
        this.task = task;
        this.collector = collector;
        this.metadataTaskRecordId = metadataTaskRecord;
        this.metadataTaskRecordService = metadataTaskRecordService;
        this.taskDTO = taskDTO;
    }


    @Async
    public void runTask() {
        CompletableFuture.runAsync(() -> {
            taskDTO.setRunning(true);
            try {
                collector.collectMetadata(datasourceInfoId, task, taskDTO.getMetadataTaskRecordId(), taskDTO);
                log.info("Task 采集记录 " + taskDTO.getMetadataTaskRecordId() + " completed successfully");
            } catch (AppErrorException e) {
                log.error("Task 采集记录 " + taskDTO.getMetadataTaskRecordId() + " was interrupted");
                //将任务采集记录更新为失败
                MetadataTaskRecord metadataTaskRecord=new MetadataTaskRecord();
                metadataTaskRecord.setId(taskDTO.getMetadataTaskRecordId());
                metadataTaskRecord.setStatus(MetadataCollectionStatusEnum.FAILURE.getCode());
                metadataTaskRecord.setErrorLog(e.getResult().toString());
                metadataTaskRecordService.update(taskDTO.getMetadataTaskRecordId(),metadataTaskRecord);
            } finally {
                taskDTO.setRunning(false);
            }
        });
    }
}
