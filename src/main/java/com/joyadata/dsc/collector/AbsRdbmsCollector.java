package com.joyadata.dsc.collector;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.joyadata.dsc.constants.Constants;
import com.joyadata.dsc.enums.*;
import com.joyadata.dsc.model.datasoure.DatasourceAlert;
import com.joyadata.dsc.model.datasoure.dto.AlarmMsg;
import com.joyadata.dsc.model.datasoure.vo.DatasourceAuthVO;
import com.joyadata.dsc.model.datasoure.vo.DatasourceConnectProgressVO;
import com.joyadata.dsc.model.metadata.*;
import com.joyadata.dsc.model.metadata.dto.GatherMetadataDTO;
import com.joyadata.dsc.model.metadata.dto.MetadataDbDTO;
import com.joyadata.dsc.model.metadata.dto.MetadataTaskDTO;
import com.joyadata.dsc.model.metadata.vo.MetadataNodeVO;
import com.joyadata.dsc.model.metadata.vo.MetadataTaskRecordResultVO;
import com.joyadata.dsc.service.*;
import com.joyadata.dsc.utils.DatasourceUtil;
import com.joyadata.dsc.utils.IdUtils;
import com.joyadata.dsc.utils.MD5Util;
import com.joyadata.exception.AppErrorException;
import com.joyadata.model.sql.EqCondition;
import com.joyadata.model.sql.WhereCondition;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/*
 * 关系型数据库采集
 * <AUTHOR>
 */
@Slf4j
@Component
public abstract class AbsRdbmsCollector implements MetadataCollector {


    public String metadataTaskRecordId;

    public MetadataTaskDTO taskDTO;
    /**
     * 平台已采集的表集合
     */
    List<MetadataTableCommon> tablesToCollect = new ArrayList<>();

    /**
     * 快速选表不存在的表
     */
//    List<String> isTablesExistList = new ArrayList<>();
    /**
     * 此次采集需要判断源库是否删除得表
     */
    List<MetadataTableCommon> isTablesToDelete = new ArrayList<>();


    /**
     * 源库取的所有表 、视图、函数、存储过程的集合
     */
    List<MetadataTableCommon> datasourceMatchedTables = new ArrayList<>();

    /**
     * 源库已获取的表字段的集合
     * key 为表id
     */
    Map<String, List<MetadataColumnCommon>> metadataColumnCommonsMap = new HashMap<>();
    /**
     * 平台已获取的表字段的map
     * key 为表id
     */
    Map<String, List<MetadataColumnCommon>> qfMetadataColumnCommonsMap = new HashMap<>();


    @Autowired
    public MetadataTaskRecordService metadataTaskRecordService;


    @Autowired
    public MetadataTableCommonService metadataTableCommonService;

    @Autowired
    public MetadataTableCommonDetailService metadataTableCommonDetailService;
    @Autowired
    public MetadataColumnCommonService metadataColumnCommonService;
    @Autowired
    public MetadataColumnCommonDetailService metadataColumnCommonDetailService;
    @Autowired
    DatasourceUtil datasourceUtil;

    @Autowired
    public DatasourceAlertService datasourceAlertService;
    @Autowired
    public SendAlertMsgService sendAlertMsgService;

    @Autowired
    public MetadataTaskStepService metadataTaskStepService;

    @Autowired
    public DatasourceInfoService datasourceInfoService;

    /**
     * 获取DatasourceDTO datasourceDTO 对象
     */
    public DatasourceDTO getDatasourceDTO(String datasourceId) {
        DatasourceDTO datasourceDTO = datasourceUtil.getDatasourceDTO(datasourceId);
        return datasourceDTO;
    }


    /**
     * 1.获取数据源配置的所有库，构建节点---获取所有库
     */
    @Override
    public List<MetadataNodeVO> getAllDbsNode(String datasourceId, DatasourceDTO datasourceDTO) {
        //todo  根据数据源id获取数据源配置
        if (Objects.isNull(datasourceDTO)) {
            datasourceDTO = getDatasourceDTO(datasourceId);
        }

        //判断数据源对象是否为null
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", datasourceId);
        }
        List<MetadataNodeVO> metadataNodeVOS = new ArrayList<>();
        try {
            //先判断此数据源有无db
            String dbName = datasourceDTO.getDbName();
            if (StringUtils.isNotEmpty(dbName)) {
                MetadataNodeVO metadataCalalogVO = new MetadataNodeVO();
                metadataCalalogVO.setDatasourceInfoId(datasourceId);
                metadataCalalogVO.setName(dbName);
                metadataCalalogVO.setDbName(dbName);
                metadataCalalogVO.setCnName(MetadataTaskObjectEnum.DB.getChineseName());
                String uuid = MD5Util.resourceToMD5(datasourceId, dbName, "", dbName, MetadataTaskObjectEnum.DB.getType(), null);
                metadataCalalogVO.setUuid(uuid);
                metadataCalalogVO.setType(MetadataTaskObjectEnum.DB.getType());
                metadataCalalogVO.setLevel("1");
                //子目录节点
                if (DatasourceTypeDatabase.schemaDataTypeList().contains(datasourceDTO.getDataType().toLowerCase())) {
                    metadataCalalogVO.setHasSchema(true);
                    //获取模式
                    List<MetadataNodeVO> schemaNode = getAllSchemaNode(datasourceId, datasourceDTO);
                    metadataCalalogVO.setChildrens(schemaNode);
                } else {
                    List<MetadataNodeVO> commonNode = getCommonNode(metadataCalalogVO.getUuid(), "2", dbName, "");
                    metadataCalalogVO.setChildrens(commonNode);
                }
                metadataNodeVOS.add(metadataCalalogVO);
                return metadataNodeVOS;
            }

            // jdbc 类型 获取所有库: 适配没有schema的数据源
            List<String> allDatabases = DatasourceUtils.getAllDbs(datasourceDTO);
            for (String allDatabase : allDatabases) {
                MetadataNodeVO metadataCalalogVO = new MetadataNodeVO();
                metadataCalalogVO.setDatasourceInfoId(datasourceId);
                metadataCalalogVO.setName(allDatabase);
                metadataCalalogVO.setDbName(allDatabase);
                metadataCalalogVO.setCnName(MetadataTaskObjectEnum.DB.getChineseName());
                metadataCalalogVO.setType(MetadataTaskObjectEnum.DB.getType());
                String uuid = MD5Util.resourceToMD5(datasourceId, allDatabase, metadataCalalogVO.getSchemaName(), allDatabase, MetadataTaskObjectEnum.DB.getType(), null);
                metadataCalalogVO.setUuid(uuid);
                metadataCalalogVO.setLevel("1");
                //子目录节点
                if (DatasourceTypeDatabase.schemaDataTypeList().contains(datasourceDTO.getDataType().toLowerCase())) {
                    metadataCalalogVO.setHasSchema(true);
                    //获取模式
                    datasourceDTO.setDbName(allDatabase);
                    List<MetadataNodeVO> schemaNode = getAllSchemaNode(datasourceId, datasourceDTO);
                    metadataCalalogVO.setChildrens(schemaNode);
                } else {
                    List<MetadataNodeVO> commonNode = getCommonNode(metadataCalalogVO.getUuid(), "2", dbName, "");
                    metadataCalalogVO.setChildrens(commonNode);
                }
                metadataNodeVOS.add(metadataCalalogVO);
            }
        } catch (AppErrorException e) {
            // 处理异常
            log.error("Error retrieving databases: " + e.getResult().toString());
            // 可以记录日志或者抛出自定义异常
            throw new AppErrorException("Failed to retrieve databases", e);
        }
        return metadataNodeVOS;

    }

    /**
     * 1.获取数据源配置的所有库，构建节点---获取所有模式
     */

    @Override
    public List<MetadataNodeVO> getAllSchemaNode(String datasourceInfoId, DatasourceDTO datasourceDTO) {
        if (Objects.isNull(datasourceDTO)) {
            datasourceDTO = getDatasourceDTO(datasourceInfoId);

        }
        List<MetadataNodeVO> metadataNodeVOS = new ArrayList<>();
        try {
            //先判断此数据源有无schema
            String dbName = datasourceDTO.getDbName();
            String schema = datasourceDTO.getSchema();
            if (StringUtils.isNotEmpty(schema)) {
                MetadataNodeVO metadataCalalogVO = new MetadataNodeVO();
                metadataCalalogVO.setDatasourceInfoId(datasourceInfoId);
                metadataCalalogVO.setName(schema);
                metadataCalalogVO.setDbName(dbName);
                metadataCalalogVO.setSchemaName(schema);
                metadataCalalogVO.setCnName(MetadataTaskObjectEnum.SCHEMA.getChineseName());
                String uuid = MD5Util.resourceToMD5(datasourceInfoId, dbName, schema, schema, MetadataTaskObjectEnum.SCHEMA.getType(), null);
                metadataCalalogVO.setUuid(uuid);
                metadataCalalogVO.setType(MetadataTaskObjectEnum.SCHEMA.getType());
                //设置子节点
                metadataCalalogVO.setChildrens(getCommonNode(uuid, "3", dbName, schema));
                metadataCalalogVO.setLevel("2");
                metadataCalalogVO.setHasSchema(false);
                metadataNodeVOS.add(metadataCalalogVO);
                return metadataNodeVOS;
            }
            //todo url  pg
            if (DatasourceTypeDatabase.pgDataTypeList().contains(datasourceDTO.getDataType().toLowerCase())) {
                //处理url
                datasourceDTO = DatasourceUtil.replaceDatasourceDTODbName(datasourceDTO, dbName);
            }
            // jdbc 类型 获取所有库: schema的数据源
            List<String> allDatabases = DatasourceUtils.getAllSchemas(datasourceDTO);
            for (String allDatabase : allDatabases) {
                MetadataNodeVO metadataCalalogVO = new MetadataNodeVO();
                metadataCalalogVO.setDatasourceInfoId(datasourceInfoId);
                metadataCalalogVO.setName(allDatabase);
                metadataCalalogVO.setDbName(dbName);
                metadataCalalogVO.setSchemaName(allDatabase);
                metadataCalalogVO.setCnName(MetadataTaskObjectEnum.SCHEMA.getChineseName());
                metadataCalalogVO.setType(MetadataTaskObjectEnum.SCHEMA.getType());
                String uuid = MD5Util.resourceToMD5(datasourceInfoId, dbName, allDatabase, allDatabase, MetadataTaskObjectEnum.SCHEMA.getType(), null);
                metadataCalalogVO.setUuid(uuid);
                //设置子节点
                metadataCalalogVO.setChildrens(getCommonNode(uuid, "3", dbName, allDatabase));
                metadataCalalogVO.setLevel("2");
                metadataCalalogVO.setHasSchema(false);
                metadataNodeVOS.add(metadataCalalogVO);
            }
        } catch (AppErrorException e) {
            // 处理异常
            log.error("Error retrieving schemas: " + e.getResult().toString());
            // 可以记录日志或者抛出自定义异常
            throw new AppErrorException("Failed to retrieve schemas", e);
        }
        return metadataNodeVOS;

    }


    /**
     * 2.获取表 、视图、索引、函数、存储过程等常量节点
     */
    @Override
    public List<MetadataNodeVO> getCommonNode(String parentId, String level, String dbName, String schemaName) {
        List<MetadataNodeVO> metadataNodeVoS = new ArrayList<>();
        metadataNodeVoS.add(MetadataTaskObjectEnum.getNode(MetadataTaskObjectEnum.TABLE.getChineseName(), MetadataTaskObjectEnum.TABLE.getType(), null, level, dbName, schemaName));
        metadataNodeVoS.add(MetadataTaskObjectEnum.getNode(MetadataTaskObjectEnum.VIEW.getChineseName(), MetadataTaskObjectEnum.VIEW.getType(), null, level, dbName, schemaName));
        metadataNodeVoS.add(MetadataTaskObjectEnum.getNode(MetadataTaskObjectEnum.INDEX.getChineseName(), MetadataTaskObjectEnum.INDEX.getType(), null, level, dbName, schemaName));
        metadataNodeVoS.add(MetadataTaskObjectEnum.getNode(MetadataTaskObjectEnum.FUNCTION.getChineseName(), MetadataTaskObjectEnum.FUNCTION.getType(), null, level, dbName, schemaName));
        metadataNodeVoS.add(MetadataTaskObjectEnum.getNode(MetadataTaskObjectEnum.PROCEDURE.getChineseName(), MetadataTaskObjectEnum.PROCEDURE.getType(), null, level, dbName, schemaName));
        return metadataNodeVoS;
    }

    /**
     * 3.获取表、视图---根据库名获取表、视图
     */
    @Override
    public List<MetadataNodeVO> getTableNodesByDb(String datasourceId, MetadataDbDTO dto, DatasourceDTO datasourceDTO) {
        //todo  根据数据源id获取数据源配置
        if (Objects.isNull(datasourceDTO)) {
            datasourceDTO = getDatasourceDTO(datasourceId);
        }
        //判断数据源对象是否为null
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", datasourceId);
        }
        datasourceDTO.setDbName(dto.getDbName());
        datasourceDTO.setType(dto.getType());
        datasourceDTO.setTableNamePattern(dto.getTableNamePattern());
        //schema 不为空，设置
        if (StringUtils.isNotEmpty(dto.getSchemaName())) {
            datasourceDTO.setSchema(dto.getSchemaName());
        }
        //todo url  pg
        if (DatasourceTypeDatabase.pgDataTypeList().contains(datasourceDTO.getDataType().toLowerCase())) {
            //处理url
            datasourceDTO = DatasourceUtil.replaceDatasourceDTODbName(datasourceDTO, datasourceDTO.getDbName());
        }
        String level = "3";
        if (StringUtils.isNotEmpty(dto.getSchemaName())) {
            level = "4";
        }
        List<MetadataNodeVO> tablelist = new ArrayList<>();
        try {
            // jdbc 类型 获取所有库: 适配没有schema的数据源
            List<DbTableVO> medataDataTables = DatasourceUtils.getMedataDataTables(datasourceDTO);
            for (DbTableVO dbTableVO : medataDataTables) {
                MetadataNodeVO table = new MetadataNodeVO();
                table.setDatasourceInfoId(datasourceId);
                table.setName(dbTableVO.getName());
                table.setType(dbTableVO.getType());
                table.setComment(dbTableVO.getComment());
                String schemaName = dbTableVO.getSchemaName();
                if (StringUtils.isEmpty(schemaName)) {
                    schemaName = "";
                }
                table.setSchemaName(schemaName);
                table.setDbName(dto.getDbName());
                table.setChildrens(new ArrayList<>());
                String uuid = MD5Util.resourceToMD5(datasourceId, dto.getDbName(), schemaName, dbTableVO.getName(), dto.getType(), dbTableVO.getTableName());
                table.setUuid(uuid);
                table.setLevel(level);
                table.setLeaf(true);
                // 假设 options 需要根据具体数据源动态设置
                tablelist.add(table);
            }

        } catch (AppErrorException e) {
            // 处理异常
            log.error("Error retrieving tablesbydb: " + e.getResult().toString());
            // 可以记录日志或者抛出自定义异常
            throw new AppErrorException("Failed to retrieve tablesbydb", e);
        }
        return tablelist;

    }


    /**
     * 4获取索引--根据库名或者表名获取索引
     */
    @Override
    public List<MetadataNodeVO> getIndexNodesByDb(String datasourceId, MetadataDbDTO dto, DatasourceDTO datasourceDTO) {
        //todo  根据数据源id获取数据源配置
        if (Objects.isNull(datasourceDTO)) {
            datasourceDTO = getDatasourceDTO(datasourceId);
        }
        //判断数据源对象是否为null
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", datasourceId);
        }
        datasourceDTO.setDbName(dto.getDbName());
        datasourceDTO.setTableName(dto.getName());
        datasourceDTO.setType(dto.getType());
        //schema 不为空，设置
        if (StringUtils.isNotEmpty(dto.getSchemaName())) {
            datasourceDTO.setSchema(dto.getSchemaName());
        }
        //todo url  pg
        if (DatasourceTypeDatabase.pgDataTypeList().contains(datasourceDTO.getDataType().toLowerCase())) {
            //处理url
            datasourceDTO = DatasourceUtil.replaceDatasourceDTODbName(datasourceDTO, datasourceDTO.getDbName());
        }
        String level = "3";
        if (StringUtils.isNotEmpty(dto.getSchemaName())) {
            level = "4";
        }

        List<MetadataNodeVO> tablelist = new ArrayList<>();
        try {
            // jdbc 类型 获取所有库: 适配没有schema的数据源
            List<DbTableVO> indexList = DatasourceUtils.getIndexList(datasourceDTO);
            for (DbTableVO dbTableVO : indexList) {
                MetadataNodeVO table = new MetadataNodeVO();
                table.setDatasourceInfoId(datasourceId);
                table.setName(dbTableVO.getName());
                table.setType(dto.getType());
                table.setComment(dbTableVO.getComment());
                String schemaName = dbTableVO.getSchemaName();
                if (StringUtils.isEmpty(schemaName)) {
                    schemaName = "";
                }
                table.setSchemaName(schemaName);
                table.setDbName(dto.getDbName());
                table.setTableName(dbTableVO.getTableName());
                table.setChildrens(new ArrayList<>());
                String uuid = MD5Util.resourceToMD5(datasourceId, dto.getDbName(), schemaName, dbTableVO.getName(), MetadataTaskObjectEnum.INDEX.getType(), dbTableVO.getTableName());
                table.setUuid(uuid);
                table.setLevel(level);
                table.setIndexType(dbTableVO.getIndexType());
                table.setUnique(dbTableVO.getUnique());
                table.setLeaf(true);
                // 假设 options 需要根据具体数据源动态设置
                tablelist.add(table);
            }

        } catch (AppErrorException e) {
            // 处理异常
            log.error("Error retrieving tablesbydb: " + e.getResult().toString());
            // 可以记录日志或者抛出自定义异常
            throw new AppErrorException("Failed to retrieve tablesbydb", e);
        }
        return tablelist;

    }

    /**
     * 5获取函数或者存储过程--根据库名函数或者存储过程
     */
    @Override
    public List<MetadataNodeVO> getFunctionNodesByDb(String datasourceId, MetadataDbDTO dto, DatasourceDTO datasourceDTO) {
        //todo  根据数据源id获取数据源配置
        if (Objects.isNull(datasourceDTO)) {
            datasourceDTO = getDatasourceDTO(datasourceId);
        }
        //判断数据源对象是否为null
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", datasourceId);
        }
        datasourceDTO.setDbName(dto.getDbName());
        datasourceDTO.setTableName(dto.getTableName());
        datasourceDTO.setType(dto.getType());
        //schema 不为空，设置
        if (StringUtils.isNotEmpty(dto.getSchemaName())) {
            datasourceDTO.setSchema(dto.getSchemaName());
        }
        //todo url  pg
        if (DatasourceTypeDatabase.pgDataTypeList().contains(datasourceDTO.getDataType().toLowerCase())) {
            //处理url
            datasourceDTO = DatasourceUtil.replaceDatasourceDTODbName(datasourceDTO, datasourceDTO.getDbName());
        }
        String level = "3";
        if (StringUtils.isNotEmpty(dto.getSchemaName())) {
            level = "4";
        }
        List<MetadataNodeVO> tablelist = new ArrayList<>();
        try {
            // jdbc 类型 获取所有库: 适配没有schema的数据源
            List<DbTableVO> indexList = DatasourceUtils.getFunctionList(datasourceDTO);
            for (DbTableVO dbTableVO : indexList) {
                MetadataNodeVO table = new MetadataNodeVO();
                table.setDatasourceInfoId(datasourceId);
                table.setName(dbTableVO.getName());
                table.setType(dbTableVO.getType());
                table.setComment(dbTableVO.getComment());
                String schemaName = dbTableVO.getSchemaName();
                if (StringUtils.isEmpty(schemaName)) {
                    schemaName = "";
                }
                table.setSchemaName(schemaName);
                table.setDbName(dto.getDbName());
                table.setTableName(dto.getTableName());
                table.setChildrens(new ArrayList<>());
                String uuid = MD5Util.resourceToMD5(datasourceId, dto.getDbName(), schemaName, dbTableVO.getName(), dto.getType(), dbTableVO.getTableName());
                table.setUuid(uuid);
                table.setLevel(level);
                table.setLeaf(true);
                // 假设 options 需要根据具体数据源动态设置
                tablelist.add(table);
            }

        } catch (AppErrorException e) {
            // 处理异常
            log.error("Error retrieving tablesbydb: " + e.getResult().toString());
            // 可以记录日志或者抛出自定义异常
            throw new AppErrorException("Failed to retrieve tablesbydb", e);
        }
        return tablelist;
    }

    /**
     * 获取所有库：有schema 是获取所有schema
     *
     * @param datasourceDTO 数据源信息对像
     * @param task
     * @return
     */
    @Override
    public List<String> getAllDbs(DatasourceDTO datasourceDTO, MetadataTask task) {
        //todo  根据数据源id获取数据源配置
        //判断数据源对象是否为null
        if (Objects.isNull(datasourceDTO)) {
            throw new AppErrorException("数据源对象为空", task.getDatasourceInfoId());
        }
        //获取有模式得集合
        //数据源登记：指定库
        List<String> dblist = new ArrayList<>();
        String dbName = datasourceDTO.getDbName();
        String schema = datasourceDTO.getSchema();
        List<String> schemaDatasourceType = DatasourceTypeDatabase.schemaDataTypeList();
        if (schemaDatasourceType.contains(datasourceDTO.getDataType().toLowerCase())) {
            //数据源登记：指定库-指定模式
            if (StringUtils.isNotEmpty(dbName) && StringUtils.isNotEmpty(schema)) {
                schema = dbName + "." + schema;
                dblist.add(schema);
                return dblist;
            }

        } else {
            if (StringUtils.isNotEmpty(dbName)) {
                dblist.add(dbName);
                return dblist;
            }
        }

        //采集得db 处理
        dblist = applyDbFilterSelect(task, datasourceDTO);
        return dblist;
    }


    /**
     * 收集元数据
     * 该方法用于从数据源中收集元数据信息，如表结构、列名等
     * 通过覆盖父类方法实现，以适应特定的数据源类型
     *
     * @param datasourceId 包含数据源连接信息的对象，如数据库连接字符串和认证信息
     *                     该参数是方法操作的核心对象，决定了数据源的连接和访问方式
     * @param task         具体的元数据任务对象，描述了需要收集的元数据类型和范围
     *                     该任务对象指导方法如何进行元数据的收集和处理
     */
    @Override
    public String collectMetadata(String datasourceId, MetadataTask task, String metadataTaskRecord, MetadataTaskDTO metadataTaskDTO) {
        metadataTaskRecordId = metadataTaskRecord;
        taskDTO = metadataTaskDTO;
        DatasourceDTO datasourceDTO = new DatasourceDTO();
        /**
         * 步骤1：获取数据源对象
         */
        String step1Id = IdUtils.simpleUUID();
        try {
            //1.获取数据源对象
            MetadataTaskStep save = metadataTaskStepService.save(step1Id, 1, Constants.STEP_NAME_CONNECT, Constants.STEP_CONTENT_CONNECT, metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), null, new Date(), null);
            //测试连接
            DatasourceConnectProgressVO datasourceConnectProgressVO = datasourceInfoService.connectById(datasourceId);
            Integer status = datasourceConnectProgressVO.getStatus();
            if(0 ==status){
                updateMetadataTaskRecord(metadataTaskRecordId, "数据源连接失败:"+datasourceConnectProgressVO.getMsg(), MetadataCollectionStatusEnum.FAILURE.getCode(), 0, 0, 0);
                clearGlobalParam();
                //检查是否发送告警
                boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
                if (b) {
                    //构建发送消息实体
                    sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
                }
                metadataTaskStepService.update(step1Id, MetadataCollectionStatusEnum.FAILURE.getCode(), "数据源连接失败:"+datasourceConnectProgressVO.getMsg(), new Date());
                //更新操作步骤
                return metadataTaskRecordId;
            }
            datasourceDTO = getDatasourceDTO(datasourceId);
        } catch (AppErrorException e) {
            log.error("获取数据源对象失败{}", e.getMessage());
            updateMetadataTaskRecord(metadataTaskRecordId, e.getResult().toString(), MetadataCollectionStatusEnum.FAILURE.getCode(), 0, 0, 0);
            clearGlobalParam();
            //检查是否发送告警
            boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
            if (b) {
                //构建发送消息实体
                sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
            }
            metadataTaskStepService.update(step1Id, MetadataCollectionStatusEnum.FAILURE.getCode(), e.getResult().toString(), new Date());
            //更新操作步骤
            return metadataTaskRecordId;

        }
        /**
         * 步骤二名称：资源发现
         */
        // todo 1.获取所有数据库
        List<String> databases = new ArrayList<>();
        String step2Id = IdUtils.simpleUUID();
        try {
            MetadataTaskStep save = metadataTaskStepService.save(step2Id, 2, Constants.STEP_NAME_DISCOVERY, Constants.STEP_CONTENT_DISCOVERY, metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), null, new Date(), null);
            databases = getAllDbs(datasourceDTO, task);
        } catch (AppErrorException e) {
            log.error("数据源[{}],采集记录[{}],获取所有库异常：{}", datasourceId, metadataTaskRecordId, e.getResult().toString());
            updateMetadataTaskRecord(metadataTaskRecordId, e.getResult().toString().toString(), MetadataCollectionStatusEnum.FAILURE.getCode(), 0, 0, 0);
            clearGlobalParam();
            //检查是否发送告警
            boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
            if (b) {
                //构建发送消息实体
                sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
            }
            metadataTaskStepService.update(step2Id, MetadataCollectionStatusEnum.FAILURE.getCode(), e.getResult().toString(), new Date());
            return metadataTaskRecordId;
        }

        //todo 2.要采集的资源
        List<MetadataTableCommon> tablesAndViewsAll = new ArrayList<>();

        //统计采集结果总数
        int taskTotal = 0;
        //操作统计对象
        GatherMetadataDTO gatherMetadataCount = new GatherMetadataDTO();
        /**
         * 步骤三名称：资源筛选
         */
        String step3Id = IdUtils.simpleUUID();
        try {
            MetadataTaskStep save = metadataTaskStepService.save(step3Id, 3, Constants.STEP_NAME_FILTER, Constants.STEP_CONTENT_FILTER, metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), null, new Date(), null);

            //todo  数据源对象
            for (String database : databases) {
                //todo  pg 处理jdbcUrl拼接 data  1.dbName   2.dbName.schemaName
                if (DatasourceTypeDatabase.schemaDataTypeList().contains(datasourceDTO.getDataType().toLowerCase())) {
                    //处理url
                    String[] split = database.split("\\.");
                    datasourceDTO = DatasourceUtil.replaceDatasourceDTODbName(datasourceDTO, split[0], split[1]);
                } else {
                    datasourceDTO = DatasourceUtil.replaceDatasourceDTODbName(datasourceDTO, database);
                }
                log.error("datasourceDTO 参数：{}", datasourceDTO);
                List<MetadataTableCommon> tablesAndViews = getTablesAndViews(datasourceDTO, database, task, gatherMetadataCount);
                taskTotal += tablesAndViews.size();
                if (!tablesAndViews.isEmpty()) {
                    tablesAndViewsAll.addAll(tablesAndViews);
                }
            }
//            if (taskTotal == 0) {
//                updateMetadataTaskRecord(metadataTaskRecordId, "", MetadataCollectionStatusEnum.SUCCESS.getCode(), taskTotal, 0, 0);
//                log.info("采集结束：需要采集得表数量{}", taskTotal);
//                clearGlobalParam();
//                return metadataTaskRecordId;
//            }
            //修改采集表总数
            updateMetadataTaskRecord(metadataTaskRecordId, "", null, gatherMetadataCount.getConfigTotal(), 0, 0);

        } catch (AppErrorException e) {
            log.error("数据源[{}],采集记录[{}],获取采集资源异常：{}", task.getDatasourceInfoId(), metadataTaskRecordId, e.getMessage());
            updateMetadataTaskRecord(metadataTaskRecordId, e.getMessage(), MetadataCollectionStatusEnum.FAILURE.getCode(), 0, 0, 0);
            clearGlobalParam();
            //检查是否发送告警
            boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
            if (b) {
                //构建发送消息实体
                sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
            }
            metadataTaskStepService.update(step3Id, MetadataCollectionStatusEnum.FAILURE.getCode(), e.getResult().toString(), new Date());
            return metadataTaskRecordId;
        }
        /**
         * 步骤四名称：元数据采集
         */
        String step4Id = IdUtils.simpleUUID();
        // todo 3.获取此次应该删除的表
        MetadataTaskStep save = metadataTaskStepService.save(step4Id, 4, Constants.STEP_NAME_COLLECT, Constants.STEP_CONTENT_COLLECT, metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), null, new Date(), null);
        try {
            int i = metadataTableToDeleteList(datasourceDTO, gatherMetadataCount, task);
            if (i > 0) {
                updateMetadataTaskRecord(metadataTaskRecordId, "", null, null, null, i);
            }
        } catch (AppErrorException e) {
            log.error("数据源[{}],此次采集异常信息为{}", datasourceDTO.getDataName(), e.getResult().toString());
            updateMetadataTaskRecord(metadataTaskRecordId, e.getResult().toString(), MetadataCollectionStatusEnum.FAILURE.getCode(), null, 0, 0);
            clearGlobalParam();
            //检查是否发送告警
            boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
            if (b) {
                //构建发送消息实体
                sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
            }
            metadataTaskStepService.update(step4Id, MetadataCollectionStatusEnum.FAILURE.getCode(), e.getResult().toString(), new Date());
            return metadataTaskRecordId;
        }

        // todo 4. 进行表具体得采集
        try {

            log.info("数据源[{}],采集资源开始", datasourceDTO.getDataName());
            //先将平台采集过的表集合 tablesToCollect 转为以id 为key 的map
            Map<String, MetadataTableCommon> metadataTableCommonMap = new HashMap<>();
            if (CollUtil.isNotEmpty(tablesToCollect)) {
                //将部门列表转为map，id作为key，Dept 作为value 重复值取第一条
                metadataTableCommonMap = tablesToCollect.stream().collect(Collectors.toMap(MetadataTableCommon::getId, metadataTableCommon -> metadataTableCommon));
            }

            //平台已删除的表集合
            Map<String, MetadataTableCommon> metadataTableCommonMapToDelete = new HashMap<>();
            List<MetadataTableCommon> tablesToDelete = getTablesToCollect(task, 1, true);
            if (CollUtil.isNotEmpty(tablesToDelete)) {
                //将部门列表转为map，id作为key，Dept 作为value 重复值取第一条
                metadataTableCommonMapToDelete = tablesToDelete.stream().collect(Collectors.toMap(MetadataTableCommon::getId, metadataTableCommon -> metadataTableCommon));
            }
            for (MetadataTableCommon metadataTable : tablesAndViewsAll) {

                if (!taskDTO.isRunning()) {
                    log.info("数据源[{}],采集资源中断停止", datasourceDTO.getDataName());
//                    updateMetadataTaskRecord(metadataTaskRecordId, "采集中断停止", MetadataCollectionStatusEnum.INTERRUPTED.getCode(), null, null, null);
                    clearGlobalParam();
                    //要更新统计值
                    //更新采集统计
                    MetadataTaskRecord metadataTaskRecordCount = new MetadataTaskRecord();
                    BeanUtil.copyProperties(gatherMetadataCount, metadataTaskRecordCount);
                    metadataTaskRecordCount.setStatus(MetadataCollectionStatusEnum.INTERRUPTED.getCode());
                    metadataTaskRecordCount.setId(metadataTaskRecordId);
                    metadataTaskRecordCount.setErrorLog("采集中断停止");
                    metadataTaskRecordService.update(metadataTaskRecordId, metadataTaskRecordCount);
                    metadataTaskStepService.update(step4Id, MetadataCollectionStatusEnum.INTERRUPTED.getCode(), "采集中断停止", new Date());
                    return metadataTaskRecordId;
                }
                //处理采集
                processTableMetadata(metadataTable, datasourceDTO, metadataTaskRecordId, task, metadataTableCommonMap, gatherMetadataCount, metadataTableCommonMapToDelete);
            }
            log.info("数据源[{}],采集资源结束,采集数量[{}]", datasourceDTO.getDataName(), tablesAndViewsAll.size());

            clearGlobalParam();
        } catch (AppErrorException e) {
            clearGlobalParam();
            log.info("数据源[{}],采集资源结束,采集异常：{}", datasourceDTO.getDataName(), e.getResult());
            //修改采集记录状态为失敗
            updateMetadataTaskRecord(metadataTaskRecordId, e.getResult().toString(), MetadataCollectionStatusEnum.FAILURE.getCode(), null, null, null);
            //检查是否发送告警
            boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
            if (b) {
                //构建发送消息实体
                sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
            }
            metadataTaskStepService.update(step4Id, MetadataCollectionStatusEnum.FAILURE.getCode(), e.getResult().toString(), new Date());

        }
        /**
         * 步骤五名称： 结果处理
         */
        //结果处理
        String step5Id = IdUtils.simpleUUID();
        try {
            metadataTaskStepService.save(step5Id, 5, Constants.STEP_NAME_RESULT, Constants.STEP_CONTENT_RESULT, metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), null, new Date(), null);
            //修改采集记录状态为成功
            //平台最终采集表数据量
            //获取平台已经采集
            MetadataTaskRecord metadataTaskRecordCount = new MetadataTaskRecord();
            BeanUtil.copyProperties(gatherMetadataCount, metadataTaskRecordCount);

            List<MetadataTableCommon> tablesAndViewsPlatform = getTablesToCollect(task, 1, false);
//            updateMetadataTaskRecord(metadataTaskRecordId, "", MetadataCollectionStatusEnum.SUCCESS.getCode(), null, tablesAndViewsPlatform.size(), null);
            //获取采集记录表里面是否有失败得表
            Integer total = metadataTableCommonDetailService.getService(MetadataTableCommonDetail.class).getQuery().eq("metadataTaskRecordId", metadataTaskRecordId)
                    .eq("status", MetadataCollectionStatusEnum.FAILURE.getCode()).total();
            if (total > 0) {
                metadataTaskRecordCount.setContentStatus(MetadataCollectionStatusEnum.FAILURE.getCode());
            } else {
                metadataTaskRecordCount.setContentStatus(MetadataCollectionStatusEnum.SUCCESS.getCode());
            }
            //更新采集统计
            metadataTaskRecordCount.setStatus(MetadataCollectionStatusEnum.SUCCESS.getCode());
            metadataTaskRecordCount.setId(metadataTaskRecordId);
            metadataTaskRecordCount.setQfRecordTotal(tablesAndViewsPlatform.size());
            metadataTaskRecordCount.setEndTime(new Date());
            metadataTaskRecordService.update(metadataTaskRecordId, metadataTaskRecordCount);
            clearGlobalParam();
        } catch (AppErrorException e) {
            clearGlobalParam();
            log.info("数据源[{}],采集资源统计结束,统计异常：{}", datasourceDTO.getDataName(), e.getResult());
            //修改采集记录状态为失敗
            updateMetadataTaskRecord(metadataTaskRecordId, e.getResult().toString(), MetadataCollectionStatusEnum.FAILURE.getCode(), null, null, null);
            //检查是否发送告警
            boolean b = checkAlarm(datasourceId, AlertStatusEnum.B401003);
            if (b) {
                //构建发送消息实体
                sendAlarm(datasourceDTO, AlertStatusEnum.B401003, task, null, null);
            }
            metadataTaskStepService.update(step5Id, MetadataCollectionStatusEnum.FAILURE.getCode(), e.getResult().toString(), new Date());
        }

        return metadataTaskRecordId;


    }

    /**
     * 此数据源有无配置任务失败或者元数据变更告警事件
     */
    public boolean checkAlarm(String datasourceId, AlertStatusEnum alertStatusEnum) {
        DatasourceAlert datasourceAlert = datasourceAlertService.getQuery()
                .lazys("datasourceAlertEvents")
                .eq("datasourceInfoId", datasourceId)
                .one();

        return datasourceAlert != null && datasourceAlert.getDatasourceAlertEvents().stream()
                .anyMatch(event -> alertStatusEnum.name().equals(event.getEventCode()));
    }

    /**
     * 发送告警
     */
    public void sendAlarm(DatasourceDTO datasourceDTO, AlertStatusEnum alertStatusEnum, MetadataTask taskDTO, MetadataTableCommon table, String changeInfo) {
        //构建发送消息实体
        AlarmMsg alarmMsg = sendAlertMsgService.convertAlarmMsg(datasourceDTO.getDatasourceId(), datasourceDTO.getDataName(), taskDTO.getTenantCode(), "", alertStatusEnum.name(), null);
        alarmMsg.setBusinessName(datasourceDTO.getDataName());
        alarmMsg.setBusinessId(datasourceDTO.getDataName());
        JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
        if (dataJsonMap != null && dataJsonMap.containsKey("jdbcUrl")) {
            alarmMsg.addAttr("jdbcUrl", dataJsonMap.getString("jdbcUrl"));
        }
        if (Objects.nonNull(table)) {
            alarmMsg.addAttr("tableName", table.getName());
            alarmMsg.addAttr("tableType", table.getType());

        } else {
            alarmMsg.setBusinessId(datasourceDTO.getDatasourceId());
        }
        if (StringUtils.isNotEmpty(changeInfo)) {
            alarmMsg.addAttr("changeInfo", changeInfo);
        }
        alarmMsg.addAttr("dataName", datasourceDTO.getDataName());
        alarmMsg.addAttr("datasourceId", datasourceDTO.getDatasourceId());
        log.info("数据源[{}],发送告警消息:{}", datasourceDTO.getDataName(), alarmMsg);
        sendAlertMsgService.sendMsg(alarmMsg, AlertStatusEnum.getDes(alertStatusEnum.name()));
    }


    /**
     * 8.获取数据库名称列表:数据源授权db或者schema Node
     *
     * @param datasourceId
     * @param dbName
     * @return
     */
    @Override
    public List<MetadataNodeVO> getDbNameOrSchemaNode(String datasourceId, String dbName, String datasourceTypeName) {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        List<MetadataNodeVO> metadataNodeVOS = new ArrayList<>();
        //如果是模式
        if (DatasourceTypeDatabase.schemaDataTypeList().contains(datasourceTypeName.toLowerCase())) {
            List<MetadataTableCommon> schemaNameByDatasourceinfoId = metadataTableCommonService.getSchemaNameByDatasourceinfoId(datasourceId, dbName);
            if (CollUtil.isEmpty(schemaNameByDatasourceinfoId)) {
                return metadataNodeVOS;
            }
            //以dbName作为key，MetadataTableCommon得list作为value
            Map<String, List<MetadataTableCommon>> dbNameMap = schemaNameByDatasourceinfoId.stream().collect(Collectors.groupingBy(MetadataTableCommon::getDbName));
            for (Map.Entry<String, List<MetadataTableCommon>> entry : dbNameMap.entrySet()) {
                MetadataNodeVO metadataCalalogVO = new MetadataNodeVO();
                metadataCalalogVO.setName(entry.getKey());
                metadataCalalogVO.setDbName(entry.getKey());
                metadataCalalogVO.setSchemaName("");
                metadataCalalogVO.setCnName(MetadataTaskObjectEnum.DB.getChineseName());
                String uuid = MD5Util.resourceToMD5(datasourceId, entry.getKey(), entry.getKey(), "", MetadataTaskObjectEnum.DB.getType(), "");
                metadataCalalogVO.setLeaf(false);
                metadataCalalogVO.setUuid(uuid);
                metadataCalalogVO.setType(MetadataTaskObjectEnum.DB.getType());
                metadataCalalogVO.setHasSchema(true);
                //模式得组装
                List<MetadataTableCommon> value = entry.getValue();
                List<MetadataNodeVO> childrens = new ArrayList<>();
                //按模式名称分组
                Map<String, List<MetadataTableCommon>> schemaNameMap = value.stream().collect(Collectors.groupingBy(MetadataTableCommon::getSchemaName));
                for (Map.Entry<String, List<MetadataTableCommon>> schemaNameEntry : schemaNameMap.entrySet()) {
                    MetadataNodeVO metadataSchemaVO = new MetadataNodeVO();
                    metadataSchemaVO.setName(schemaNameEntry.getKey());
                    metadataSchemaVO.setDbName(entry.getKey());
                    metadataSchemaVO.setSchemaName(schemaNameEntry.getKey());
                    metadataSchemaVO.setCnName(MetadataTaskObjectEnum.SCHEMA.getChineseName());
                    String uuid1 = MD5Util.resourceToMD5(datasourceId, entry.getKey(), entry.getKey(), schemaNameEntry.getKey(), MetadataTaskObjectEnum.SCHEMA.getType(), "");
                    metadataSchemaVO.setUuid(uuid1);
                    metadataSchemaVO.setType(MetadataTaskObjectEnum.SCHEMA.getType());
                    metadataSchemaVO.setLeaf(false);
                    metadataSchemaVO.setChildrens(getCommonNode(uuid1, "2", entry.getKey(), schemaNameEntry.getKey()));
                    childrens.add(metadataSchemaVO);
                }
                metadataCalalogVO.setChildrens(childrens);
                metadataNodeVOS.add(metadataCalalogVO);
            }
        } else {
            List<MetadataTableCommon> dbNameByDatasourceinfoId = metadataTableCommonService.getDbNameByDatasourceinfoId(datasourceId, dbName);
            for (MetadataTableCommon metadataTable : dbNameByDatasourceinfoId) {
                MetadataNodeVO metadataCalalogVO = new MetadataNodeVO();
                metadataCalalogVO.setName(metadataTable.getDbName());
                metadataCalalogVO.setDbName(metadataTable.getDbName());
                metadataCalalogVO.setSchemaName("");
                metadataCalalogVO.setCnName(MetadataTaskObjectEnum.DB.getChineseName());
                String uuid = MD5Util.resourceToMD5(datasourceId, metadataTable.getDbName(), "", metadataTable.getDbName(), MetadataTaskObjectEnum.DB.getType(), metadataTable.getTableName());
                metadataCalalogVO.setUuid(uuid);
                metadataCalalogVO.setType(MetadataTaskObjectEnum.DB.getType());
                metadataCalalogVO.setProjectId("");
                metadataCalalogVO.setLeaf(false);
                //设置子节点
                metadataCalalogVO.setChildrens(getCommonNode(uuid, "", metadataTable.getDbName(), metadataTable.getSchemaName()));
                metadataNodeVOS.add(metadataCalalogVO);
            }
        }

        return metadataNodeVOS;
    }

    /**
     * 9.获取数据库名称列表:数据源授权
     *
     * @param datasourceId
     * @param dbName
     * @return
     */
    @Override
    public List<DatasourceAuthVO> getDbNameOrSchemaList(String datasourceId, String dbName, String datasourceTypeName) {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        List<DatasourceAuthVO> datasourceAuths = new ArrayList<>();
        if (DatasourceTypeDatabase.schemaDataTypeList().contains(datasourceTypeName.toLowerCase())) {
            List<MetadataTableCommon> schemaNameByDatasourceinfoId = metadataTableCommonService.getSchemaNameByDatasourceinfoId(datasourceId, dbName);
            for (MetadataTableCommon metadataTable : schemaNameByDatasourceinfoId) {
                DatasourceAuthVO datasourceAuth = new DatasourceAuthVO();
                datasourceAuth.setDbName(metadataTable.getDbName());
                datasourceAuth.setSchemaName(metadataTable.getSchemaName());
                datasourceAuth.setDatasourceInfoId(datasourceId);
                datasourceAuth.setIsAuth(false);
                datasourceAuth.setProjectIds(new ArrayList<>());
                String uuid = MD5Util.resourceToMD5(datasourceId, dbName, metadataTable.getDbName(), metadataTable.getSchemaName(), MetadataTaskObjectEnum.SCHEMA.getType(), metadataTable.getTableName());
                datasourceAuth.setUuid(uuid);
                datasourceAuths.add(datasourceAuth);
            }

        } else {
            List<MetadataTableCommon> dbNameByDatasourceinfoId = metadataTableCommonService.getDbNameByDatasourceinfoId(datasourceId, dbName);
            for (MetadataTableCommon metadataTable : dbNameByDatasourceinfoId) {
                DatasourceAuthVO datasourceAuth = new DatasourceAuthVO();
                datasourceAuth.setDbName(metadataTable.getDbName());
                datasourceAuth.setSchemaName(metadataTable.getSchemaName());
                datasourceAuth.setDatasourceInfoId(datasourceId);
                datasourceAuth.setIsAuth(false);
                datasourceAuth.setProjectIds(new ArrayList<>());
                String uuid = MD5Util.resourceToMD5(datasourceId, metadataTable.getDbName(), metadataTable.getSchemaName(), metadataTable.getDbName(), metadataTable.getType(), metadataTable.getTableName());
                datasourceAuth.setUuid(uuid);
                datasourceAuths.add(datasourceAuth);
            }
        }

        return datasourceAuths;
    }

    /**
     * 是否采集
     *
     * @param datasourceinfoId
     * @return
     */
    @Override
    public Boolean isCollection(String datasourceinfoId) {
        boolean isCollection = false;
        Integer datasourceInfoId = metadataTableCommonService.getQuery().eq("datasourceInfoId", datasourceinfoId).total();
        if (datasourceInfoId > 0) {
            isCollection = true;
        }
        return isCollection;
    }


    /**
     * 10.获取表列表：数据源授权
     *
     * @param table
     * @param page
     * @param pager
     * @param tableNames
     * @return
     */
    @Override
    public List<DatasourceAuthVO> getTableList(MetadataTableCommon table, Integer page, Integer pager, List<String> tableNames) {
        // 根据数据源信息ID查询并获取数据源信息，同时加载关联的数据源类型名称
        List<DatasourceAuthVO> datasourceAuths = new ArrayList<>();
        List<MetadataTableCommon> dbNameByDatasourceinfoId = metadataTableCommonService.getTableByDatasourceinfoId(table, page, pager, tableNames);
        for (MetadataTableCommon metadataTable : dbNameByDatasourceinfoId) {
            DatasourceAuthVO datasourceAuth = new DatasourceAuthVO();
            datasourceAuth.setDbName(metadataTable.getDbName());
            datasourceAuth.setSchemaName(metadataTable.getSchemaName());
            datasourceAuth.setDatasourceInfoId(metadataTable.getDatasourceInfoId());
            datasourceAuth.setMetadataTableCommonId(metadataTable.getId());
            datasourceAuth.setMetadataTableName(metadataTable.getName());
            datasourceAuth.setMetadataTableType(metadataTable.getType());
            datasourceAuth.setProjectId("");
            datasourceAuth.setIsAuth(false);
            datasourceAuth.setProjectIds(new ArrayList<>());
            datasourceAuths.add(datasourceAuth);
        }
        return datasourceAuths;
    }

    //清空全局参数
    public void clearGlobalParam() {
        tablesToCollect = new ArrayList<>();
//        isTablesExistList = new ArrayList<>();
        isTablesToDelete = new ArrayList<>();
        datasourceMatchedTables = new ArrayList<>();
        metadataColumnCommonsMap = new HashMap<>();
        qfMetadataColumnCommonsMap = new HashMap<>();
    }

    /**
     * 处理采集到的表和视图元数据
     */
    @Transactional(rollbackFor = AppErrorException.class)
    public void processTableMetadata(MetadataTableCommon table, DatasourceDTO datasourceDTO,
                                     String metadataTaskRecordId, MetadataTask task,
                                     Map<String, MetadataTableCommon> metadataTableCommonMap,
                                     GatherMetadataDTO gatherMetadataDTO, Map<String, MetadataTableCommon> metadataTableCommonMapToDelete) throws AppErrorException {

        List<MetadataColumnCommon> columns = new ArrayList<>();

        String msg = "";
        try {

            //快速选表时才判断
            if (MetadataCollectionRangeEnum.QUICK_SELECT_TABLE.getCode() == task.getAcquisitionScope()) {
                //先将所有表集合 matchedTables 转为以id 为key 的map
                Map<String, MetadataTableCommon> matchedTableCommonMap = new HashMap<>();
                if (CollUtil.isNotEmpty(datasourceMatchedTables)) {
                    //将部门列表转为map，id作为key，Dept 作为value
                    matchedTableCommonMap = datasourceMatchedTables.stream().collect(Collectors.toMap(MetadataTableCommon::getId, metadataTableCommon -> metadataTableCommon));
                }
                //判断此表在所有采集资源中是否存在
                if (!matchedTableCommonMap.containsKey(table.getId())) {
                    updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.FAILURE.getCode(), "此资源在库中不存在", "", null, "");
                    return;
                }
            }
            if (Objects.isNull(datasourceDTO)) {
                datasourceDTO = getDatasourceDTO(task.getDatasourceInfoId());
            }
            if (datasourceDTO == null) {
                updateMetadataTaskRecord(metadataTaskRecordId, "数据源连接信息datasourceDTO为空", MetadataCollectionStatusEnum.FAILURE.getCode(), null, null, null);
                return;
            }
            datasourceDTO.setDbName(table.getDbName());
            if (StringUtils.isNotEmpty(table.getSchemaName())) {
                datasourceDTO.setSchema(table.getSchemaName());
            }
            datasourceDTO.setTableName(table.getTableName());
            //获取表字段
            log.info("开始采集表{},字段信息", table.getDbName() + "." + table.getName());
            if (metadataColumnCommonsMap.containsKey(table.getId())) {
                columns = metadataColumnCommonsMap.get(table.getId());
            } else {
                columns = getColumns(table, datasourceDTO);
            }
            log.info("结束采集表{},字段信息：{}", table.getDbName() + "." + table.getName(), columns);

            //设置字段数
            table.setMetadataColumnTotal(columns.size());
        } catch (AppErrorException e) {
            log.error("采集表{}字段信息异常：{}", table.getDbName() + "." + table.getName(), e.getMessage());
            //根据表id和采集记录id修改表详情状态为失败
            msg = e.getMessage();
//            updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.FAILURE.getCode(), e.getMessage(), "", null);
        } finally {
            datasourceDTO.setSchema(null);
            datasourceDTO.setType(null);
            datasourceDTO.setTableName(null);
            datasourceDTO.setDbName(null);
        }

        //若字段为空则返回
        if (CollUtil.isEmpty(columns)) {
            if (StringUtils.isEmpty(msg)) {
                msg = "采集表字段信息为空";
            }
            updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.FAILURE.getCode(), msg, "", null, "");
            return;
        }

        try {
            log.info("开始保存采集表{}的信息", table.getDbName() + "." + table.getName());
            //保存表和字段
            saveTableAndColumns(table, columns, task, metadataTableCommonMap, datasourceDTO, gatherMetadataDTO, metadataTableCommonMapToDelete);
            log.info("结束保存采集表{}的信信息", table.getDbName() + "." + table.getName());
        } catch (AppErrorException e) {

            log.error("保存采集表{},和字段信息异常：{}", table.getDbName() + "." + table.getName(), e.getResult().toString());
            //根据表id和采集记录id修改表详情状态为失败
            updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.FAILURE.getCode(), e.getResult().toString(), "", null, "");
        }


    }

    /**
     * 元数据采集结果统计
     *
     * @param gatherMetadataDTO
     * @param table
     * @param operationType
     */
    private void updateMetadataCounts(GatherMetadataDTO gatherMetadataDTO, MetadataTableCommon table
            , OperationType operationType) {
        if (operationType == OperationType.INSERT) {
            Map<String, Runnable> typeCounterMap = new HashMap<>();
            typeCounterMap.put(MetadataTaskObjectEnum.TABLE.getType(), () -> gatherMetadataDTO.setAddTableCount(gatherMetadataDTO.getAddTableCount() + 1));
            typeCounterMap.put(MetadataTaskObjectEnum.VIEW.getType(), () -> gatherMetadataDTO.setAddViewCount(gatherMetadataDTO.getAddViewCount() + 1));
            typeCounterMap.put(MetadataTaskObjectEnum.PROCEDURE.getType(), () -> gatherMetadataDTO.setAddProcedureCount(gatherMetadataDTO.getAddProcedureCount() + 1));
            typeCounterMap.put(MetadataTaskObjectEnum.FUNCTION.getType(), () -> gatherMetadataDTO.setAddFunctionCount(gatherMetadataDTO.getAddFunctionCount() + 1));
            typeCounterMap.put(MetadataTaskObjectEnum.INDEX.getType(), () -> gatherMetadataDTO.setAddIndexCount(gatherMetadataDTO.getAddIndexCount() + 1));

            Optional.ofNullable(typeCounterMap.get(table.getType()))
                    .ifPresent(Runnable::run);
        }

        if (operationType == OperationType.UPDATE) {
            Map<String, Runnable> typeCounterMap = new HashMap<>();
            typeCounterMap.put(MetadataTaskObjectEnum.TABLE.getType(), () -> gatherMetadataDTO.setUpdateTableCount(gatherMetadataDTO.getUpdateTableCount() + 1));
            typeCounterMap.put(MetadataTaskObjectEnum.VIEW.getType(), () -> gatherMetadataDTO.setUpdateViewCount(gatherMetadataDTO.getUpdateViewCount() + 1));
            typeCounterMap.put(MetadataTaskObjectEnum.PROCEDURE.getType(), () -> gatherMetadataDTO.setUpdateProcedureCount(gatherMetadataDTO.getUpdateProcedureCount() + 1));
            typeCounterMap.put(MetadataTaskObjectEnum.FUNCTION.getType(), () -> gatherMetadataDTO.setUpdateFunctionCount(gatherMetadataDTO.getUpdateFunctionCount() + 1));
            typeCounterMap.put(MetadataTaskObjectEnum.INDEX.getType(), () -> gatherMetadataDTO.setUpdateIndexCount(gatherMetadataDTO.getUpdateIndexCount() + 1));

            Optional.ofNullable(typeCounterMap.get(table.getType()))
                    .ifPresent(Runnable::run);
        }
    }

    //保存表的信息和字段信息
    @Transactional(rollbackFor = AppErrorException.class)
    public void saveTableAndColumns(MetadataTableCommon table, List<MetadataColumnCommon> columns,
                                    MetadataTask task, Map<String, MetadataTableCommon> metadataTableCommonMap,
                                    DatasourceDTO datasourceDTO, GatherMetadataDTO gatherMetadata, Map<String, MetadataTableCommon> metadataTableCommonMapToDelete) throws AppErrorException {
        //1.若为表级增量直接新增
        if (MetadataTaskWayEnum.TABLE_INCREMENTAL.getCode() == task.getTaskWay()) {
            log.info("表级增量采集，直接新增表{}和字段{}", table.getDbName() + "." + table.getName(), columns.size());
            //判断该表是否删除过，若删除过则修改恢复
            if (Objects.nonNull(metadataTableCommonMapToDelete) && metadataTableCommonMapToDelete.containsKey(table.getId())) {
                //修改恢复
                table.setDelFlag(false);
                metadataTableCommonService.update(table, true);
                //字段也是修改
                columns.forEach(column -> {
                    column.setDelFlag(false);
                    metadataColumnCommonService.update(column, true);
                });
            } else {
                metadataTableCommonService.add(table);
                metadataColumnCommonService.add(columns);
            }
            //记录字段详情表
            List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails = getMetadataTaskRecordColumnDetails(columns, table.getDbName(), table.getSchemaName(), OperationType.INSERT.name(), table.getType());
            //根据表id和采集记录id修改表详情进行修改记录
            metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails);
            //统计新增表类型
            updateMetadataCounts(gatherMetadata, table, OperationType.INSERT);
            gatherMetadata.setAddColumnCount(gatherMetadata.getAddColumnCount() + columns.size());
            return;
        }

        //2.字段级增量
        if (MetadataTaskWayEnum.FIELD_INCREMENTAL.getCode() == task.getTaskWay()) {
            //若表不在 则是直接新增
            //定义一个表是否更新标识
            boolean isTableUpdate = false;
            StringBuilder changeInfo = new StringBuilder();
            if (metadataTableCommonMap.containsKey(table.getId())) {
                log.info("字段级增量采集-此表存在，修改表{}和字段{}", table.getDbName() + "." + table.getName(), columns);
                //判断表的字段或者备注是否有更新
                MetadataTableCommon metadataTable = metadataTableCommonMap.get(table.getId());
                //判断表的字段或者备注是否有更新
                if (notEqualsTwoString(metadataTable.getComment(), table.getComment())) {
                    log.info("字段级增量采集-此表存在，修改表{}和字段{}", table.getDbName() + "." + table.getName(), columns);
                    //字段中文名不能修改
                    table.setCnName(null);
                    isTableUpdate = true;
                    //修改表备注
                    changeInfo.append(table.getName()).append("注释由 ").append(metadataTable.getComment()).append("改为").append(table.getComment()).append(",");
                }
                if (!metadataTable.getMetadataColumnTotal().equals(table.getMetadataColumnTotal())) {
                    table.setCnName(null);
                    isTableUpdate = true;
                    changeInfo.append(table.getName()).append("字段数由").append(metadataTable.getMetadataColumnTotal()).append("改为").append(table.getMetadataColumnTotal()).append(",");
                }
                //ddl变更
                if (notEqualsTwoString(metadataTable.getDdl(), table.getDdl())) {
                    table.setCnName(null);
                    isTableUpdate = true;
                    changeInfo.append(table.getName()).append("ddl由").append(metadataTable.getDdl()).append("改为").append(table.getDdl()).append(",");
                }
                if (isTableUpdate) {
                    metadataTableCommonService.update(table);
                }

                //更新字段时，1.先获取此表已采集哪些字段，2.获取此次采集字段应该删除得集合，3获取此次应该新增字段得集合，4获取此次应该更新字段得集合
                // 5.最后进行增、删、改的操作
                List<MetadataColumnCommon> metadataColumnsCollect = new ArrayList<>();
                if (qfMetadataColumnCommonsMap.containsKey(table.getId())) {
                    metadataColumnsCollect = qfMetadataColumnCommonsMap.get(table.getId());
                } else {
                    metadataColumnsCollect = metadataColumnCommonService.getTablesByTableId(table.getId());
                }
                List<MetadataColumnCommon> metadataColumnsToDelete = getMetadataColumnsToDelete(metadataColumnsCollect, columns);
                if (!metadataColumnsToDelete.isEmpty()) {
                    metadataColumnCommonService.deleteByTableId(metadataColumnsToDelete);
                    //记录删除字段详情
                    //记录字段详情表
                    List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails = getMetadataTaskRecordColumnDetails(metadataColumnsToDelete, table.getDbName(), table.getSchemaName(), OperationType.DELETE.name(), table.getType());
                    metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails);
                    log.info("字段级增量采集-此表存在，修改表{}，删除字段{}", table.getDbName() + "." + table.getName(), metadataColumnsToDelete);
                    //表更新
                    isTableUpdate = true;
                    //字段删除
                    String metadataColu = metadataColumnsToDelete.stream().map(MetadataColumnCommon::getColumnName).collect(Collectors.joining(","));
                    //将字段名拼接成字符串
                    changeInfo.append(table.getTableName()).append("删除字段").append(metadataColu).append(",");
                }
                List<MetadataColumnCommon> metadataColumnsToAdd = getMetadataColumnsToAdd(metadataColumnsCollect, columns);
                if (!metadataColumnsToAdd.isEmpty()) {
                    metadataColumnCommonService.add(metadataColumnsToAdd);
                    log.info("字段级增量采集-此表存在，修改表{}，新增字段{}", table.getDbName() + "." + table.getName(), metadataColumnsToAdd);
                    //记录新增字段详情
                    //记录字段详情表
                    List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails = getMetadataTaskRecordColumnDetails(metadataColumnsToAdd, table.getDbName(), table.getSchemaName(), OperationType.INSERT.name(), table.getType());
                    metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails);
                    //表更新
                    isTableUpdate = true;
                    changeInfo.append(table.getTableName()).append("新增字段").append(metadataColumnsToAdd.stream().map(MetadataColumnCommon::getColumnName).collect(Collectors.joining(","))).append(",");
                }
                List<MetadataColumnCommon> metadataColumnsToUpdate = getMetadataColumnsToUpdate(metadataColumnsCollect, columns);
                //新建集合 哪些需要更新，哪些不需要处理
                List<MetadataColumnCommon> metadataColumnsToUpdateNow = new ArrayList<>();
                //修改字段信息

                if (!metadataColumnsToUpdate.isEmpty()) {
                    //todo 比较字段是否更新
                    //不需要处理集合
                    List<MetadataColumnCommon> metadataColumnsToUpdateNo = new ArrayList<>();
                    //将metadataColumnsCollect集合转为id为key的，MetadataColumnCommon为value的map集合
                    Map<String, MetadataColumnCommon> metadataColumnsCollectMap = metadataColumnsCollect.stream().collect(Collectors.toMap(MetadataColumnCommon::getId, metadataColumnCommon -> metadataColumnCommon));
                    for (MetadataColumnCommon metadataColumnCommon : metadataColumnsToUpdate) {
                        MetadataColumnCommon metadataColumnCommonCollect = metadataColumnsCollectMap.get(metadataColumnCommon.getId());
                        GatherMetadataDTO gatherMetadataDTO = comparisonFieldAttribute(metadataColumnCommonCollect, metadataColumnCommon);
                        //判断是更新还是不处理
                        if (gatherMetadataDTO.getUpdateColumnFlag()) {
                            //更新
                            metadataColumnsToUpdateNow.add(metadataColumnCommon);
                            changeInfo.append(gatherMetadataDTO.getUpdateColumnDetail()).append(";");
                            isTableUpdate = true;
                        } else {
                            //不更新
                            metadataColumnsToUpdateNo.add(metadataColumnCommon);
                        }
                    }
                    log.info("字段级增量采集-此表存在，修改表{}，更新字段{}", table.getDbName() + "." + table.getName(), metadataColumnsToUpdateNow.size());
                    if (CollUtil.isNotEmpty(metadataColumnsToUpdateNow)) {
                        //根据id 批量更新
                        metadataColumnCommonService.batchUpdate(metadataColumnsToUpdateNow);

                        //记录字段详情：更新
                        List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails1 = getMetadataTaskRecordColumnDetails(metadataColumnsToUpdateNow, table.getDbName(), table.getSchemaName(), OperationType.UPDATE.name(), table.getType());
                        metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails1);
                    }
                    if (CollUtil.isNotEmpty(metadataColumnsToUpdateNo)) {
                        //记录字段详情：不处理
                        List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails = getMetadataTaskRecordColumnDetails(metadataColumnsToUpdateNo, table.getDbName(), table.getSchemaName(), OperationType.UNHANDLED.name(), table.getType());
                        metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails);
                    }
                    //todo  changeInfo长度大于0 则发字段变更告警

                }
                if (isTableUpdate) {
                    updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), "", OperationType.UPDATE.name(), table.getMetadataColumnTotal(), table.getComment());
                    updateMetadataCounts(gatherMetadata, table, OperationType.UPDATE);
                    //新增字段
                    gatherMetadata.setAddColumnCount(gatherMetadata.getAddColumnCount() + metadataColumnsToAdd.size());
                    //更新字段
                    gatherMetadata.setUpdateColumnCount(gatherMetadata.getUpdateColumnCount() + metadataColumnsToUpdateNow.size());
                    //删除字段
                    gatherMetadata.setDeleteColumnCount(gatherMetadata.getDeleteColumnCount() + metadataColumnsToDelete.size());
                    //todo  表变更告警信息
                    //检查是否发送告警
                    boolean b = checkAlarm(task.getDatasourceInfoId(), AlertStatusEnum.B402005);
                    if (b) {
                        //发送消息
                        sendAlarm(datasourceDTO, AlertStatusEnum.B402005, task, table, changeInfo.toString());
                    }
                } else {
                    updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), "", OperationType.UNHANDLED.name(), table.getMetadataColumnTotal(), table.getComment());
                }
            } else {
                log.info("字段级增量采集-此表不存在，新增表{}和字段{}", table.getDbName() + "." + table.getName(), columns.size());
                //判断该表是否删除过，若删除过则修改恢复
                if (Objects.nonNull(metadataTableCommonMapToDelete) && metadataTableCommonMapToDelete.containsKey(table.getId())) {
                    //修改恢复
                    table.setDelFlag(false);
                    metadataTableCommonService.update(table, true);
                    //字段也是修改
                    columns.forEach(column -> {
                        column.setDelFlag(false);
                        metadataColumnCommonService.update(column, true);
                    });
                } else {
                    metadataTableCommonService.add(table);
                    metadataColumnCommonService.add(columns);
                }
                //记录字段详情表
                List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails = getMetadataTaskRecordColumnDetails(columns, table.getDbName(), table.getSchemaName(), OperationType.INSERT.name(), table.getType());
                metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails);
                //修改表的采集状态
                updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), "", OperationType.INSERT.name(), table.getMetadataColumnTotal(), table.getComment());
                //统计新增表类型
                updateMetadataCounts(gatherMetadata, table, OperationType.INSERT);
                gatherMetadata.setAddColumnCount(gatherMetadata.getAddColumnCount() + columns.size());
            }
        }

        //3.全量
        if (MetadataTaskWayEnum.ALL.getCode() == task.getTaskWay()) {
            //1.若为全量，先判断此表是否采集过，若采集过则修改此表内容，若没有采集则新增此表信息
            //定义一个表是否更新标识
            boolean isTableUpdate = false;
            StringBuilder changeInfo = new StringBuilder();
            if (metadataTableCommonMap.containsKey(table.getId())) {
                MetadataTableCommon metadataTable = metadataTableCommonMap.get(table.getId());
                //判断表的字段或者备注是否有更新
                //判断表的字段或者备注是否有更新
                if (notEqualsTwoString(metadataTable.getComment(), table.getComment())) {
                    log.info("字段级增量采集-此表存在，修改表{}和字段{}", table.getDbName() + "." + table.getName(), columns);
                    //字段中文名不能修改
                    table.setCnName(null);
                    isTableUpdate = true;
                    //修改表备注
                    changeInfo.append(table.getName()).append("注释由 ").append(metadataTable.getComment()).append("改为").append(table.getComment()).append(",");
                }
                if (!metadataTable.getMetadataColumnTotal().equals(table.getMetadataColumnTotal())) {
                    table.setCnName(null);
                    isTableUpdate = true;
                    changeInfo.append(table.getName()).append("字段数由").append(metadataTable.getMetadataColumnTotal()).append("改为").append(table.getMetadataColumnTotal()).append(",");
                }
                //ddl变更
                if (notEqualsTwoString(metadataTable.getDdl(), table.getDdl())) {
                    table.setCnName(null);
                    isTableUpdate = true;
                    changeInfo.append(table.getName()).append("ddl由").append(metadataTable.getDdl()).append("改为").append(table.getDdl()).append(",");
                }
                if (isTableUpdate) {
                    metadataTableCommonService.update(table);
                }

                //更新字段时，1.先获取此表已采集哪些字段，2.获取此次采集字段应该删除得集合，3获取此次应该新增字段得集合，4获取此次应该更新字段得集合
                // 5.最后进行增、删、改的操作
                List<MetadataColumnCommon> metadataColumnsCollect = new ArrayList<>();
                if (qfMetadataColumnCommonsMap.containsKey(table.getId())) {
                    metadataColumnsCollect = qfMetadataColumnCommonsMap.get(table.getId());
                } else {
                    metadataColumnsCollect = metadataColumnCommonService.getTablesByTableId(table.getId());
                }
                List<MetadataColumnCommon> metadataColumnsToDelete = getMetadataColumnsToDelete(metadataColumnsCollect, columns);
                if (!metadataColumnsToDelete.isEmpty()) {
                    metadataColumnCommonService.deleteByTableId(metadataColumnsToDelete);
                    //记录删除字段详情
                    //记录字段详情表
                    List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails = getMetadataTaskRecordColumnDetails(metadataColumnsToDelete, table.getDbName(), table.getSchemaName(), OperationType.DELETE.name(), table.getType());
                    metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails);
                    log.info("全量采集-此表存在，修改表{}，删除字段{}", table.getDbName() + "." + table.getName(), metadataColumnsToDelete);
                    //表更新
                    isTableUpdate = true;
                    //字段删除
                    String metadataColu = metadataColumnsToDelete.stream().map(MetadataColumnCommon::getColumnName).collect(Collectors.joining(","));
                    //将字段名拼接成字符串
                    changeInfo.append(table.getTableName()).append("删除字段").append(metadataColu).append(",");
                }
                List<MetadataColumnCommon> metadataColumnsToAdd = getMetadataColumnsToAdd(metadataColumnsCollect, columns);
                if (!metadataColumnsToAdd.isEmpty()) {
                    metadataColumnCommonService.add(metadataColumnsToAdd);
                    //记录新增字段详情
                    //记录字段详情表
                    List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails = getMetadataTaskRecordColumnDetails(metadataColumnsToAdd, table.getDbName(), table.getSchemaName(), OperationType.INSERT.name(), table.getType());
                    metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails);
                    //表更新
                    isTableUpdate = true;
                    changeInfo.append(table.getTableName()).append("新增字段").append(metadataColumnsToAdd.stream().map(MetadataColumnCommon::getColumnName).collect(Collectors.joining(","))).append(",");
                    log.info("全量采集-此表存在，修改表{}，新增字段{}", table.getDbName() + "." + table.getName(), metadataColumnsToAdd);
                }
                //平台存在相同字段
                List<MetadataColumnCommon> metadataColumnsToUpdate = getMetadataColumnsToUpdate(metadataColumnsCollect, columns);
                //新建集合 哪些需要更新，哪些不需要处理
                List<MetadataColumnCommon> metadataColumnsToUpdateNow = new ArrayList<>();
                //修改字段信息

                if (!metadataColumnsToUpdate.isEmpty()) {
                    //todo 比较字段是否更新
                    //不需要处理集合
                    List<MetadataColumnCommon> metadataColumnsToUpdateNo = new ArrayList<>();
                    //将metadataColumnsCollect集合转为id为key的，MetadataColumnCommon为value的map集合
                    Map<String, MetadataColumnCommon> metadataColumnsCollectMap = metadataColumnsCollect.stream().collect(Collectors.toMap(MetadataColumnCommon::getId, metadataColumnCommon -> metadataColumnCommon));
                    for (MetadataColumnCommon metadataColumnCommon : metadataColumnsToUpdate) {
                        MetadataColumnCommon metadataColumnCommonCollect = metadataColumnsCollectMap.get(metadataColumnCommon.getId());
                        GatherMetadataDTO gatherMetadataDTO = comparisonFieldAttribute(metadataColumnCommonCollect, metadataColumnCommon);
                        //判断是更新还是不处理
                        if (gatherMetadataDTO.getUpdateColumnFlag()) {
                            //更新
                            metadataColumnsToUpdateNow.add(metadataColumnCommon);
                            changeInfo.append(gatherMetadataDTO.getUpdateColumnDetail()).append(";");
                            isTableUpdate = true;
                        } else {
                            //不更新
                            metadataColumnsToUpdateNo.add(metadataColumnCommon);
                        }
                    }
                    log.info("全量采集-此表存在，修改表{}，更新字段{}", table.getDbName() + "." + table.getName(), metadataColumnsToUpdateNow.size());
                    if (CollUtil.isNotEmpty(metadataColumnsToUpdateNow)) {
                        //根据id 批量更新
                        metadataColumnCommonService.batchUpdate(metadataColumnsToUpdateNow);

                        //记录字段详情：更新
                        List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails1 = getMetadataTaskRecordColumnDetails(metadataColumnsToUpdateNow, table.getDbName(), table.getSchemaName(), OperationType.UPDATE.name(), table.getType());
                        metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails1);
                    }
                    if (CollUtil.isNotEmpty(metadataColumnsToUpdateNo)) {
                        //记录字段详情：不处理
                        List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails = getMetadataTaskRecordColumnDetails(metadataColumnsToUpdateNo, table.getDbName(), table.getSchemaName(), OperationType.UNHANDLED.name(), table.getType());
                        metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails);
                    }

                }

                if (isTableUpdate) {
                    updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), "", OperationType.UPDATE.name(), table.getMetadataColumnTotal(), table.getComment());
                    updateMetadataCounts(gatherMetadata, table, OperationType.UPDATE);
                    //新增字段
                    gatherMetadata.setAddColumnCount(gatherMetadata.getAddColumnCount() + metadataColumnsToAdd.size());
                    //更新字段
                    gatherMetadata.setUpdateColumnCount(gatherMetadata.getUpdateColumnCount() + metadataColumnsToUpdateNow.size());
                    //删除字段
                    gatherMetadata.setDeleteColumnCount(gatherMetadata.getDeleteColumnCount() + metadataColumnsToDelete.size());
                    //todo  告警信息
                    //检查是否发送告警
                    boolean b = checkAlarm(task.getDatasourceInfoId(), AlertStatusEnum.B402005);
                    if (b) {
                        //发送消息
                        sendAlarm(datasourceDTO, AlertStatusEnum.B402005, task, table, changeInfo.toString());
                    }
                } else {
                    updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), "", OperationType.UNHANDLED.name(), table.getMetadataColumnTotal(), table.getComment());
                }

            } else {
                log.info("全量采集-此表不存在，新增表{}和字段{}", table.getDbName() + "." + table.getName(), columns.size());
                //判断该表是否删除过，若删除过则修改恢复
                if (Objects.nonNull(metadataTableCommonMapToDelete) && metadataTableCommonMapToDelete.containsKey(table.getId())) {
                    //修改恢复
                    table.setDelFlag(false);
                    metadataTableCommonService.update(table, true);
                    //字段也是修改
                    columns.forEach(column -> {
                        column.setDelFlag(false);
                        metadataColumnCommonService.update(column, true);
                    });
                } else {
                    metadataTableCommonService.add(table);
                    metadataColumnCommonService.add(columns);
                }
                //记录字段详情表
                List<MetadataColumnCommonDetail> metadataTaskRecordColumnDetails = getMetadataTaskRecordColumnDetails(columns, table.getDbName(), table.getSchemaName(), OperationType.INSERT.name(), table.getType());
                metadataColumnCommonDetailService.add(metadataTaskRecordColumnDetails);
                //修改表的采集状态
                updateMetadataTaskRecordTableDetails(table.getId(), metadataTaskRecordId, MetadataCollectionStatusEnum.SUCCESS.getCode(), "", OperationType.INSERT.name(), table.getMetadataColumnTotal(), table.getComment());
                //统计新增表类型
                updateMetadataCounts(gatherMetadata, table, OperationType.INSERT);
                gatherMetadata.setAddColumnCount(gatherMetadata.getAddColumnCount() + columns.size());
            }
        }

    }

    /**
     * 比较字段是否更新
     * 比较内容：字段类型、字段长度、字段精度、字段是否为空、字段备注、是否主键、排序规则、inOut
     */
    public GatherMetadataDTO comparisonFieldAttribute(MetadataColumnCommon qfMetadataColumnCommon, MetadataColumnCommon metadataColumnCommonNow) {
        GatherMetadataDTO gatherMetadataDTO = new GatherMetadataDTO();
        //字段更新标识
        boolean isFieldUpdate = false;
        //字段元数据变动信息
        StringBuilder changeInfo = new StringBuilder();
        if (notEqualsTwoString(qfMetadataColumnCommon.getColumnType(), metadataColumnCommonNow.getColumnType())) {
            //若值为null，则填充为空字符串
            if (StringUtils.isEmpty(qfMetadataColumnCommon.getColumnType())) {
                qfMetadataColumnCommon.setColumnType(" ");
            }
            changeInfo.append("字段类型由").append(qfMetadataColumnCommon.getColumnType()).append("改为").append(metadataColumnCommonNow.getColumnType()).append(",");
            isFieldUpdate = true;
        }
        if (notEqualsTwoString(qfMetadataColumnCommon.getColumnLength(), metadataColumnCommonNow.getColumnLength())) {
            if (StringUtils.isEmpty(qfMetadataColumnCommon.getColumnLength())) {
                qfMetadataColumnCommon.setColumnLength(" ");
            }
            changeInfo.append("字段长度由").append(qfMetadataColumnCommon.getColumnLength()).append("改为").append(metadataColumnCommonNow.getColumnLength()).append(",");
            isFieldUpdate = true;
        }
        if (notEqualsTwoInt(qfMetadataColumnCommon.getColumnScale(), metadataColumnCommonNow.getColumnScale())) {
            if (Objects.isNull(qfMetadataColumnCommon.getColumnScale())) {
                qfMetadataColumnCommon.setColumnScale(0);
            }
            changeInfo.append("字段精度由").append(qfMetadataColumnCommon.getColumnScale()).append("改为").append(metadataColumnCommonNow.getColumnScale()).append(",");
            isFieldUpdate = true;
        }
        if (notEqualsTwoInt(qfMetadataColumnCommon.getColumnNull(), metadataColumnCommonNow.getColumnNull())) {
            if (Objects.isNull(qfMetadataColumnCommon.getColumnNull())) {
                qfMetadataColumnCommon.setColumnNull(0);
            }
            changeInfo.append("字段是否为空由").append(qfMetadataColumnCommon.getColumnNull()).append("改为").append(metadataColumnCommonNow.getColumnNull()).append(",");
            isFieldUpdate = true;
        }
        if (notEqualsTwoString(qfMetadataColumnCommon.getColumnComment(), metadataColumnCommonNow.getColumnComment())) {
            if (StringUtils.isEmpty(qfMetadataColumnCommon.getColumnComment())) {
                qfMetadataColumnCommon.setColumnComment(" ");
            }
            changeInfo.append("字段备注由").append(qfMetadataColumnCommon.getColumnComment()).append("改为").append(metadataColumnCommonNow.getColumnComment()).append(",");
            isFieldUpdate = true;
        }
        if (notEqualsTwoInt(qfMetadataColumnCommon.getColumnPrimaryKey(), metadataColumnCommonNow.getColumnPrimaryKey())) {
            if (Objects.isNull(qfMetadataColumnCommon.getColumnPrimaryKey())) {
                qfMetadataColumnCommon.setColumnPrimaryKey(0);
            }
            changeInfo.append("是否主键由").append(qfMetadataColumnCommon.getColumnPrimaryKey()).append("改为").append(metadataColumnCommonNow.getColumnPrimaryKey()).append(",");
            isFieldUpdate = true;
        }
        if (notEqualsTwoInt(qfMetadataColumnCommon.getColumnForeignKey(), metadataColumnCommonNow.getColumnForeignKey())) {
            if (Objects.isNull(qfMetadataColumnCommon.getColumnForeignKey())) {
                qfMetadataColumnCommon.setColumnForeignKey(0);
            }
            changeInfo.append("是否外键由").append(qfMetadataColumnCommon.getColumnForeignKey()).append("改为").append(metadataColumnCommonNow.getColumnForeignKey()).append(",");
            isFieldUpdate = true;
        }
        if (notEqualsTwoString(qfMetadataColumnCommon.getInOut(), metadataColumnCommonNow.getInOut())) {
            if (StringUtils.isEmpty(qfMetadataColumnCommon.getInOut())) {
                qfMetadataColumnCommon.setInOut(" ");
            }
            if (StringUtils.isEmpty(qfMetadataColumnCommon.getInOut())) {
                qfMetadataColumnCommon.setInOut(" ");
            }
            changeInfo.append("inOut由").append(qfMetadataColumnCommon.getInOut()).append("改为").append(metadataColumnCommonNow.getInOut()).append(",");
            isFieldUpdate = true;
        }
        if (notEqualsTwoString(qfMetadataColumnCommon.getCollation(), metadataColumnCommonNow.getCollation())) {
            if (StringUtils.isEmpty(qfMetadataColumnCommon.getCollation())) {
                qfMetadataColumnCommon.setCollation(" ");
            }
            changeInfo.append("排序规则由").append(qfMetadataColumnCommon.getCollation()).append("改为").append(metadataColumnCommonNow.getCollation()).append(",");
            isFieldUpdate = true;
        }
        gatherMetadataDTO.setUpdateColumnFlag(isFieldUpdate);
        gatherMetadataDTO.setUpdateColumnDetail(changeInfo.toString());
        return gatherMetadataDTO;

    }

    /**
     * 比较两个字符串是否相等，会将null和空字符串认为是同一种值
     *
     * @return 不相等返回true，相等返回false
     */
    public boolean notEqualsTwoString(String str1, String str2) {
        str1 = str1 == null ? "" : str1;
        str2 = str2 == null ? "" : str2;
        return !str1.equals(str2);
    }

    /**
     * 比较两个int是否相等，会将null和空字符串认为是同一种值
     *
     * @return 不相等返回true，相等返回false
     */
    public boolean notEqualsTwoInt(Integer str1, Integer str2) {
        str1 = str1 == null ? 0 : str1;
        str2 = str2 == null ? 0 : str2;
        return !str1.equals(str2);
    }

    //4获取此次应该更新字段得集合
    public List<MetadataColumnCommon> getMetadataColumnsToUpdate(List<MetadataColumnCommon> metadataColumnsCollect, List<MetadataColumnCommon> columns) {
        //过滤出metadataColumnsCollect在columns存在的集合 并生成新集合
        // 直接用数组流式过滤
        List<MetadataColumnCommon> needToUpdate = columns.stream()
                .filter(col -> Arrays.stream(metadataColumnsCollect.toArray(new MetadataColumnCommon[0]))
                        .map(MetadataColumnCommon::getColumnName)
                        .anyMatch(name -> name.equals(col.getColumnName())))
                .collect(Collectors.toList());

        return needToUpdate;
    }


    //3获取此次应该新增字段得集合
    public List<MetadataColumnCommon> getMetadataColumnsToAdd(List<MetadataColumnCommon> metadataColumnsCollect, List<MetadataColumnCommon> columns) {
        //过滤出metadataColumnsCollect在columns不存在的集合 并生成新集合
        List<MetadataColumnCommon> needToInsert = columns.stream()
                .filter(b -> metadataColumnsCollect.stream().map(MetadataColumnCommon::getColumnName).noneMatch(columnName -> Objects.equals(b.getColumnName(), columnName))).collect(Collectors.toList());
        return needToInsert;
    }

    //2.获取此次采集字段应该删除得集合
    public List<MetadataColumnCommon> getMetadataColumnsToDelete(List<MetadataColumnCommon> metadataColumnsCollect, List<MetadataColumnCommon> columns) {
        //过滤出metadataColumnsCollect在columns不存在的集合 并生成新集合
        List<MetadataColumnCommon> needToDelete = metadataColumnsCollect.stream()
                .filter(b -> columns.stream().map(MetadataColumnCommon::getColumnName).noneMatch(columnName -> Objects.equals(b.getColumnName(), columnName)))
                .collect(Collectors.toList());
        return needToDelete;
    }


    //构建字段 List<MetadataColumn>转成List<MetadataTaskRecordColumnDetails>集合
    public List<MetadataColumnCommonDetail> getMetadataTaskRecordColumnDetails(List<MetadataColumnCommon> columns, String dbName,
                                                                               String schemaName, String option, String resourceType) {
        List<MetadataColumnCommonDetail> recordColumnDetailsList = new ArrayList<>();
        for (MetadataColumnCommon column : columns) {
            MetadataColumnCommonDetail recordColumnDetails = new MetadataColumnCommonDetail();
            //字段详情id metadataTaskRecordId_tableId_columnId
            String recordColumnDetailsId = MD5Util.columnToMD5(metadataTaskRecordId, column.getMetadataTableCommonId(), column.getId(), "");
            recordColumnDetails.setId(recordColumnDetailsId);
            recordColumnDetails.setMetadataTableCommonId(column.getMetadataTableCommonId());
            recordColumnDetails.setMetadataTaskRecordId(metadataTaskRecordId);
            recordColumnDetails.setColumnComment(column.getColumnComment());
            recordColumnDetails.setColumnName(column.getColumnComment());
            recordColumnDetails.setColumnLength(column.getColumnLength());
            recordColumnDetails.setColumnScale(column.getColumnScale());
            recordColumnDetails.setColumnDataType(column.getColumnDataType());
            recordColumnDetails.setPos(column.getPos());
            recordColumnDetails.setColumnType(column.getColumnType());
            //详情id metadataTaskRecordId_tableId
            String uuid = MD5Util.tableIdToMD5(metadataTaskRecordId, column.getMetadataTableCommonId(), "");
            recordColumnDetails.setMetadataTableCommonDetailId(uuid);
            recordColumnDetails.setColumnForeignKey(column.getColumnForeignKey());
            recordColumnDetails.setColumnPrimaryKey(column.getColumnPrimaryKey());
            recordColumnDetails.setSqlType(column.getSqlType());
            recordColumnDetails.setColumnName(column.getColumnName());
            recordColumnDetails.setColumnNull(column.getColumnNull());
            recordColumnDetails.setDbName(dbName);
            recordColumnDetails.setSchemaName(schemaName);
            recordColumnDetails.setOpt(option);
            recordColumnDetails.setResourceType(resourceType);
            recordColumnDetails.setTableName(column.getTableName());
            recordColumnDetailsList.add(recordColumnDetails);

        }
        return recordColumnDetailsList;
    }

    /**
     * 获取表字段元数据
     */
    public List<MetadataColumnCommon> getColumns(MetadataTableCommon metadataTable, DatasourceDTO datasourceDTO) throws AppErrorException {
        List<MetadataColumnCommon> columnProInsert = new ArrayList<>();
        if (Objects.isNull(datasourceDTO)) {
            datasourceDTO = getDatasourceDTO(metadataTable.getDatasourceInfoId());
        }
        //获取字段信息
        try {
            //表类型是表或者视图
            if (MetadataTaskObjectEnum.TABLE.getType().equalsIgnoreCase(metadataTable.getType()) ||
                    MetadataTaskObjectEnum.VIEW.getType().equalsIgnoreCase(metadataTable.getType())) {
                datasourceDTO.setDbName(metadataTable.getDbName());
                datasourceDTO.setTableName(metadataTable.getName());
                datasourceDTO.setType(metadataTable.getType());
                String schemaName = metadataTable.getSchemaName();
                if (StringUtils.isEmpty(schemaName)) {
                    schemaName = datasourceDTO.getDbName();
                }
                datasourceDTO.setSchema(schemaName);
                List<JSONObject> columns = DatasourceUtils.tableColumn(datasourceDTO, false);
                for (JSONObject column : columns) {
                    MetadataColumnCommon columnDTO = new MetadataColumnCommon();
                    String uuid = MD5Util.columnToMD5(datasourceDTO.getDbName(), metadataTable.getId(), column.getString("key"), metadataTable.getDatasourceInfoId());
                    columnDTO.setId(uuid);
                    columnDTO.setMetadataTableCommonId(metadataTable.getId());
                    columnDTO.setColumnName(column.getString("key"));
                    String type = column.getString("type");
                    columnDTO.setColumnType(type);
                    Integer dataType = column.getInteger("dataType");
                    columnDTO.setSqlType(dataType == null ? "" : String.valueOf(dataType));
                    String comment = column.getString("comment");
                    if ("null".equalsIgnoreCase(comment)) {
                        comment = "";
                    }
                    columnDTO.setColumnComment(comment);
                    columnDTO.setColumnCnName(comment);
                    Integer precision = column.getInteger("precision");
                    columnDTO.setColumnLength(precision == null ? "" : String.valueOf(precision));
                    //获取数据精度
                    Integer scale = column.getInteger("scale");
                    if (Objects.nonNull(scale)) {
                        columnDTO.setColumnScale(scale);
                    }
                    Integer columnSort = column.getInteger("columnSort");
                    columnDTO.setPos(columnSort);
                    //是否是主键
                    Boolean pkFlag = column.getBoolean("pkflag");
                    if (pkFlag) {
                        columnDTO.setColumnPrimaryKey(0);
                    } else {
                        columnDTO.setColumnPrimaryKey(1);
                    }
                    //是否是外键
                    Boolean fkFlag = column.getBoolean("fkflag");
                    if (fkFlag) {
                        columnDTO.setColumnForeignKey(0);
                    } else {
                        columnDTO.setColumnForeignKey(1);
                    }
                    //是否为空
                    Boolean notNullFlag = column.getBoolean("notNullFlag");
                    if (notNullFlag) {
                        columnDTO.setColumnNull(0);
                    } else {
                        columnDTO.setColumnNull(1);
                    }
                    //获取数据类型
                    String dateType = column.getString("dateType");
                    columnDTO.setColumnDataType(dateType);
                    columnDTO.setResourceType(metadataTable.getType());
                    columnDTO.setTableName(metadataTable.getName());

                    columnProInsert.add(columnDTO);
                }
                return columnProInsert;
            }
            //索引
            List<ColumnMetaDTO> indexColumn = new ArrayList<>();
            if (MetadataTaskObjectEnum.INDEX.getType().equalsIgnoreCase(metadataTable.getType())) {
                datasourceDTO.setDbName(metadataTable.getDbName());
                datasourceDTO.setSchema(metadataTable.getSchemaName());
                datasourceDTO.setTableName(metadataTable.getTableName());
                datasourceDTO.setType(metadataTable.getType());
                datasourceDTO.setIndexName(metadataTable.getName());
                indexColumn = DatasourceUtils.getIndexColumn(datasourceDTO);
            }
            //函数或者存储过程
            if (MetadataTaskObjectEnum.FUNCTION.getType().equalsIgnoreCase(metadataTable.getType()) ||
                    MetadataTaskObjectEnum.PROCEDURE.getType().equalsIgnoreCase(metadataTable.getType())) {
                datasourceDTO.setDbName(metadataTable.getDbName());
                datasourceDTO.setSchema(metadataTable.getSchemaName());
                datasourceDTO.setTableName(metadataTable.getTableName());
                datasourceDTO.setType(metadataTable.getType());
                datasourceDTO.setObjectName(metadataTable.getName());
                indexColumn = DatasourceUtils.getFunctionArguments(datasourceDTO);
            }
            for (ColumnMetaDTO column : indexColumn) {
                MetadataColumnCommon columnDTO = new MetadataColumnCommon();
                String uuid = MD5Util.columnToMD5(datasourceDTO.getDbName(), metadataTable.getId(), column.getKey(), metadataTable.getDatasourceInfoId());
                columnDTO.setId(uuid);
                columnDTO.setMetadataTableCommonId(metadataTable.getId());
                columnDTO.setColumnName(column.getKey());
                columnDTO.setColumnType(column.getType());
                columnDTO.setColumnComment(column.getComment());
                columnDTO.setColumnCnName(column.getComment());
                columnDTO.setResourceType(metadataTable.getType());
                //是否为空
                Boolean notNullFlag = column.getNotNullFlag();
                if (notNullFlag) {
                    columnDTO.setColumnNull(0);
                } else {
                    columnDTO.setColumnNull(1);
                }
                columnDTO.setPos(column.getColumnOrder());
                columnDTO.setCollation(column.getCollation());
                columnDTO.setInOut(column.getInOut());
                Integer length = column.getLength();
                if (Objects.nonNull(length)) {
                    columnDTO.setColumnLength(length.toString());
                }
                //获取数据精度
                Integer scale = column.getScale();
                if (Objects.nonNull(scale)) {
                    columnDTO.setColumnScale(scale);
                }
                Integer precision = column.getPrecision();
                if (Objects.nonNull(precision)) {
                    columnDTO.setColumnLength(precision.toString());
                }
                columnDTO.setTableName(metadataTable.getName());
                columnProInsert.add(columnDTO);
            }
        } catch (Exception e) {
            log.error("数据源[{}],资源名[{}],获取表字段元数据失败{}", datasourceDTO.getDataName(), metadataTable.getName(), e.getMessage());
            throw new AppErrorException("数据源获取表字段元数据失败：" + e.getMessage());
        } finally {
            datasourceDTO.setTableName(null);
            datasourceDTO.setType(null);
            datasourceDTO.setSchema(null);
            datasourceDTO.setDbName(null);
        }
        return columnProInsert;
    }

    //根据表id和采集记录id修改表详情状态为失败
    public void updateMetadataTaskRecordTableDetails(String tableId, String metadataTaskRecordId, Integer status, String log, String opt, Integer colCount, String comment) {
        MetadataTableCommonDetail recordTableDetails = new MetadataTableCommonDetail();
        recordTableDetails.setMetadataTableCommonId(tableId);
        recordTableDetails.setStatus(status);
        recordTableDetails.setMetadataTaskRecordId(metadataTaskRecordId);
        recordTableDetails.setErrorLog(log);
        recordTableDetails.setComment(comment);
        recordTableDetails.setCnName(comment);
        recordTableDetails.setOpt(opt);
        //colCount 不为null时，更新colCount
        if (Objects.nonNull(colCount)) {
            recordTableDetails.setMetadataColumnTotal(colCount);
        }
        EqCondition update = new EqCondition("metadataTaskRecordId", metadataTaskRecordId);
        EqCondition metadataTableId = new EqCondition("metadataTableCommonId", tableId);
        List<WhereCondition> conditions = new ArrayList<>();
        conditions.add(update);
        conditions.add(metadataTableId);
        metadataTableCommonDetailService.updateBy(conditions, recordTableDetails, true);
    }


    //4.计算应该删除的表 及采集过得表在源库中已不存在的集合
    public List<MetadataTableCommon> getTablesToDelete(List<MetadataTableCommon> matchedTables, List<MetadataTableCommon> collectedTables) throws AppErrorException {

        List<MetadataTableCommon> newList = new ArrayList<>();
        if (CollUtil.isEmpty(collectedTables)) {
            return newList;
        }
        if (CollUtil.isEmpty(matchedTables)) {
            return newList;
        }
        //获取matchedTables集合在collectedTables中不存在得表，生成新集合
        for (MetadataTableCommon table : matchedTables) {
            if (!collectedTables.contains(table)) {
                table.setDelFlag(true);
                newList.add(table);
            }
        }

        return newList;
    }

    //5.计算已经采集得表集合，并获取新得集合
    public List<MetadataTableCommon> getTablesNotExists(List<MetadataTableCommon> matchedTables, List<MetadataTableCommon> collectedTables) throws AppErrorException {
        //获取collectedTables集合在matchedTables中不存在得表，以表id 判断生成新集合
        List<MetadataTableCommon> tablesToDelete = collectedTables.stream()
                .filter(table -> !matchedTables.contains(table))  // 过滤出已采集的表
                .collect(Collectors.toList());
        return tablesToDelete;
    }

    //5.计算已经采集得表集合，并获取新得集合
    public List<MetadataTableCommon> getTablesNotExistsByTableChoose(List<MetadataTableCommon> matchedTables, List<MetadataTableCommon> collectedTables) throws AppErrorException {
        //获取collectedTables集合在matchedTables中不存在得表，以表id 判断生成新集合
        List<MetadataTableCommon> tablesToDelete = collectedTables.stream()
                .filter(table -> matchedTables.contains(table))  // 过滤出已采集的表
                .collect(Collectors.toList());
        return tablesToDelete;
    }


    /**
     * 删除的集合表
     *
     * @throws AppErrorException
     */
    public int metadataTableToDeleteList(DatasourceDTO datasourceDTO, GatherMetadataDTO gatherMetadataDTO, MetadataTask task) throws AppErrorException {
        //获取删除的表
        if (CollUtil.isEmpty(isTablesToDelete)) {
            log.info("数据源[{}],此次采集删除资源数量[{}]", datasourceDTO.getDataName(), isTablesToDelete.size());
            return 0;
        }
        //此次采集表配置的集合 在获取所有表中中已不存在的集合
        //isTablesToDelete 在mamatchedTables 中不存在的集合
        //isTablesToDelete 在平台中采集过的表
        //matchedTables  此次采集的所有资源
        List<MetadataTableCommon> tablesToDelete = isTablesToDelete;
        //tablesToDelete 不为空时 提取id为新的list集合
        if (CollUtil.isNotEmpty(tablesToDelete)) {
            List<String> ids = tablesToDelete.stream().map(MetadataTableCommon::getId).collect(Collectors.toList());
            //逻辑删除
            metadataTableCommonService.batchUpdate(tablesToDelete);
            //todo  删除表记录
            List<MetadataTableCommonDetail> metadataTableCommonDetail = getMetadataTableCommonDetail(tablesToDelete, OperationType.DELETE.name());
            //获取删除表记录中每个类型得数量
            metadataTableCommonDetail.forEach(metadataTableCommonDetail1 -> {
                switch (metadataTableCommonDetail1.getType()) {
                    case "TABLE":
                        gatherMetadataDTO.setDeleteTableCount(gatherMetadataDTO.getDeleteTableCount() + 1);
                        break;
                    case "VIEW":
                        gatherMetadataDTO.setDeleteViewCount(gatherMetadataDTO.getDeleteViewCount() + 1);
                        break;
                    case "PROCEDURE":
                        gatherMetadataDTO.setDeleteProcedureCount(gatherMetadataDTO.getDeleteProcedureCount() + 1);
                        break;
                    //函数
                    case "FUNCTION":
                        gatherMetadataDTO.setDeleteFunctionCount(gatherMetadataDTO.getDeleteFunctionCount() + 1);
                        break;
                    //索引
                    case "INDEX":
                        gatherMetadataDTO.setDeleteIndexCount(gatherMetadataDTO.getDeleteIndexCount() + 1);
                }
            });
            addInBatchesMetadataTableCommonDetailS(metadataTableCommonDetail, 1000);
            //删除字段
            //1.先获取字段
            List<MetadataColumnCommon> colsByTableIds = metadataColumnCommonService.getColsByTableIds(ids);
            //删除字段
            if (CollUtil.isNotEmpty(colsByTableIds)) {
                metadataColumnCommonService.deleteByTableId(colsByTableIds);
                //todo  字段删除记录
                MetadataTableCommonDetail metadataTableCommonDetail1 = metadataTableCommonDetail.get(0);
                List<MetadataColumnCommonDetail> metadataColumnCommonDetails = getMetadataTaskRecordColumnDetails(colsByTableIds, metadataTableCommonDetail1.getDbName(), metadataTableCommonDetail1.getSchemaName(), OperationType.DELETE.name(), metadataTableCommonDetail1.getType());
                metadataColumnCommonDetailService.add(metadataColumnCommonDetails);
                //字段删除数据量
                gatherMetadataDTO.setDeleteColumnCount(metadataColumnCommonDetails.size());
            }
        }
        log.info("数据源[{}],此次采集删除资源数量[{}]", datasourceDTO.getDataName(), tablesToDelete.size());
        //todo  表变更告警信息
        //检查是否发送告警
        boolean b = checkAlarm(datasourceDTO.getDatasourceId(), AlertStatusEnum.B402005);
        if (b) {
            //发送消息
            StringBuilder changeInfo = new StringBuilder();
            //删除表，将表名拼接成字符串
            String metadataColu = tablesToDelete.stream().map(MetadataTableCommon::getName).collect(Collectors.joining(","));
            changeInfo.append("删除资源：").append(metadataColu).append("，");
            sendAlarm(datasourceDTO, AlertStatusEnum.B402005, task, null, changeInfo.toString());
        }
        return tablesToDelete.size();
    }


    /**
     * 构建采集记录表详情列表
     *
     * @param detailsList
     * @param batchSize
     * @throws AppErrorException
     */
    @Transactional(rollbackFor = AppErrorException.class)
    public void addInBatchesMetadataTableCommonDetailS(List<MetadataTableCommonDetail> detailsList, int batchSize) throws AppErrorException {
        int size = detailsList.size();
        for (int i = 0; i < size; i += batchSize) {
            int end = Math.min(i + batchSize, size);
            List<MetadataTableCommonDetail> batch = detailsList.subList(i, end);
            metadataTableCommonDetailService.add(batch);
        }
    }


    /**
     * 构建元数据采集记录表
     *
     * @param tables
     * @return
     */
    public List<MetadataTableCommonDetail> getMetadataTableCommonDetail(List<MetadataTableCommon> tables, String opt) {
        List<MetadataTableCommonDetail> recordTableDetailsList = new ArrayList<>();
        for (MetadataTableCommon table : tables) {
            MetadataTableCommonDetail recordTableDetails = new MetadataTableCommonDetail();
            recordTableDetails.setMetadataTaskRecordId(metadataTaskRecordId);
            recordTableDetails.setDatasourceInfoId(table.getDatasourceInfoId());
            recordTableDetails.setComment(table.getComment());
            recordTableDetails.setCnName(table.getComment());
            recordTableDetails.setType(table.getType());
            if (OperationType.DELETE.name().equals(opt)) {
                recordTableDetails.setStatus(MetadataCollectionStatusEnum.SUCCESS.getCode());
            } else {
                recordTableDetails.setStatus(MetadataCollectionStatusEnum.NOT_STARTED.getCode());
            }
            recordTableDetails.setName(table.getName());
            recordTableDetails.setDatasourceType(table.getDatasourceType());
            recordTableDetails.setDbName(table.getDbName());
            recordTableDetails.setSchemaName(table.getSchemaName());
            recordTableDetails.setTableName(table.getTableName());
            recordTableDetails.setMetadataTableCommonId(table.getId());
            recordTableDetails.setIndexTableId(table.getIndexTableId());
            recordTableDetails.setDatasourceType(table.getDatasourceType());
            //详情id metadataTaskRecordId_tableId
            String uuid = MD5Util.tableIdToMD5(metadataTaskRecordId, table.getId(), "");
            recordTableDetails.setId(uuid);
            recordTableDetails.setMetadataColumnTotal(table.getMetadataColumnTotal());
            recordTableDetails.setOpt(opt);
            recordTableDetails.setDdl(table.getDdl());
            recordTableDetails.setManager(table.getManager());
            recordTableDetails.setCollation(table.getCollation());
            recordTableDetails.setCharset(table.getCharset());
            recordTableDetails.setIndexType(table.getIndexType());
            recordTableDetails.setUnique(table.getUnique());
            recordTableDetailsList.add(recordTableDetails);
        }
        return recordTableDetailsList;
    }

    /**
     * 获取此次采集的最终资源
     */
    // 假设这是获取表和视图的代码，保持和之前示例一致
    public List<MetadataTableCommon> getTablesAndViews(DatasourceDTO datasourceDTO, String database, MetadataTask task, GatherMetadataDTO gatherMetadataCount) throws AppErrorException {
        List<MetadataTableCommon> tables = new ArrayList<>();
        // 检查线程是否被要求停止
        if (!taskDTO.isRunning()) {
            return tables;
        }

        // 获取表
        List<MetadataTableCommon> tableList = getTables(task, database, datasourceDTO, MetadataTaskObjectEnum.TABLE.getType(), gatherMetadataCount);
        tables.addAll(tableList);
        if (!taskDTO.isRunning()) {
            return tables;
        }
        // 获取视图
        List<MetadataTableCommon> viewList = getTables(task, database, datasourceDTO, MetadataTaskObjectEnum.VIEW.getType(), gatherMetadataCount);
        tables.addAll(viewList);
        if (!taskDTO.isRunning()) {
            return tables;
        }
        //获取索引
        List<MetadataTableCommon> indexList = getTables(task, database, datasourceDTO, MetadataTaskObjectEnum.INDEX.getType(), gatherMetadataCount);
        tables.addAll(indexList);

        if (!taskDTO.isRunning()) {
            return tables;
        }
        //获取函数
        List<MetadataTableCommon> functionList = getTables(task, database, datasourceDTO, MetadataTaskObjectEnum.FUNCTION.getType(), gatherMetadataCount);
        tables.addAll(functionList);
        if (!taskDTO.isRunning()) {
            return tables;
        }
        //获取存储过程
        List<MetadataTableCommon> procedureList = getTables(task, database, datasourceDTO, MetadataTaskObjectEnum.PROCEDURE.getType(), gatherMetadataCount);
        tables.addAll(procedureList);

        return tables;
    }

    /**
     * 获取表ddl、字符集、排序规则、注释
     */
    public List<MetadataTableCommon> getTablesProps(List<MetadataTableCommon> tablesToCollectByTableOrColumnFilter,
                                                    String database, String type, DatasourceDTO datasourceDTO, boolean isCollectRecord) throws AppErrorException {

        //获取表ddl 表或者视图ddl
        if (MetadataTaskObjectEnum.TABLE.getType().equals(type) || MetadataTaskObjectEnum.VIEW.getType().equals(type)) {
            for (MetadataTableCommon tableCommon : tablesToCollectByTableOrColumnFilter) {
                //获取注释
                String tableComment = getTableComment(database, datasourceDTO, tableCommon);
                tableCommon.setComment(tableComment);
                tableCommon.setCnName(tableComment);
                //获取ddl
                String ddl = getTableDdl(database, datasourceDTO, tableCommon);
                tableCommon.setDdl(ddl);
                //获取字符集
                String charset = getTableCharset(database, datasourceDTO, tableCommon);
                tableCommon.setCharset(charset);
                //获取排序字符集
                String tableCollation = getTableCollation(database, datasourceDTO, tableCommon);
                tableCommon.setCollation(tableCollation);
            }
        }
        if (MetadataTaskObjectEnum.FUNCTION.getType().equals(type) || MetadataTaskObjectEnum.PROCEDURE.getType().equals(type)) {
            for (MetadataTableCommon tableCommon : tablesToCollectByTableOrColumnFilter) {
                //获取ddl
                String ddl = getTableDdl(database, datasourceDTO, tableCommon);
                tableCommon.setDdl(ddl);
            }
        }

        return tablesToCollectByTableOrColumnFilter;
    }

    /**
     * 获取此次采集的表
     */
    public List<MetadataTableCommon> getTables(MetadataTask task, String database, DatasourceDTO datasourceDTO, String type, GatherMetadataDTO gatherMetadataCount) throws AppErrorException {
        //获取到最终要采集的表
        List<MetadataTableCommon> metadataTables = new ArrayList<>();
        //获取所有要采集的表

        //先获取采集对象
        String taskObject = task.getTaskObject();
        String[] taskObjects = new String[]{};
        if (StringUtils.isNotEmpty(taskObject)) {
            //采集对象可能存在多个 先将逗号分割，在与type 匹配
            taskObjects = taskObject.split(",");
        }
        //采集库不存在得表
        if (CollUtil.isEmpty(tablesToCollect)) {
            tablesToCollect = getTablesToCollect(task, 0, false);
        }
        String start = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        log.info("开始----将要采集的库写入采集记录[{}] 获取：[{}] 资源的时间：[{}]", database, type, start);

        //1 选择库
        if (MetadataCollectionRangeEnum.DATABASE_SELECTION.getCode() == task.getAcquisitionScope() && Arrays.asList(taskObjects).contains(type)) {
            //获取所有表
            List<MetadataTableCommon> dbTableToMetadataTables = getDbTableToMetadataTables(task, database, type, datasourceDTO);
            //是否配置黑白名单
            //判断选择范围过滤白名单：白名单
            metadataTables = applyTableFilterWhiteList(dbTableToMetadataTables, task);

            //todo  处理表级增量或者字段级增量
            List<MetadataTableCommon> tablesToCollectByTableOrColumnFilter = getTablesToCollectByTableOrColumnFilter(metadataTables, task, datasourceDTO, database, type);
            //获取表ddl 表或者视图ddl
            List<MetadataTableCommon> tablesProps = getTablesProps(tablesToCollectByTableOrColumnFilter, database, type, datasourceDTO, false);


            //todo 获取应该删除得表
            //平台配置要采集的表：tablesProps
            //采集库不存在得表
            if (CollUtil.isEmpty(tablesToCollect)) {
                tablesToCollect = getTablesToCollect(task, 0, false);
            }
            List<MetadataTableCommon> tablesNotExists = getTablesNotExists(tablesProps, tablesToCollect);
            //以dbname.schemaname为key 生成map
            Map<String, List<MetadataTableCommon>> tablesNotExistsMap = tablesNotExists.stream()
                    .collect(Collectors.groupingBy(d -> d.getDbName() + "." +
                            (d.getSchemaName() != null ? d.getSchemaName() : "")));
            MetadataTableCommon metadataTableCommon = tablesProps.get(0);
            String dbName = metadataTableCommon.getDbName();
            String schemaName = metadataTableCommon.getSchemaName();
            if (StringUtils.isEmpty(schemaName)) {
                schemaName = "";
            }
            if (tablesNotExistsMap.containsKey(dbName + "." + schemaName)) {
                //删除库下得表
                List<MetadataTableCommon> metadataTableCommons = tablesNotExistsMap.get(dbName + "." + schemaName);
                if (CollUtil.isNotEmpty(metadataTableCommons)) {
                    isTablesToDelete.addAll(metadataTableCommons);
                }
            }
            gatherMetadataCount.setConfigTotal(gatherMetadataCount.getConfigTotal() + tablesProps.size());
            return tablesProps;
        }

        //2判断选择范围过滤选择范围：选择库表
        if (MetadataCollectionRangeEnum.TABLE_SELECTION.getCode() == task.getAcquisitionScope()) {
            List<MetadataTableCommon> metadataTables1 = applyTableFilterSelect(task, type, database, datasourceDTO);
            if (CollUtil.isEmpty(metadataTables1)) {
                return metadataTables;
            }
            gatherMetadataCount.setConfigTotal(gatherMetadataCount.getConfigTotal() + metadataTables1.size());

//            //获取所有表
            List<MetadataTableCommon> dbTableToMetadataTables = getDbTableToMetadataTables(task, database, type, datasourceDTO);
//            if (CollUtil.isEmpty(dbTableToMetadataTables)) {
//                return metadataTables;
//            }
            //获取metadataTables1 的dbname、 schemaName、 name、 type 在medataDataTables这个集合中存在的集合,最后以dbTableToMetadataTables这个里面得数据为准
//            从dbTableToMetadataTables 集合中获取出metadataTables1这数组得数据，生成新得集合
            metadataTables = dbTableToMetadataTables.stream()
                    .filter(dbTable -> metadataTables1.stream()
                            .anyMatch(metaTable -> metaTable.equals(dbTable)))
                    .collect(Collectors.toList());
//            metadataTables = metadataTables1;
            //3.与我们存放采集元数据的库 比较是全量采集还是增量采集
            //todo  处理表级增量或者字段级增量
            List<MetadataTableCommon> tablesToCollectByTableOrColumnFilter = getTablesToCollectByTableOrColumnFilter(metadataTables, task, datasourceDTO, database, type);
            //获取表ddl 表或者视图ddl
            List<MetadataTableCommon> tablesProps = getTablesProps(tablesToCollectByTableOrColumnFilter, database, type, datasourceDTO, false);
            //todo 获取应该删除得表
            //先获取配置中得表 哪些在源库中已经没有了
            List<MetadataTableCommon> tablesNotExists = getTablesNotExists(metadataTables, metadataTables1);
            if (CollUtil.isEmpty(tablesNotExists)) {
                return tablesProps;
            }
            //采集库不存在得表
            if (CollUtil.isEmpty(tablesToCollect)) {
                tablesToCollect = getTablesToCollect(task, 0, false);
            }
            //在判断这个不存在得表是否采集过
            List<MetadataTableCommon> tablesNotExists1 = getTablesNotExistsByTableChoose(tablesNotExists, tablesToCollect);
            if (CollUtil.isNotEmpty(tablesNotExists1)) {
                isTablesToDelete.addAll(tablesNotExists1);
            }
            return tablesProps;
        }
        //2 todo 处理快速选表的逻辑
        if (MetadataCollectionRangeEnum.QUICK_SELECT_TABLE.getCode() == task.getAcquisitionScope() && Arrays.asList(taskObjects).contains(type)) {
            //选择表
            String selectTable = task.getSelectTable();
            //若为空直接返回
            if (StringUtils.isEmpty(selectTable)) {
                return metadataTables;
            }
            //获取所有表
            List<MetadataTableCommon> dbTableToMetadataTables = getDbTableToMetadataTables(task, database, type, datasourceDTO);
            if (CollUtil.isEmpty(dbTableToMetadataTables)) {
                return metadataTables;
            }
            //将采集资源放进去
            datasourceMatchedTables.addAll(dbTableToMetadataTables);
            //获取库名和database一样的表
            //平台选中配置表
            List<MetadataTableCommon> metadataTables1 = applyTableFilterSelectTable(datasourceDTO, task, database, type);
            //从dbTableToMetadataTables 集合中获取出metadataTables1这数组得数据，生成新得集合
            metadataTables = dbTableToMetadataTables.stream()
                    .filter(dbTable -> metadataTables1.stream()
                            .anyMatch(metaTable -> metaTable.equals(dbTable)))
                    .collect(Collectors.toList());
            //todo  处理表级增量或者字段级增量
            List<MetadataTableCommon> tablesToCollectByTableOrColumnFilter = getTablesToCollectByTableOrColumnFilter(metadataTables, task, datasourceDTO, database, type);
            //获取表ddl 表或者视图ddl
            List<MetadataTableCommon> tablesProps = getTablesProps(tablesToCollectByTableOrColumnFilter, database, type, datasourceDTO, false);
            //todo 获取应该删除得表
            List<MetadataTableCommon> tablesNotExists = getTablesNotExists(metadataTables, metadataTables1);
            if (CollUtil.isEmpty(tablesNotExists)) {
                return tablesProps;
            }
            //采集库不存在得表
            if (CollUtil.isEmpty(tablesToCollect)) {
                tablesToCollect = getTablesToCollect(task, 0, false);
            }
            //在判断这个不存在得表是否采集过
            List<MetadataTableCommon> tablesNotExists1 = getTablesNotExistsByTableChoose(tablesNotExists, tablesToCollect);
            if (CollUtil.isNotEmpty(tablesNotExists1)) {
                isTablesToDelete.addAll(tablesNotExists1);
            }


            return tablesProps;
        }
        //3判断选择范围过滤选择范围：选择全部：黑白名单 且采集对象选择表
        if (MetadataCollectionRangeEnum.BLACK_WHITE_LIST.getCode() == task.getAcquisitionScope() && Arrays.asList(taskObjects).contains(type)) {
            //获取所有表
            List<MetadataTableCommon> dbTableToMetadataTables = getDbTableToMetadataTables(task, database, type, datasourceDTO);
            if (CollUtil.isEmpty(dbTableToMetadataTables)) {
                return metadataTables;
            }
            metadataTables.addAll(dbTableToMetadataTables);
            //todo先获取该数据库下所有表
            //判断选择范围过滤白名单：白名单
            metadataTables = applyTableFilterWhiteList(dbTableToMetadataTables, task);
        }
        //3.与我们存放采集元数据的库 比较是全量采集还是增量采集
        //todo  处理表级增量或者字段级增量
        List<MetadataTableCommon> tablesToCollectByTableOrColumnFilter = getTablesToCollectByTableOrColumnFilter(metadataTables, task, datasourceDTO, database, type);
        //获取表ddl 表或者视图ddl
        List<MetadataTableCommon> tablesProps = getTablesProps(tablesToCollectByTableOrColumnFilter, database, type, datasourceDTO, false);

        return tablesProps;
    }

    /**
     * 获取表或者视图 ddl
     */
    public String getTableDdl(String database, DatasourceDTO datasourceDTO, MetadataTableCommon tableCommon) {
        try {
            datasourceDTO.setDbName(tableCommon.getDbName());
            //若schema 为空填充dbName
            if (StringUtils.isEmpty(tableCommon.getSchemaName())) {
                datasourceDTO.setSchema(database);
            } else {
                datasourceDTO.setSchema(tableCommon.getSchemaName());
            }
            datasourceDTO.setTableName(tableCommon.getName());
            datasourceDTO.setType(tableCommon.getType());
            String ddl = DatasourceUtils.getDDL(datasourceDTO);
            return ddl;
        } catch (Exception e) {
            log.error("获取表或者视图{} ddl 失败：{}", tableCommon.getName(), e.getMessage());
            return "";
        } finally {
            datasourceDTO.setTableName(null);
            datasourceDTO.setSchema(null);
            datasourceDTO.setDbName(null);

        }

    }

    /**
     * 获取表或者视图的字符集
     */
    public String getTableCharset(String database, DatasourceDTO datasourceDTO, MetadataTableCommon tableCommon) {
        try {
            datasourceDTO.setDbName(tableCommon.getDbName());
            //若schema 为空填充dbName
            if (StringUtils.isEmpty(tableCommon.getSchemaName())) {
                datasourceDTO.setSchema(database);
            } else {
                datasourceDTO.setSchema(tableCommon.getSchemaName());
            }
            datasourceDTO.setTableName(tableCommon.getName());

            String charset = DatasourceUtils.getCharacterSet(datasourceDTO);
            return charset;
        } catch (Exception e) {
            log.error("获取表或者视图{} 字符集失败：{}", tableCommon.getName(), e.getMessage());
            return "";
        } finally {
            datasourceDTO.setTableName(null);
            datasourceDTO.setSchema(null);
            datasourceDTO.setDbName(null);

        }
    }

    /**
     * 获取表或者视图的排序字符集
     */
    public String getTableCollation(String database, DatasourceDTO datasourceDTO, MetadataTableCommon tableCommon) {
        try {
            datasourceDTO.setDbName(tableCommon.getDbName());
            //若schema 为空填充dbName
            if (StringUtils.isEmpty(tableCommon.getSchemaName())) {
                datasourceDTO.setSchema(database);
            } else {
                datasourceDTO.setSchema(tableCommon.getSchemaName());
            }
            datasourceDTO.setTableName(tableCommon.getName());
            String characterCollation = DatasourceUtils.getCharacterCollation(datasourceDTO);
            return characterCollation;
        } catch (Exception e) {
            log.error("获取表或者视图{} 排序字符集失败：{}", tableCommon.getName(), e.getMessage());
            return "";
        } finally {
            datasourceDTO.setTableName(null);
            datasourceDTO.setSchema(null);
            datasourceDTO.setDbName(null);
        }
    }

    /**
     * 获取表注释
     */
    public String getTableComment(String database, DatasourceDTO datasourceDTO, MetadataTableCommon tableCommon) {
        try {
            datasourceDTO.setDbName(tableCommon.getDbName());
            //若schema 为空填充dbName
            if (StringUtils.isEmpty(tableCommon.getSchemaName())) {
                datasourceDTO.setSchema(database);
            } else {
                datasourceDTO.setSchema(tableCommon.getSchemaName());
            }
            datasourceDTO.setTableName(tableCommon.getName());
            String tableMetaComment = DatasourceUtils.getTableMetaComment(datasourceDTO);
            return tableMetaComment;
        } catch (Exception e) {
            log.error("获取表或者视图{} 注释失败：{}", tableCommon.getName(), e.getMessage());
            return "";
        } finally {
            datasourceDTO.setTableName(null);
            datasourceDTO.setSchema(null);
            datasourceDTO.setDbName(null);
        }
    }

    /**
     * 白名单和黑名单过滤逻辑
     *
     * @param metadataTables
     * @param task
     * @return
     */
    public List<MetadataTableCommon> applyTableFilterWhiteList(List<MetadataTableCommon> metadataTables, MetadataTask task) {

        // 白名单和黑名单过滤逻辑
        List<MetadataTableCommon> filteredTables = new ArrayList<>();
        for (MetadataTableCommon table : metadataTables) {
            if (matchesWhitelist(table, task)) {
                if (!matchesBlacklist(table, task)) {
                    filteredTables.add(table);  // 先满足白名单，再检查是否命中黑名单
                }
            }
        }
        return filteredTables;
    }

    /**
     * 白名单
     *
     * @param table
     * @param task
     * @return
     */
    public boolean matchesWhitelist(MetadataTableCommon table, MetadataTask task) {

        String whitelist = task.getWhitelist();
        //判断白名单是否为空：为空则返回全采集
        if (StringUtils.isEmpty(whitelist)) {
            return true;
        }
        String[] whitelists = whitelist.split(",");
        // 验证正则表达式
        String tableName = table.getDbName() + "." + table.getName();
        if (StringUtils.isNotEmpty(table.getSchemaName())) {
            tableName = table.getDbName() + "." + table.getSchemaName() + "." + tableName;
        }
        for (String patternStr : whitelists) {
            Pattern pattern = Pattern.compile(patternStr);
            if (pattern.matcher(tableName).matches()) {
                return true;
            }
        }

        // 验证精确匹配
        for (String exactMatch : whitelists) {
            if (tableName.equals(exactMatch)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 黑名单
     *
     * @param table
     * @param task
     * @return
     */
    public boolean matchesBlacklist(MetadataTableCommon table, MetadataTask task) {

        String blacklist = task.getBlacklist();
        //判断黑名单是否为空：为空则未命中黑名单
        if (StringUtils.isEmpty(blacklist)) {
            return false;
        }
        // 验证正则表达式：表示命中黑名单
        String[] blacklists = blacklist.split(",");
        String tableName = table.getDbName() + "." + table.getName();
        if (StringUtils.isNotEmpty(table.getSchemaName())) {
            tableName = table.getDbName() + "." + table.getSchemaName() + "." + tableName;
        }
        for (String patternStr : blacklists) {
            Pattern pattern = Pattern.compile(patternStr);
            if (pattern.matcher(tableName).matches()) {
                return true;
            }
        }

        // 验证精确匹配
        for (String exactMatch : blacklists) {
            if (tableName.equals(exactMatch)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取快速选表的表过滤：根据database 过滤
     *
     * @param
     * @param task
     * @return
     * @throws AppErrorException
     */
    public List<MetadataTableCommon> applyTableFilterSelectTable(DatasourceDTO datasourceDTO, MetadataTask task, String database, String type) {
        List<MetadataTableCommon> metadataTables = new ArrayList<>();
        String selectTable = task.getSelectTable();
        if (StringUtils.isEmpty(selectTable)) {
            return metadataTables;
        }
        //是否是模式得数据源
        if (DatasourceTypeDatabase.schemaDataTypeList().contains(datasourceDTO.getDataType().toLowerCase())) {
            //以逗号分隔
            String[] selectTables = selectTable.split(",");
            //0 库名 1模式 2表名
            for (String selectTable1 : selectTables) {
                //以.分隔获取dbName、 name
                String[] dbNameAndName = selectTable1.split("\\.");
                MetadataTableCommon metadataTable = new MetadataTableCommon();
                //dbName和database 匹配上则构建表对象
                String schema = dbNameAndName[0] + "." + dbNameAndName[1];
                if (!dbNameAndName[0].equalsIgnoreCase(schema)) {
                    continue;
                }
                //获取name
                String name = dbNameAndName[2];
                String tableName = datasourceDTO.getTableName();
                if (StringUtils.isEmpty(tableName)) {
                    tableName = "";
                }
                metadataTable.setId(MD5Util.resourceToMD5(task.getDatasourceInfoId(), dbNameAndName[0], dbNameAndName[1], name, type, tableName));
                metadataTable.setName(name);
                metadataTable.setDatasourceInfoId(task.getDatasourceInfoId());
                metadataTable.setDatasourceType(datasourceDTO.getDataType());
                metadataTable.setType(type);
                metadataTable.setDbName(dbNameAndName[0]);
                metadataTable.setSchemaName(dbNameAndName[1]);
                metadataTable.setTableName(tableName);
                metadataTables.add(metadataTable);
            }
        } else {
            //是不含 模式得处理逻辑
            //以逗号分隔
            String[] selectTables = selectTable.split(",");
            //循环遍历获取表对象
            for (String selectTable1 : selectTables) {
                //以.分隔获取dbName、 name
                String[] dbNameAndName = selectTable1.split("\\.");
                MetadataTableCommon metadataTable = new MetadataTableCommon();
                //dbName和database 匹配上则构建表对象
                if (!dbNameAndName[0].equalsIgnoreCase(database)) {
                    continue;
                }
                String tableName = datasourceDTO.getTableName();
                if (StringUtils.isEmpty(tableName)) {
                    tableName = "";
                }
                //获取name
                String name = dbNameAndName[1];
                metadataTable.setId(MD5Util.resourceToMD5(task.getDatasourceInfoId(), database, "", name, type, tableName));
                metadataTable.setName(name);
                metadataTable.setDatasourceInfoId(task.getDatasourceInfoId());
                metadataTable.setDatasourceType(datasourceDTO.getDataType());
                metadataTable.setType(type);
                metadataTable.setDbName(database);
                metadataTable.setTableName(tableName);
                metadataTables.add(metadataTable);
            }
        }

        return metadataTables;
    }


    /**
     * 处理表级增量或者字段级增量：过滤采集表的方法
     *
     * @param matchedTables
     * @param task
     * @return
     * @throws AppErrorException
     */
    public List<MetadataTableCommon> getTablesToCollectByTableOrColumnFilter(List<MetadataTableCommon> matchedTables, MetadataTask task,
                                                                             DatasourceDTO datasourceDTO, String database, String type) throws AppErrorException {
        List<MetadataTableCommon> collectedTables = new ArrayList<>();
        if (CollUtil.isEmpty(matchedTables)) {
            return collectedTables;
        }
        //先获取新增表
        if (CollUtil.isEmpty(tablesToCollect)) {
            tablesToCollect = getTablesToCollect(task, 0, false);
        }
        //全量则直接返回
        if (MetadataTaskWayEnum.ALL.getCode() == task.getTaskWay()) {
            collectedTables = matchedTables;
        }
        //获取表级增量
        if (MetadataTaskWayEnum.TABLE_INCREMENTAL.getCode() == task.getTaskWay()) {
            //先获取采集过得表
            //先判断tablesToCollect是否为空，为空则获取
            //计算最终应采集的表
            collectedTables = getTablesToCollect(matchedTables, tablesToCollect);
        }
        //字段级增量
        if (MetadataTaskWayEnum.FIELD_INCREMENTAL.getCode() == task.getTaskWay()) {
            List<MetadataTableCommon> tablesToCollect1 = getTablesToCollect(matchedTables, tablesToCollect);
            if (CollUtil.isNotEmpty(tablesToCollect1)) {
                collectedTables.addAll(tablesToCollect1);
            }
            //获取字段不同的表
            List<MetadataTableCommon> tablesToCollect2 = getTablesToCollectByColumnFilter(matchedTables, task, datasourceDTO);
            if (CollUtil.isNotEmpty(tablesToCollect2)) {
                collectedTables.addAll(tablesToCollect2);
            }
        }
        //获取当前时间以 2025-04-21 19:07:56
        String end = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        log.info("结束---将要采集的库[{}]获取[{}]资源的时间：[{}]", database, type, end);
//        //先获取注释
//        List<MetadataTableCommon> tablesProps = getTablesProps(collectedTables, database, type, datasourceDTO, false);

        //todo 采集要配置的表 存记录
        //将要采集的表保存到数据库，表详情列表中:一个库保存一次
        List<MetadataTableCommonDetail> metadataTaskRecordTableDetails = getMetadataTableCommonDetail(collectedTables, "");
        //metadataTaskRecordTableDetails 1000条数据保存一次
        if (!metadataTaskRecordTableDetails.isEmpty()) {
            int batchSize = 1000;
            addInBatchesMetadataTableCommonDetailS(metadataTaskRecordTableDetails, batchSize);
        }
        log.info("将要采集的库{}所有表记录到采集表详情中，记录数量{}", database, collectedTables.size());
        String end1 = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        log.info("采集记录结束-----将要采集的库写入采集记录[{}] 获取：[{}] 资源的时间：[{}]", database, type, end1);

        return collectedTables;
    }

    /**
     * 字段变更的表：字段新增或者删除
     *
     * @param matchedTables
     * @param task
     * @return
     * @throws AppErrorException
     */
    //获取metadataTables1 的dbname、 schemaName、 name、 type相同且metadataColumnTotal不同的表 在medataDataTables这个集合中存在的集合
    public List<MetadataTableCommon> getTablesToCollectByColumnFilter(List<MetadataTableCommon> matchedTables, MetadataTask task, DatasourceDTO datasourceDTO) throws AppErrorException {
        List<MetadataTableCommon> tablesToCollectNew = new ArrayList<>();
        if (CollUtil.isEmpty(tablesToCollect)) {
            tablesToCollect = getTablesToCollect(task, 0, false);
        }
        Map<String, MetadataTableCommon> tablesToCollectMap = new HashMap<>();
        if (CollUtil.isNotEmpty(tablesToCollect)) {
            //将tablesToCollect 转为以id 为key，MetadataTableCommon 为value的map
            tablesToCollectMap = tablesToCollect.stream().collect(Collectors.toMap(MetadataTableCommon::getId, table -> table));

        }
        //获取matchedTables 的dbname、 schemaName、 name、 type相同且metadataColumnTotal不同的表 tablesToCollect这个集合中存在的集合
        //获取字段
        for (MetadataTableCommon matchedTable : matchedTables) {

            //如果此表没有采集过直接采集
            if (!tablesToCollectMap.containsKey(matchedTable.getId())) {
                tablesToCollectNew.add(matchedTable);
                continue;
            }
            //采集过才比较字段的变化
            try {
                List<MetadataColumnCommon> columns = getColumns(matchedTable, datasourceDTO);
                if (CollUtil.isNotEmpty(columns)) {
                    matchedTable.setMetadataColumnTotal(columns.size());
                    metadataColumnCommonsMap.put(matchedTable.getId(), columns);
                    //获取平台以采集的表字段
                    List<MetadataColumnCommon> tablesByTableId = metadataColumnCommonService.getTablesByTableId(matchedTable.getId());
                    qfMetadataColumnCommonsMap.put(matchedTable.getId(), tablesByTableId);
                    matchedTable.setMetadataColumnTotal(columns.size());
                    //todo 比较字段是否发生变化 包含个数 和字段名
                    //1.比较字段个数
                    if (columns.size() != tablesByTableId.size()) {
                        tablesToCollectNew.add(matchedTable);
                        continue;
                    }
                    //将columns id 为key，MetadataColumnCommon 为value的map
                    Map<String, MetadataColumnCommon> columnsMap = columns.stream().collect(Collectors.toMap(MetadataColumnCommon::getId, column -> column));
                    //2.字段个数相同时，比较字段名是否相同
                    for (MetadataColumnCommon column : columns) {
                        //字段是否存在,不存在则采集
                        if (!columnsMap.containsKey(column.getId())) {
                            tablesToCollectNew.add(matchedTable);
                            break;
                        }
                        //字段存在
                        //比较字段名 、字段类型、字段长度、字段精度、字段是否为空、字段备注、是否主键、排序规则、inOut 生成一个key 判断是否一致
                        if (columnsMap.containsKey(column.getId())) {
                            MetadataColumnCommon column1 = columnsMap.get(column.getId());
                            //比较字段名 、字段类型、字段长度、字段精度、字段是否为空、字段备注、是否主键、排序规则、inOut 生成一个key 判断是否一致
                            String key = generateUniqueKey(column);
                            String key1 = generateUniqueKey(column1);
                            if (!key.equals(key1)) {
                                tablesToCollectNew.add(matchedTable);
                                break;
                            }
                        }
                    }
                } else {
                    metadataColumnCommonsMap.put(matchedTable.getId(), new ArrayList<>());
                }

            } catch (Exception e) {
                log.error("获取字段异常{}", e.getMessage());
            }

        }

        return tablesToCollectNew;

    }

    /**
     * 比较字段名、字段类型、字段长度、字段精度、字段是否为空、字段备注、是否主键、排序规则、inOut 生成唯一键
     */
    public String generateUniqueKey(MetadataColumnCommon common) {
        return common.getColumnName() + ":" + common.getColumnType() + ":" + common.getColumnLength() + ":" + common.getColumnScale() + ":"
                + common.getColumnNull() + ":" + common.getColumnComment() + ":" + common.getColumnPrimaryKey() + ":" + common.getColumnForeignKey() + ":"
                + common.getCollation() + ":" + common.getInOut();
    }


    /**
     * 计算全量时增量采集的表
     *
     * @param
     * @return
     * @throws AppErrorException
     */
    public List<MetadataTableCommon> getTablesToCollect(@NotNull List<MetadataTableCommon> matchedTables, List<MetadataTableCommon> collectedTables) throws AppErrorException {
        Set<MetadataTableCommon> collectedSet = new HashSet<>(collectedTables);
        return matchedTables.stream()
                .filter(table -> !collectedSet.contains(table))  // 过滤出未采集的表
                .collect(Collectors.toList());
    }

    /**
     * 获取已经采集过的表 或者以删除的表
     *
     * @param task
     * @return
     * @throws AppErrorException
     */
    public List<MetadataTableCommon> getTablesToCollect(MetadataTask task, Integer code, boolean isDelFlag) throws AppErrorException {
        // 模拟从采集记录表中获取已采集过的表
        //根据数据源id  查询
        String datasourceInfoId = task.getDatasourceInfoId();
        List<MetadataTableCommon> metadataTables = metadataTableCommonService.getTablesByDatasourceinfoId(datasourceInfoId, isDelFlag);
        if (metadataTables == null) {
            return new ArrayList<>();
        }
        //采集前  还是采集后
        if (!isDelFlag) {
            if (MetadataCollectionStatusEnum.NOT_STARTED.getCode() == code) {
                tablesToCollect = metadataTables;
            }
        }
        return metadataTables;
    }


    /**
     * 库表选择--采集的表
     *
     * @param task MetadataTask对象，包含任务的配置信息，包括要筛选的表格信息
     * @return 返回一个包含根据任务配置筛选后的元数据表列表
     */
    public List<MetadataTableCommon> applyTableFilterSelect(MetadataTask task, String type, String database, DatasourceDTO datasourceDTO) {
        // 选择表过滤
        List<MetadataTableCommon> filteredTables = new ArrayList<>();
        List<MetadataSelect> metadataSelectTables = task.getMetadataSelect();
        if (!metadataSelectTables.isEmpty()) {
            for (MetadataSelect metadataSelectTable : metadataSelectTables) {
                String databaseAndSchema = metadataSelectTable.getDbName();
                if (database.contains(".")) {
                    databaseAndSchema = metadataSelectTable.getDbName() + "." + metadataSelectTable.getSchemaName();
                }
                if (type.equalsIgnoreCase(metadataSelectTable.getType()) && database.equalsIgnoreCase(databaseAndSchema)) {
                    //将这个对象转换为MetadataTable这个对象返回
                    MetadataTableCommon metadataTable = new MetadataTableCommon();
                    BeanUtil.copyProperties(metadataSelectTable, metadataTable);
                    //表uuid
                    String schemaName = metadataTable.getSchemaName();
                    if (StringUtils.isEmpty(schemaName)) {
                        schemaName = "";
                    }
                    String tableName = metadataTable.getTableName();
                    if (StringUtils.isEmpty(tableName)) {
                        tableName = "";
                    }
                    String uuid = MD5Util.resourceToMD5(task.getDatasourceInfoId(), metadataTable.getDbName(), schemaName, metadataTable.getName(), type, tableName);
                    metadataTable.setId(uuid);
                    metadataTable.setManager(datasourceDTO.getManager());
                    //如果dbTableVO.getTableName() 不为空，则按规则生成tableId
                    if (StringUtils.isNotEmpty(metadataSelectTable.getTableName())) {
                        String indexTableId = MD5Util.resourceToMD5(task.getDatasourceInfoId(), metadataTable.getDbName(), schemaName, metadataSelectTable.getTableName(), MetadataTaskObjectEnum.TABLE.getType(), "");
                        metadataTable.setIndexTableId(indexTableId);
                    }
                    metadataTable.setIndexType(metadataSelectTable.getIndexType());
                    metadataTable.setUnique(metadataSelectTable.getUnique());
                    filteredTables.add(metadataTable);
                }
            }
        }
        return filteredTables;
    }


    /**
     * 获取所有表
     *
     * @param task
     * @param database
     * @param type
     * @return
     * @throws AppErrorException
     */
    public List<MetadataTableCommon> getDbTableToMetadataTables(MetadataTask task, String database, String type, DatasourceDTO datasourceDTO) throws AppErrorException {

        List<MetadataTableCommon> metadataTables = new ArrayList<>();
        //根据数据源id获取数据源参数对象、TODO
        if (Objects.isNull(datasourceDTO)) {
            //todo  数据源对象
            datasourceDTO = getDatasourceDTO(task.getDatasourceInfoId());
        }
        if (database.contains(".")) {
            String[] split = database.split("\\.");
            datasourceDTO.setDbName(split[0]);
            datasourceDTO.setSchema(split[1]);
        } else {
            datasourceDTO.setDbName(database);
        }
        datasourceDTO.setType(type);
        List<DbTableVO> dbTableVOS = new ArrayList<>();
        if (MetadataTaskObjectEnum.TABLE.getType().equalsIgnoreCase(type) || MetadataTaskObjectEnum.VIEW.getType().equalsIgnoreCase(type)) {
            dbTableVOS = DatasourceUtils.getMedataDataTables(datasourceDTO);
        }
        //索引
        if (MetadataTaskObjectEnum.INDEX.getType().equalsIgnoreCase(type)) {
            dbTableVOS = DatasourceUtils.getIndexList(datasourceDTO);
        }
        //函数或者存储过程
        if (MetadataTaskObjectEnum.FUNCTION.getType().equalsIgnoreCase(type) || MetadataTaskObjectEnum.PROCEDURE.getType().equalsIgnoreCase(type)) {
            dbTableVOS = DatasourceUtils.getFunctionList(datasourceDTO);
        }

        if (CollUtil.isNotEmpty(dbTableVOS)) {
            for (DbTableVO dbTableVO : dbTableVOS) {
                MetadataTableCommon metadataTable = new MetadataTableCommon();
                String schema = datasourceDTO.getSchema();
                if (StringUtils.isEmpty(schema)) {
                    schema = "";
                }
                String tableName = dbTableVO.getTableName();
                if (StringUtils.isEmpty(tableName)) {
                    tableName = "";
                }
                metadataTable.setId(MD5Util.resourceToMD5(task.getDatasourceInfoId(), datasourceDTO.getDbName(), schema, dbTableVO.getName(), type, tableName));
                metadataTable.setComment(dbTableVO.getComment());
                metadataTable.setCnName(dbTableVO.getComment());
                metadataTable.setName(dbTableVO.getName());
                metadataTable.setDatasourceInfoId(task.getDatasourceInfoId());
                metadataTable.setDatasourceType(datasourceDTO.getDataType());
                metadataTable.setType(type);
                metadataTable.setManager(datasourceDTO.getManager());
                metadataTable.setDbName(datasourceDTO.getDbName());
                metadataTable.setSchemaName(dbTableVO.getSchemaName());
                metadataTable.setMetadataColumnTotal(dbTableVO.getColumnCount());
                metadataTable.setTableName(dbTableVO.getTableName());
                metadataTable.setDdl(dbTableVO.getDdl());
                metadataTable.setIndexType(dbTableVO.getIndexType());
                metadataTable.setUnique(dbTableVO.getUnique());
                //如果dbTableVO.getTableName() 不为空，则按规则生成tableId
                if (StringUtils.isNotEmpty(dbTableVO.getTableName())) {
                    String indexTableId = MD5Util.resourceToMD5(task.getDatasourceInfoId(), datasourceDTO.getDbName(), schema, dbTableVO.getTableName(), MetadataTaskObjectEnum.TABLE.getType(), "");
                    metadataTable.setIndexTableId(indexTableId);
                }
                metadataTables.add(metadataTable);
            }
        }
        datasourceDTO.setType(null);
        datasourceDTO.setDbName(null);
        datasourceDTO.setSchema(null);

        //todo 将资源写入未采集


        return metadataTables;
    }


    /**
     * 根据id更新采集记录对象
     */
    public void updateMetadataTaskRecord(String metadataTaskRecordId,
                                         String log, Integer status,
                                         Integer total,
                                         Integer qfTotal,
                                         Integer delTotal
    ) {
        MetadataTaskRecord metadataTaskRecord = new MetadataTaskRecord();
        metadataTaskRecord.setId(metadataTaskRecordId);
        if (Objects.nonNull(status)) {
            metadataTaskRecord.setStatus(status);
            if (status == MetadataCollectionStatusEnum.SUCCESS.getCode() || status == MetadataCollectionStatusEnum.FAILURE.getCode()) {
                metadataTaskRecord.setEndTime(new Date());
            }
        }
        if (StringUtils.isNotEmpty(log)) {
            metadataTaskRecord.setErrorLog(log);
        }
        if (Objects.nonNull(total)) {
            metadataTaskRecord.setRecordTotal(total);
        }
        if (Objects.nonNull(qfTotal)) {
            metadataTaskRecord.setQfRecordTotal(qfTotal);
        }
        if (Objects.nonNull(delTotal)) {
            metadataTaskRecord.setDeleteRecordTotal(delTotal);
        }
        metadataTaskRecord.setLastModificationTime(new Date());
        metadataTaskRecordService.update(metadataTaskRecordId, metadataTaskRecord);
    }


    /**
     * 从采集配置中获取需要采集的库
     *
     * @param task
     * @return
     */
    public List<String> applyDbFilterSelect(MetadataTask task, DatasourceDTO datasourceDTO) {
        // 选择库过滤
        List<String> dbs = new ArrayList<>();

        //库选择
        if (MetadataCollectionRangeEnum.DATABASE_SELECTION.getCode() == task.getAcquisitionScope()) {
            List<String> dbsList = new ArrayList<>();
            List<MetadataSelect> metadataSelectTables = task.getMetadataSelect();
            if (!metadataSelectTables.isEmpty()) {
                for (MetadataSelect metadataSelectTable : metadataSelectTables) {
                    if (StringUtils.isNotEmpty(metadataSelectTable.getSchemaName())) {
                        String schemaName = metadataSelectTable.getSchemaName();
                        schemaName = metadataSelectTable.getDbName() + "." + schemaName;
                        dbsList.add(schemaName);
                        continue;
                    }
                    dbsList.add(metadataSelectTable.getDbName());
                }
            }
            //匹配黑白名单
            //todo 黑白名单库名选择
            for (String dbName : dbsList) {
                boolean b = matchesDbName(dbName, task);
                if (b) {
                    dbs.add(dbName);
                }
            }
        }
        //库表选择
        if (MetadataCollectionRangeEnum.TABLE_SELECTION.getCode() == task.getAcquisitionScope()) {
            List<MetadataSelect> metadataSelectTables = task.getMetadataSelect();
            if (!metadataSelectTables.isEmpty()) {
                for (MetadataSelect metadataSelectTable : metadataSelectTables) {
                    if (StringUtils.isNotEmpty(metadataSelectTable.getSchemaName())) {
                        String schemaName = metadataSelectTable.getSchemaName();
                        schemaName = metadataSelectTable.getDbName() + "." + schemaName;
                        dbs.add(schemaName);
                        continue;
                    }
                    dbs.add(metadataSelectTable.getDbName());
                }
            }
        }

        //快速选表
        //todo 库名和模式名选择优化
        if (MetadataCollectionRangeEnum.QUICK_SELECT_TABLE.getCode() == task.getAcquisitionScope()) {
            String selectTable = task.getSelectTable();
            List<String> split = StrUtil.split(selectTable, ",");
            //以"."分隔获取dbName
            //如果含有schema 则截取前两个 dbName.schema.tableName
            if (DatasourceTypeDatabase.schemaDataTypeList().contains(datasourceDTO.getType().toLowerCase())) {
                //dbName.schema.tableName 截取前两个
                dbs = split.stream().map(s -> StrUtil.subBefore(s, ".", true)).collect(Collectors.toList());
            } else {
                dbs = split.stream().map(s -> StrUtil.subBefore(s, ".", false)).collect(Collectors.toList());
            }
        }

        //黑白名单
        if (MetadataCollectionRangeEnum.BLACK_WHITE_LIST.getCode() == task.getAcquisitionScope()) {
            //获取白名单中满足得库名
            String whitelist = task.getWhitelist();
            //获取所有库名
            //todo  根据数据源id获取数据源配置
            //判断数据源对象是否为null
            if (Objects.isNull(datasourceDTO)) {
                throw new AppErrorException("数据源对象为空", task.getDatasourceInfoId());
            }
            List<String> allDatabases = DatasourceUtils.getAllSchemas(datasourceDTO);
            if (StringUtils.isEmpty(whitelist)) {
                return allDatabases;
            }
            for (String allDatabase : allDatabases) {
                boolean b = matchesDbName(allDatabase, task);
                if (b) {
                    dbs.add(allDatabase);
                }
            }
        }

        //将dbs 去重
        if (!dbs.isEmpty()) {
            dbs = dbs.stream().distinct().collect(Collectors.toList());
        }
        return dbs;
    }

    /**
     * 验证库名是否匹配白名单规则（支持正则和精确匹配）
     */
    public boolean matchesDbName(String dbName, MetadataTask task) {
        String whitelist = task.getWhitelist();
        if (StringUtils.isEmpty(whitelist)) {
            return true;
        }

        String[] whitelistArr = whitelist.split(",");
        for (String rule : whitelistArr) {
            // 如果是正则表达式（以 ^ 开头）
            if (rule.startsWith("^")) {
                // 尝试提取库名（或库.schema）正则部分
                String patternDbPart = extractDbFromRegex(rule);
                if (patternDbPart != null && Pattern.matches(patternDbPart, dbName)) {
                    return true;
                }
            } else {
                // 精确匹配部分（可能是 db 或 db.schema）
                String exactDbName = rule.contains(".")
                        ? StrUtil.subBefore(rule, ".", false)
                        : rule;
                if (dbName.equals(exactDbName)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 从正则表达式中提取库名匹配部分
     * 例如： ^test\\.aaa.*$ 提取 ^test$，^A\\.dim.*$ 提取 ^A$
     */
    private String extractDbFromRegex(String regex) {
        try {
            // 匹配前缀：^开头，直到第一个非转义的反斜杠（表示库名结束）
            Pattern p = Pattern.compile("^\\^((?:[^\\\\]|\\\\.)*?)\\\\\\.");
            Matcher m = p.matcher(regex);
            if (m.find()) {
                String group = m.group(1);
                return "^" + group + "$";
            }
        } catch (Exception ignored) {
        }
        return null;
    }

    //分dbName统计此次库采集的表数量、视图数量、字段数量，以及表总数、视图总数、字段总数
    @Override
    public List<MetadataTaskRecordResultVO> getResultByTaskRecordId(String id, String opt) {
        //获取记录
        List<MetadataTaskRecordResultVO> metadataTaskRecordResultList = new ArrayList<>();

        //获取此次记录的表详情
        List<MetadataTableCommonDetail> list = metadataTaskRecordService.getService(MetadataTableCommonDetail.class)
                .getQuery()
                .eq("metadataTaskRecordId", id)
                .eq("opt", opt)
                .groupSum("dbName")
                .groupSum("schemaName")
                .list();

        //表
        Integer tableTotal = 0;
        //视图
        Integer viewTotal = 0;
        //索引
        Integer indexTotal = 0;
        //函数
        Integer functionTotal = 0;
        //存储过程
        Integer procedureTotal = 0;
        //字段
        Integer columnTotal = 0;

        if (CollUtil.isNotEmpty(list)) {
            for (MetadataTableCommonDetail metadataTableCommonDetail : list) {

                //表
                Integer metadataTableCommonDetails = getMetadataTableCommonDetails(id, opt, metadataTableCommonDetail, MetadataTaskObjectEnum.TABLE.getType());
                tableTotal += metadataTableCommonDetails;
                //视图
                Integer metadataViewCommonDetails = getMetadataTableCommonDetails(id, opt, metadataTableCommonDetail, MetadataTaskObjectEnum.VIEW.getType());
                viewTotal += metadataViewCommonDetails;
                //索引
                Integer metadataIndexCommonDetails = getMetadataTableCommonDetails(id, opt, metadataTableCommonDetail, MetadataTaskObjectEnum.INDEX.getType());
                indexTotal += metadataIndexCommonDetails;
                //函数
                Integer metadataFunctionCommonDetails = getMetadataTableCommonDetails(id, opt, metadataTableCommonDetail, MetadataTaskObjectEnum.FUNCTION.getType());
                functionTotal += metadataFunctionCommonDetails;
                //存储过程
                Integer metadataProcedureCommonDetails = getMetadataTableCommonDetails(id, opt, metadataTableCommonDetail, MetadataTaskObjectEnum.PROCEDURE.getType());
                procedureTotal += metadataProcedureCommonDetails;

                //字段查询数据量
                Integer total = metadataTaskRecordService.getService(MetadataColumnCommonDetail.class)
                        .getQuery()
                        .eq("metadataTaskRecordId", id)
                        .eq("opt", opt)
                        .eq("dbName", metadataTableCommonDetail.getDbName())
                        .eq("schemaName", metadataTableCommonDetail.getSchemaName()).total();
                columnTotal += total;
                MetadataTaskRecordResultVO metadataTaskRecordResultVO = MetadataTaskRecordResultVO.builder()
                        .dbName(metadataTableCommonDetail.getDbName())
                        .schemaName(metadataTableCommonDetail.getSchemaName())
                        .tableCount(metadataTableCommonDetails)
                        .viewCount(metadataViewCommonDetails)
                        .indexCount(metadataIndexCommonDetails)
                        .functionCount(metadataFunctionCommonDetails)
                        .procedureCount(metadataProcedureCommonDetails)
                        .columnCount(total)
                        .build();
                metadataTaskRecordResultList.add(metadataTaskRecordResultVO);
            }
            //合计
            MetadataTaskRecordResultVO metadataTaskRecordResultVO = MetadataTaskRecordResultVO.builder()
                    .dbName("合计")
                    .tableCount(tableTotal)
                    .viewCount(viewTotal)
                    .indexCount(indexTotal)
                    .functionCount(functionTotal)
                    .procedureCount(procedureTotal)
                    .columnCount(columnTotal)
                    .build();
            metadataTaskRecordResultList.add(metadataTaskRecordResultVO);

        } else {
            List<MetadataColumnCommonDetail> metadataColumnCommonDetails = metadataTaskRecordService.getService(MetadataColumnCommonDetail.class).getQuery()
                    .eq("metadataTaskRecordId", id)
                    .eq("opt", opt)
                    .groupSum("dbName")
                    .groupSum("schemaName")
                    .list();
            if (CollUtil.isNotEmpty(metadataColumnCommonDetails)) {
                //统计字段
                for (MetadataColumnCommonDetail metadataColumnCommonDetail : metadataColumnCommonDetails) {
                    Integer total = metadataTaskRecordService.getService(MetadataColumnCommonDetail.class)
                            .getQuery()
                            .eq("metadataTaskRecordId", id)
                            .eq("opt", opt)
                            .eq("dbName", metadataColumnCommonDetail.getDbName())
                            .eq("schemaName", metadataColumnCommonDetail.getSchemaName()).total();
                    columnTotal += total;
                    MetadataTaskRecordResultVO metadataTaskRecordResultVO = MetadataTaskRecordResultVO.builder()
                            .dbName(metadataColumnCommonDetail.getDbName())
                            .schemaName(metadataColumnCommonDetail.getSchemaName())
                            .tableCount(tableTotal)
                            .viewCount(viewTotal)
                            .indexCount(indexTotal)
                            .functionCount(functionTotal)
                            .procedureCount(procedureTotal)
                            .columnCount(columnTotal)
                            .build();
                    metadataTaskRecordResultList.add(metadataTaskRecordResultVO);
                }

            }


            //合计
            MetadataTaskRecordResultVO metadataTaskRecordResultVO1 = MetadataTaskRecordResultVO.builder()
                    .dbName("合计")
                    .tableCount(tableTotal)
                    .viewCount(viewTotal)
                    .indexCount(indexTotal)
                    .functionCount(functionTotal)
                    .procedureCount(procedureTotal)
                    .columnCount(columnTotal)
                    .build();
            metadataTaskRecordResultList.add(metadataTaskRecordResultVO1);

        }

        return metadataTaskRecordResultList;
    }

    private Integer getMetadataTableCommonDetails(String id, String opt, MetadataTableCommonDetail metadataTableCommonDetail, String type) {
        //根据dbName和schemaName查询
        Integer total = metadataTaskRecordService.getService(MetadataTableCommonDetail.class)
                .getQuery()
                .eq("metadataTaskRecordId", id)
                .eq("opt", opt)
                .eq("type", type)
                .eq("dbName", metadataTableCommonDetail.getDbName())
                .eq("schemaName", metadataTableCommonDetail.getSchemaName()).total();
        return total;
    }

}
