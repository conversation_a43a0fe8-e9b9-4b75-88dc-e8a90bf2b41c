package com.joyadata.dsc.constants;

public final class Constants {
    // 私有构造函数，防止实例化
    private Constants() {
        throw new AssertionError("Cannot instantiate Constants class");
    }

    // 定义常量
    public static final String JDBC_URL = "jdbcUrl";
    public static final String PORT = "port";
    public static final String HOST = "host";
    public static final String DB_NAME = "dbName";
    // 数据源类型 是全部的 id
    public static final String ALL_DATATYPE_ID = "999999";
    // 树的父节点 id
    public static final String TREE_PID_ID = "000000";
    // 未归属 业务系统的 id
    public static final String UNOWNED_BUSINESS_ID = "99999999999999";

    /**
     * 执行计划sql模板
     */
    public static final String EXECUTION_PLAN_SQL_TEMPLATE = "EXPLAIN  %s";

    /**
     * 前置sql 模板
     */
    public static final String PRE_SQL_TEMPLATE = "USE %s";

    /**
     * pg
     */
    public static final String PRE_SQL_TEMPLATE_PG = "SET search_path TO %s";

    /**
     * oracle
     */
    public static final String PRE_SQL_TEMPLATE_ORACLE = "ALTER SESSION SET CURRENT_SCHEMA=%s";

/**
 * ==========================================================采集任务步骤常量=======================================
 */

    /**
     * 步骤一名称：数据源连接
     */
    public static final String STEP_NAME_CONNECT = "数据源连接";

    /**
     * 步骤一内容：建立与目标数据库的连接
     * 验证连接有效性
     */
    public static final String STEP_CONTENT_CONNECT = "建立与目标数据库的连接，验证连接有效性";
    /**
     * 步骤二名称：资源发现
     */
    public static final String STEP_NAME_DISCOVERY = "资源发现";

    /**
     * 步骤二内容：获取数据库/模式列表
     */
    public static final String STEP_CONTENT_DISCOVERY = "获取数据库/模式列表";

    /**
     * 步骤三名称：资源筛选
     */
    public static final String STEP_NAME_FILTER = "资源筛选";

    /**
     * 步骤三内容：扫描指定范围内的表/视图/索引等对象
     * 按全量/表级/字段级进行筛选
     * 应用过滤条件
     */
    public static final String STEP_CONTENT_FILTER = "扫描指定范围内的表/视图/索引等对象，按全量/表级/字段级进行筛选，应用过滤条件";

    /**
     * 步骤四名称：元数据采集
     */
    public static final String STEP_NAME_COLLECT = "元数据采集";

    /**
     * 步骤四内容：获取表/视图/索引等对象元数据
     * 获取字段元数据
     */
    public static final String STEP_CONTENT_COLLECT = "获取表/视图/索引等对象元数据，获取字段元数据";

    /**
     * 步骤五名称： 结果处理
     */
    public static final String STEP_NAME_RESULT = "结果处理";

    /**
     * 步骤五内容：清理无效/过时数据
     * 更新采集状态记录
     */
    public static final String STEP_CONTENT_RESULT = "清理无效/过时数据，更新采集状态记录";
}
