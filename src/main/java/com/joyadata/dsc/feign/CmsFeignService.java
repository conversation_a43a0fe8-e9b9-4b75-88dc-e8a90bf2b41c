package com.joyadata.dsc.feign;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.joyadata.cms.model.*;
import com.joyadata.exception.AppWarningException;
import com.joyadata.feign.service.HttpRequestFeignService;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.interfaces.IQueryWrapper;
import com.joyadata.model.BaseBean;
import com.joyadata.model.web.Response;
import com.joyadata.pcs.model.MyApp;
import com.joyadata.tms.model.Product;
import com.joyadata.tms.model.Project;
import com.joyadata.ui.model.Icon;
import com.joyadata.util.ThreadLocalUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName CmsFeignService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/16 10:50
 * @Version 1.0
 **/
@Slf4j
@Service
public class CmsFeignService {
    private JoyaFeignService<Product> productJoyaFeignService = FeignFactory.make(Product.class);
    private JoyaFeignService<Project> projectJoyaFeignService = FeignFactory.make(Project.class);
    private JoyaFeignService<ApprovalProcess> processJoyaFeignService = FeignFactory.make(ApprovalProcess.class);
    private JoyaFeignService<Dept> deptJoyaFeignService = FeignFactory.make(Dept.class);
    private JoyaFeignService<User> userJoyaFeignService = FeignFactory.make(User.class);
    private JoyaFeignService<WorkNotice> workNoticeJoyaFeignService = FeignFactory.make(WorkNotice.class);
    private JoyaFeignService<MyApp> myAppJoyaFeignService = FeignFactory.make(MyApp.class);
    private JoyaFeignService<Icon> iconJoyaFeignService = FeignFactory.make(Icon.class);
    private JoyaFeignService<Role> roleJoyaFeignService = FeignFactory.make(Role.class);

    private HttpRequestFeignService httpRequestFeignService;

    public Product getProductByName(String name) {
        return productJoyaFeignService.getQuery().eq("name", name).one();
    }

    public Product getProductByCode(String code) {
        // dispatch 调度中心  assetPortal 资产门户
        return productJoyaFeignService.getQuery().eq("code", code).one();
    }

    public Project getProjectByName(String name) {
        return projectJoyaFeignService.getQuery().eq("name", name).one();
    }

    public Project getProjectById(String id) {
        return projectJoyaFeignService.getQuery().eq("id", id).one();
    }

    public Dept getDeptById(String deptId) {
        return deptJoyaFeignService.getQuery().eq("id", deptId).one();
    }

    public List<Dept> getDeptList(String deptId) {
        return deptJoyaFeignService.getQuery().setin("parentIds", deptId).list();
    }

    public Icon getIconByCode(String code) {
        return iconJoyaFeignService.getQuery().eq("code", code).one();
    }

    public List<Icon> getIconListByCodes(List<String> iconCodes) {
        return iconJoyaFeignService.getQuery().in("code", iconCodes).list();
    }

    public Map<String, String> getIconMapByCodes(List<String> iconCodes) {
        return iconJoyaFeignService.getQuery().in("code", iconCodes).map("code", "imageData");
    }

    public List<ApprovalProcess> getApprovalProcessListByIds(List<String> processIds, String status) {
        return processJoyaFeignService.getQuery().lazys("approvalNodeRecords")
                .in("id", processIds)
                .eq("status", status).list();
    }

    /**
     * 发起审批
     * @param approvalModelId
     * @param busiId
     * @param pageUrl
     * @param body
     * @return
     */
    public ApprovalProcess startApprove(String approvalModelId, String busiId, String pageUrl, JSONObject body) {
        ApprovalProcess approvalProcess = processJoyaFeignService.start(approvalModelId, busiId, pageUrl, body);
        return approvalProcess;
    }

    public ApprovalProcess nextApprove(String approvalProcessId, String approvalModelId, String approvalNodeId, boolean result, JSONObject body) {
        ApprovalProcess approvalProcess = processJoyaFeignService.next(approvalProcessId, approvalModelId, approvalNodeId, result, body);
        return approvalProcess;
    }

    public User getUser(String userId) {
        return userJoyaFeignService.getQuery().withs("deptName", "deptId", "roleId").eq("id", userId).one();
    }


    public void addWorkNotice(WorkNotice workNotice) {
        workNoticeJoyaFeignService.add(workNotice);
    }

    public MyApp getMyAppById(String id) {
        return myAppJoyaFeignService.getById(id);
    }

    public void deleteMyAppById(String myAppId) {
        myAppJoyaFeignService.delete(myAppId);
    }

    // 所有角色
    public List<Role> getAllRoles() {
        //捕获异常时返回空集合
        try {
            return roleJoyaFeignService.getQuery().list();
        } catch (Exception e) {
            log.error("获取所有角色失败:{}", e.getMessage());
            return new ArrayList<>();
        }
    }

    //所有用户
    public List<User> getAllUsers() {
        //捕获异常时返回空集合
        try {
            return userJoyaFeignService.getQuery().list();
        } catch (Exception e) {
            log.error("获取所有用户失败:{}", e.getMessage());
            return new ArrayList<>();
        }
    }

    //所有产品
    public List<Product> getAllProducts() {
        //捕获异常时返回空集合
        try {
            return productJoyaFeignService.getQuery().list();
        } catch (Exception e) {
            log.error("获取所有产品失败:{}", e.getMessage());
            return new ArrayList<>();
        }
    }
    //所有项目
    public List<Project> getAllProjects() {
        //捕获异常时返回空集合//捕获异常时返回空集合
        try {
            return projectJoyaFeignService.getQuery().list();
        } catch (Exception e) {
            log.error("获取所有项目失败:{}", e.getMessage());
            return new ArrayList<>();
        }
    }



    public List<Dept> getDeptList(String deptId, String keywords, String deptIds, Boolean checked, Integer page, Integer pager) {
        IQueryWrapper<Dept> deptIQueryWrapper = getDeptIQueryWrapper(deptId, keywords, checked, deptIds);
        if (null != page && null != pager) {
            deptIQueryWrapper.page(page, pager);
        }

        return deptIQueryWrapper.list();
    }

    public IQueryWrapper<Dept> getDeptIQueryWrapper(String deptId, String keywords, Boolean checked, String deptIds) {
        IQueryWrapper<Dept> deptIQueryWrapper = deptJoyaFeignService.getQuery().sortbyDesc("createTime");
        if (null != checked) {
            if (checked) {
                queryParam(deptIQueryWrapper, "id", deptIds, "IN");
            } else {
                queryParam(deptIQueryWrapper, "id", deptIds, "NOTIN");
            }
        }

        queryParam(deptIQueryWrapper, "name", keywords, "SEARCHBY");
        queryParam(deptIQueryWrapper, "parentIds", deptId, "SETIN");
        return deptIQueryWrapper;
    }

    public List<User> getUserList(String deptId, String keywords, String userIds, Boolean checked, Integer page, Integer pager) {
        IQueryWrapper<User> userIQueryWrapper = buildUserQueryWrapper(deptId, keywords, userIds, checked);
        // 分页处理
        if (page != null && pager != null) {
            userIQueryWrapper.page(page, pager);
        }
        // 查询用户列表
        return userIQueryWrapper.list();
    }

    public IQueryWrapper<User> buildUserQueryWrapper(String deptId, String keywords, String userIds, Boolean checked) {
        IQueryWrapper<User> userIQueryWrapper = userJoyaFeignService.getQuery()
                .withs("deptId", "deptName", "fullDeptName", "fullDeptIds");
        if (null != checked) {
            if (checked) {
                queryParam(userIQueryWrapper, "id", userIds, "IN");
            } else {
                queryParam(userIQueryWrapper, "id", userIds, "NOTIN");
            }
        }
        queryParam(userIQueryWrapper, "fullDeptIds", deptId, "SETIN");
        queryParam(userIQueryWrapper, "username,nickname", keywords, "SEARCHBY");
        return userIQueryWrapper;
    }



    public void queryParam(IQueryWrapper<? extends BaseBean> query, String paramName, String paramValue, String symbol) {
        if (StrUtil.isBlank(paramValue)) {
            return; // 如果参数值为空，直接返回
        }
        switch (symbol) {
            case "EQ":
                query.eq(paramName, paramValue);
                break;
            case "SEARCHBY":
                query.searchby(paramName, paramValue);
                break;
            case "SETIN":
                query.setin(paramName, paramValue);
                break;
            case "LIKE":
                query.like(paramName, paramValue);
                break;
            case "IN":
                List<String> inIds = StrUtil.split(paramValue, ",");
                query.in(paramName, inIds);
                break;
            case "NOTIN":
                List<String> notInIds = StrUtil.split(paramValue, ",");
                query.notIn(paramName, notInIds);
                break;
            default:
                break;
        }
    }
}
