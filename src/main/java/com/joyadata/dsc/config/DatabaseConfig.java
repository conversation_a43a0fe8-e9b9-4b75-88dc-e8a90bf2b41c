package com.joyadata.dsc.config;

import com.dsg.database.datasource.utils.DatasourceUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.joyadata.dsc.properties.AppProperties;
import com.joyadata.dsc.properties.DatabaseProperties;
import com.joyadata.util.ApplicationContextHelp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.*;

/**
 * datasource配置文件
 */
@Slf4j
@Data
@Component("databaseConfig")
public class DatabaseConfig {

    private ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("dsc-pool-%d").build();
    private ExecutorService singleThreadPool = new ThreadPoolExecutor(1, 1,
            10L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(1024), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
    /**
     * datasource插件包路径
     */
    private String dataSourcePluginPath;

    /**
     * kerberos临时目录
     * 结构为 DatabaseCenter_租户code/DatasourceInfoId_数据源id/DatasourceNodeId_数据源节点id
     */
    private String localKerberosPath;
    @Autowired
    private DatabaseProperties databaseProperties;
    @Autowired
    private AppProperties appProperties;
    /**
     * 从配置文件中获取插件路径并设置到当前环境中
     *
     * @throws Exception 异常
     */
    @PostConstruct
    public void init() {
        singleThreadPool.execute(()-> {
            DatasourceUtils.initDataSourcePluginPath(getDataSourcePluginPath());
            log.info("datasource插件路径：[{}]，加载成功", getDataSourcePluginPath());
        });
        singleThreadPool.shutdown();
    }

    public String getDataSourcePluginPath() {
        String runtimeWorkDir = ApplicationContextHelp.getRuntimeWorkDir();
        if ("local".equals(appProperties.getActive())) {
            this.dataSourcePluginPath = databaseProperties.getLocalPluginPath();
            this.localKerberosPath = databaseProperties.getLocalKerberosPath();
        } else {
            this.dataSourcePluginPath = runtimeWorkDir + databaseProperties.getPluginPath();
            this.localKerberosPath = runtimeWorkDir + databaseProperties.getKerberosPath();
        }
        return dataSourcePluginPath;
    }
}

