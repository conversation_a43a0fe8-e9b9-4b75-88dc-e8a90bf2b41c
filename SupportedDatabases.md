# JDBC URL工具包支持的数据库类型

本文档列出了JDBC URL工具包当前支持的所有数据库类型及其配置。

## 支持的数据库列表

| 数据库类型 | 代码 | JDBC前缀 | 默认端口 | 默认数据库 | 是否必需数据库 |
|-----------|------|----------|----------|------------|---------------|
| **MySQL** | 1 | `jdbc:mysql://` | 3306 | mysql | ✓ |
| **Oracle** | 2 | `jdbc:oracle:thin:@//` | 1521 | ORCL | ✓ |
| **SQLServer jTDS** | 3 | `jdbc:jtds:sqlserver://` | 1433 | master | ✗ |
| **PostgreSQL** | 4 | `jdbc:postgresql://` | 5432 | postgres | ✓ |
| **ClickHouse** | 25 | `jdbc:clickhouse://` | 8123 | default | ✓ |
| **TiDB** | 31 | `jdbc:mysql://` | 3306 | mysql | ✓ |
| **SQLServer Microsoft** | 32 | `jdbc:sqlserver://` | 1433 | master | ✗ |
| **DMDB** | 35 | `jdbc:dm://` | 5236 | SYSDBA | ✓ |
| **Greenplum6** | 36 | `jdbc:pivotal:greenplum://` | 5432 | postgres | ✓ |
| **KingBase8** | 40 | `jdbc:kingbase8://` | 54321 | test | ✓ |
| **DB2** | 50 | `jdbc:db2://` | 50000 | DB2 | ✓ |
| **MariaDB** | 63 | `jdbc:mysql://` | 3306 | mysql | ✓ |
| **Greenplum PostgreSQL** | 66 | `jdbc:postgresql://` | 5432 | postgres | ✓ |
| **StarRocks** | 72 | `jdbc:mysql://` | 3306 | mysql | ✓ |
| **GoldenDB** | 73 | `jdbc:goldendb://` | 3306 | mysql | ✓ |
| **ADS** | 15 | `jdbc:mysql://` | 3306 | mysql | ✓ |
| **Sequoiadb MySQL** | 1005 | `jdbc:mysql://` | 3306 | mysql | ✓ |
| **TDSQL MySQL** | 1007 | `jdbc:mysql://` | 3306 | mysql | ✓ |
| **Informix** | 1003 | `jdbc:informix-sqli://` | 9088 | sysmaster | ✓ |
| **GaussDB** | 2000 | `jdbc:postgresql://` | 5432 | postgres | ✓ |
| **GaussDB MySQL** | 2003 | `jdbc:mysql://` | 3306 | mysql | ✓ |
| **DWS MySQL** | 2009 | `jdbc:mysql://` | 3306 | mysql | ✓ |
| **TBase** | 2018 | `jdbc:postgresql://` | 5432 | postgres | ✓ |
| **TDSQL PostgreSQL** | 2019 | `jdbc:postgresql://` | 5432 | postgres | ✓ |

## 使用示例

### 基本URL构建
```java
// MySQL
String mysqlUrl = JdbcUrlBuilderUtil.buildJdbcUrl(1, "localhost", 3306, "mydb");

// DB2  
String db2Url = JdbcUrlBuilderUtil.buildJdbcUrl(50, "db2-server", 50000, "sample");

// ClickHouse
String chUrl = JdbcUrlBuilderUtil.buildJdbcUrl(25, "ch-server", 8123, "default");
```

### 带属性的URL构建
```java
Map<String, String> properties = new HashMap<>();
properties.put("user", "admin");
properties.put("password", "password");

String url = JdbcUrlBuilderUtil.buildJdbcUrl(50, 
    Collections.singletonList(new JdbcUrlBuilderUtil.HostPort("db2-server", 50000)),
    "sample", properties);
```

### 流式API
```java
String url = JdbcUrlBuilder.forDatabase(DatabaseJdbcUrlType.DB2)
    .host("db2-server")
    .port(50000)
    .database("sample")
    .addParameter("user", "db2admin")
    .build();
```

### 特殊处理

#### Informix数据库
Informix需要INFORMIXSERVER参数，建议使用专门的方法：
```java
String informixUrl = JdbcUrlBuilderUtil.buildInformixJdbcUrl(
    "informix-server", 9088, "testdb", "ol_informix1410", properties);
```

#### Oracle数据库
Oracle支持服务名和SID两种格式：
```java
// 使用服务名
String url = JdbcUrlBuilder.forDatabase(DatabaseJdbcUrlType.ORACLE)
    .host("oracle-server")
    .port(1521)
    .serviceName("ORCL")
    .build();

// 使用SID
String url = JdbcUrlBuilder.forDatabase(DatabaseJdbcUrlType.ORACLE)
    .host("oracle-server")
    .port(1521)
    .sid("ORCL")
    .build();
```

## 驱动类参考

| 数据库类型 | 推荐驱动类 |
|-----------|-----------|
| MySQL | `com.mysql.cj.jdbc.Driver` |
| Oracle | `oracle.jdbc.driver.OracleDriver` |
| PostgreSQL | `org.postgresql.Driver` |
| SQL Server | `com.microsoft.sqlserver.jdbc.SQLServerDriver` |
| DB2 | `com.ibm.db2.jcc.DB2Driver` |
| ClickHouse | `ru.yandex.clickhouse.ClickHouseDriver` |
| Informix | `com.informix.jdbc.IfxDriver` |
| Greenplum6 | `com.pivotal.jdbc.GreenplumDriver` |
| KingBase8 | `com.kingbase8.Driver` |
| DMDB | `dm.jdbc.driver.DmDriver` |

## 注意事项

1. **默认数据库**: 当没有指定数据库名时，工具会使用默认数据库名
2. **端口处理**: 当没有指定端口时，会使用各数据库的默认端口
3. **特殊格式**: Informix和Oracle有特殊的URL格式要求
4. **驱动依赖**: 使用时需要确保相应的JDBC驱动包在classpath中
5. **URL验证**: 工具提供URL格式验证和数据库类型自动检测功能

## 扩展性

工具包设计具有良好的扩展性，添加新数据库类型需要：
1. 在`DatabaseJdbcUrlType`枚举中添加新类型
2. 在`JdbcUrlParser`中添加解析逻辑
3. 在`JdbcUrlValidator`中添加验证逻辑
4. 在`JdbcUrlBuilder`中添加构建逻辑
5. 更新相关的工具方法 